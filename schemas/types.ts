export enum E_PROMOTION_STATUS {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export enum CATEGORY_STATUS {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export enum INCENTIVE_STATUS {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export enum E_PROMOTION_VALUE_TYPE {
  MONEY = "MONEY",
  PERCENTAGE = "PERCENTAGE",
}

export enum E_COMBO_VOUCHER_TARGET_USER {
  BOTH = "BOTH",
  NEW = "NEW",
  CURRENT = "CURRENT",
}

export enum COMBO_VOUCHER_FROM_PARTNER {
  SYSTEM = "SYSTEM",
}

export enum CAMPAIGN_TYPE {
  PROMOTION = "PROMOTION",
  INFO = "INFO",
  LINK = "LINK",
}

export enum CAMPAIGN_OPTION_NAVIGATE {
  NAVIGATE_TO = "NAVIGATE_TO",
  URL = "URL",
}

export enum CAMPAIGN_TARGET_OPTIONS {
  BOTH = "BOTH",
  NEW = "NEW",
  CURRENT = "CURRENT",
}

export enum TYPE_OF_TARGET_USER_CAMPAIGN {
  TARGET = "TARGET",
  USERS = "USERS",
}

export enum TYPE_OF_REDIRECT {
  NAVIGATE_TO = "navigateTo",
  URL = "url",
}

export type TPromotionValueType = keyof typeof E_PROMOTION_VALUE_TYPE;
export type TStatus = keyof typeof E_PROMOTION_STATUS;
type TTargetUser = keyof typeof E_COMBO_VOUCHER_TARGET_USER;

export interface Vouchers {
  image: string;
  title: TextLang;
  content: TextLang;
  note: TextLang;
  brandInfo: { image: string; name: string; text: TextLang };
  quantity: number;
  promotion: {
    prefix: string;
    type: TPromotionValueType;
    value: number;
    isChosenExpiredDate: string;
    maxValue?: number;
    numberOfDayDueDate?: number;
    expiredDate?: Date;
  };
  applyFor: {
    cities: string[];
    districts: string[];
    services: string[];
    isAllUsers: boolean;
    isSharePublic: boolean;
    applyForType?: "isAllUsers" | "isSharePublic";
  };
}

export interface ComboVoucher {
  thumbnail: string;
  image: string;
  isTesting?: boolean;
  cost: number;
  price: number;
  title: TextLang;
  content: TextLang;
  termsAndCondition: TextLang;
  startDate: Date;
  endDate: Date;
  vouchers: Vouchers[];
  target: TTargetUser;
  status: string;
  currency?: {
    code: string;
    sign: string;
  };
  isoCode?: string;
}

export interface ComboVoucherFormData {
  rangeDate: {
    from: Date;
    to: Date;
  };
  target: string;
  status: string;
  termsAndCondition: TextLang;
  title: TextLang;
  content: TextLang;
  vouchers: Array<{
    image: File | null | string;
    quantity: number;
    promotion: {
      prefix: string;
      type: TPromotionValueType;
      value: number;
      maxValue?: number;
      isChosenExpiredDate: string;
      numberOfDayDueDate?: number;
      expiredDate?: Date;
    };
    title: TextLang;
    content: TextLang;
    note: TextLang;
    brandInfo: { image: File | null | string };
    applyFor: {
      cities: Array<OptionType>;
      districts: Array<OptionType>;
      serviceIds: Array<OptionType>;
      applyForType: string;
    };
  }>;
  cost: number;
  price: number;
  image: File | null | string;
  thumbnail: File | null | string;
  isTesting?: boolean;
}

interface IncentiveInfosForm {
  _id: OptionType[];
  originalPoint: number;
  point: number;
  image: string;
  title: TextLang;
}
interface IncentiveInfos {
  _id: string;
  originalPoint: number;
  point: number;
  image: string;
  title: TextLang;
}

export interface FlashSaleForm {
  _id: string;
  rangeDate: { from: Date; to: Date };
  status: string;
  description: string;
  isTesting: boolean;
  incentiveInfos: Array<IncentiveInfosForm>;
}

export interface FlashSale {
  startDate: Date;
  endDate: Date;
  status: string;
  description: string;
  isTesting?: boolean;
  incentiveInfos: Array<IncentiveInfos>;
  isoCode?: string;
}

export interface SinglePromotion {
  code: string;
  limit: number;
  value: {
    type: string;
    value: number;
    maxValue?: number;
  };
  taskPlace: {
    city: string[];
    district: string[];
  };
  serviceId?: string;
  typeOfPromotion: string;
  source: string;
  startDate: Date;
  endDate: Date;
  description: string;
  userIds?: string[];
  isFirstBooking?: boolean;
  taskStartDate?: Date;
  taskEndDate?: Date;
  promotionBy?: string;
  minOrderValue?: number;
  isTesting?: boolean;
  updatedAt?: Date;
  createdBy: string;
  numberOfUsed?: number;
  status?: string;
  locked?: boolean;
  paymentMethods?: Array<string>;
  isoCode?: string;
}

export interface SinglePromotionFormData {
  code: string;
  limit: number;
  rangeDate: {
    from: Date;
    to: Date;
  };
  value: {
    type: string;
    value: number;
    maxValue?: number;
  };
  taskPlace: {
    city: string[];
    district: string[];
  };
  serviceId: string;
  target: string;
  typeOfPromotion: string;
  source: string;
  description: string;
  promotionBy: string;
  userIds?: string;
  isFirstBooking?: boolean;
  minOrderValue?: number;
  workingTime?: {
    from: Date;
    to: Date;
  };
  status: string;
  locked?: boolean;
  paymentMethods: Array<OptionType>;
}
export interface SeriesPromotion {
  _id?: string;
  code?: string;
  limit: number;
  value: {
    type: string;
    value: number;
    maxValue?: number;
  };
  taskPlace: {
    city: string[];
    district: string[];
  };
  serviceId?: string;
  typeOfPromotion: string;
  source: string;
  startDate: Date;
  endDate: Date;
  description?: string;
  userIds?: string[];
  isFirstBooking?: boolean;
  taskStartDate?: Date;
  taskEndDate?: Date;
  promotionBy?: string;
  minOrderValue?: number;
  isTesting?: boolean;
  createdBy?: string;
  number: number;
  series?: string;
  prefix: string;
  excludedCharacters?: string[];
  paymentMethods?: string[];
  isoCode?: string;
}

export interface SeriesPromotionFormData {
  limit: number;
  rangeDate: {
    from: Date;
    to: Date;
  };
  value: {
    type: string;
    value: number;
    maxValue?: number;
  };
  taskPlace: {
    city: OptionType[];
    district: OptionType[];
  };
  serviceId: string;
  typeOfPromotion: string;
  source: string;
  description?: string;
  userIds?: string;
  isFirstBooking?: boolean;
  promotionBy: string;
  minOrderValue?: number;
  workingTime?: {
    from: Date;
    to: Date;
  };
  series?: string;
  number: number;
  prefix: string;
  excludedCharacters?: OptionType[];
  paymentMethods?: Array<OptionType>;
}

export interface AskerReferralCampaign {
  image: string;
  startDate: Date;
  endDate: Date;
  status: string;
  description: string;
  isTesting?: boolean;
  inviter: Array<InviteTypeForm>;
  invitee: Array<InviteTypeForm>;
  isoCode?: string;
}

export interface AskerReferralCampaignFormData {
  imageUrlFromServer: string;
  img: File | null | string;
  rangeDate: { from: Date; to: Date };
  status: string;
  description: string;
  isTesting: boolean;
  invitee: Array<{
    _id: OptionType[];
    title: TextLang;
    image: string;
    quantity: number;
  }>;
  inviter: Array<{
    _id: OptionType[];
    title: TextLang;
    image: string;
    quantity: number;
  }>;
}

interface InviteTypeForm {
  _id: string;
  quantity: number;
  image: string;
  title: TextLang;
}

export interface PromotionSource {
  _id: string;
  createdAt: Date;
  name: string;
  description: string;
  status?: string;
  updatedAt?: Date;
  isTesting?: boolean;
  createdBy: string;
}

export interface PromotionSourceFormData {
  name: string;
  isTesting?: boolean;
  description: string;
  createdBy: string;
}

export interface MarketingCampaign {
  _id: string;
  image: string;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  isTesting?: boolean;
  notShowApplyTime?: boolean;
  isSpecialCampaign?: boolean;
  status: string;
  type: string;
  promotion?: {
    code: string;
    serviceId: string;
    serviceText: TextLang;
  };
  applyForUser: { target?: string; userIds?: string[] };
  title?: TextLang;
  description?: TextLang;
  subDescription?: TextLang;
  content?: TextLang;
  subContent?: TextLang;
  primaryButtonConfig?: {
    action?: {
      navigateTo?: string | null;
      url?: string | null;
      params?: { serviceId?: string; screen?: string };
      isRequireLogin?: boolean;
    };
    text?: TextLang;
  };
  secondaryButtonConfig?: {
    action?: {
      navigateTo?: string | null;
      url?: string | null;
      params?: { serviceId?: string; screen?: string };
      isRequireLogin?: boolean;
    };
    text?: TextLang;
  };
  isApplyForAllService?: boolean;
  isoCode?: string;
}

export interface MarketingCampaignFormData {
  _id: string;
  image: File | null | string;
  rangeDate: {
    from: Date;
    to: Date;
  };
  isTesting?: boolean;
  notShowApplyTime?: boolean;
  isSpecialCampaign?: boolean;
  status: string;
  type: string;
  promotion?: {
    code: OptionType[];
    services: Array<{
      _id: string;
      text: TextLang;
      isSubscription?: boolean;
    }>;
  };
  targetUser: string;
  applyForUser?: {
    target?: string;
    userIds?: string;
  };
  title?: TextLang;
  description?: TextLang;
  subDescription?: TextLang;
  content?: TextLang;
  subContent?: TextLang;
  primaryButtonConfig?: {
    action?: {
      typeOfRedirect: string;
      navigateTo?: string | null;
      url?: string | null;
      params: { serviceId?: string; screen?: string };
      isRequireLogin?: boolean;
    };
    text?: TextLang;
  };
  secondaryButtonConfig?: {
    action?: {
      typeOfRedirect: string;
      navigateTo?: string | null;
      url?: string | null;
      params?: { serviceId?: string; screen?: string };
      isRequireLogin?: boolean;
    };
    text?: TextLang;
  };
  isApplyForAllService?: boolean;
}

export interface Category {
  iconForV3: string;
  name: string;
  text: TextLang;
  status: string;
}

export interface CategoryFormData {
  img: File | null | string;
  name: string;
  text: TextLang;
  status: string;
  weight?: number;
}

export interface PartnerDirectoryFormData {
  logo: string | File | null;
  city: Array<OptionType>;
  name: string;
  description: string;
  headquarters: string;
  website: string;
  email: string;
  hotline: string;
  storeAddresses: string;
  socialLinks: {
    facebook: string;
    instagram: string;
    tiktok: string;
    youtube: string;
  };
  applicationLinks: {
    playStore: string;
    appStore: string;
  };
  level: string;
  industry: string;
  contactInformation: {
    contact: string;
    email: string;
    phoneNumber: string;
    position: string;
    note: string;
  };
}

export interface PartnerDirectory {
  _id: string;
  logo: string;
  city?: string[];
  name: string;
  description?: string;
  headquarters?: string;
  website?: string;
  email?: string;
  hotline?: string;
  storeAddresses?: string[];
  socialLinks?: {
    facebook: string;
    instagram: string;
    tiktok: string;
    youtube: string;
  };
  applicationLinks?: {
    playStore: string;
    appStore: string;
  };
  level: string;
  industry: string;
  contactInformation?: {
    contact: string;
    email: string;
    phoneNumber: string;
    position: string;
    note: string;
  };
  createdAt: Date;
  updatedAt?: Date;
  isoCode: string;
}

export interface PartnerRequestFormData {
  source: string;
  status: string;
  note: string;
  companyInformation: {
    name: string;
    industry: string;
    city: Array<OptionType>;
  };
  contactInformation: {
    contact: string;
    email: string;
    phoneNumber: string;
  };
}

export interface PartnerRequest {
  _id: string;
  source: string;
  status: string;
  note?: string;
  companyInformation: {
    name: string;
    industry: string;
    city?: string[];
  };
  contactInformation?: {
    contact: string;
    email: string;
    phoneNumber: string;
  };
  createdAt: Date;
  updatedAt?: Date;
  isoCode: string;
}

export interface BReward {
  image: string;
  title: TextLang;
  content: TextLang;
  note: TextLang;
  type?: string;
  from: string;
  brandInfo: {
    name: string;
    text: TextLang;
    image: string;
  };
  exchange: {
    by: string;
    point: number;
  };
  startDate: Date;
  endDate: Date;
  status: string;
  categoryName: string;
  isTesting?: boolean;
  promotion?: {
    expiredDate?: Date;
    numberOfDayDueDate?: number;
    type?: string;
    maxValue?: number;
    value?: number;
  };
  createdBy: string;
  rankRequire?: number;
  isRedeemOneTime?: boolean;
  isSpecialIncentive?: boolean;
  redeemLink?: string;
  office?: Array<{
    name: string;
    text: TextLang;
  }>;
  codeList?: Array<{
    code: string;
    isUsed: boolean;
    numberOfUsed: number;
  }>;
  codeFromPartner?: {
    code: string;
    limit: number;
    numberOfUsed: number;
  };
  giftInfo?: {
    id?: string;
    campaignCode?: string;
    cat_id?: string;
    gift_id?: string;
    price?: number;
  };
  social?: {
    hotline?: string;
    tiktok?: string;
    youtube?: string;
    email?: string;
    facebook?: string;
    instagram?: string;
    ios?: string;
    android?: string;
    website?: string;
  };
  applyFor?: {
    service?: string[];
    city?: string[];
    district?: string[];
    isAllUsers?: boolean;
    isSharePublic?: boolean;
  };
  isoCode?: string;
}

export interface BRewardFormData {
  image: File | null | string;
  title: TextLang;
  content: TextLang;
  note: TextLang;
  rangeDate: {
    from: Date;
    to: Date;
  };
  categoryName: string;
  rankRequire: string;
  from: string;
  isRedeemOneTime?: boolean;
  isTesting?: boolean;
  exchange: { point: number };
  status: string;
  type: string;
  promotion: {
    isChosenExpiredDate: string;
    numberOfDayDueDate?: number;
    expiredDate?: Date;
    value?: number;
    maxValue?: number;
    type: string;
  };
  brandInfo: {
    name: string;
    text: TextLang;
    image: string;
  };
  selectedPartner?: OptionType[];
  redeemLink?: string;
  social?: {
    website?: string;
    hotline?: string;
    facebook?: string;
    youtube?: string;
    instagram?: string;
    tiktok?: string;
    android?: string;
    ios?: string;
    email?: string;
  };
  office?: Array<{ name: string }>;
  isSpecialIncentive?: boolean;
  codeList?: string;
  applyFor: {
    applyForType: string;
    service: string;
    city?: OptionType[];
    district?: OptionType[];
    isAllUsers?: boolean;
    isSharePublic?: boolean;
  };
  codeType: string;
  codeFromPartner?: {
    code?: string;
    limit?: number;
  };
  giftInfo?: {
    id?: string;
    cat_id?: string;
    gift_id?: string;
    price?: number;
    campaignCode?: string;
  };
}
