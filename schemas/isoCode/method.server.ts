export enum EnumIsoCode {
  VN = "VN",
  TH = "TH",
  ID = "ID",
  MY = "MY",
}

import MY, { MYName } from "./my/index.server";
import ID, { IDName } from "./id/index.server";
import TH, { THName } from "./th/index.server";
import VN, { VNName } from "./vn/index.server";

export type ModelName =
  | "task"
  | "workingPlaces"
  | "flashSale"
  | "incentive"
  | "promotionCode"
  | "settingCountry"
  | "marketingCampaign"
  | "comboVoucher"
  | "referralCampaign";

export function getModels(isoCode: string) {
  if (isoCode === "VN") {
    return VN;
  }
  if (isoCode === "TH") {
    return TH;
  }
  if (isoCode === "ID") {
    return ID;
  }
  if (isoCode === 'MY') {
    return MY;
  }

  throw new Error("Iso code not is VN or TH or ID or MY");
}

// Legacy
export function getFieldNameByIsoCode({
  isoCode,
  fieldName,
}: {
  fieldName: string;
  isoCode: string;
}) {
  if (isoCode === EnumIsoCode.VN || isoCode === EnumIsoCode.MY) return fieldName;
  return `${isoCode}_${fieldName}`;
}

export function getCollectionNameByIsoCode(isoCode: string) {
  if (isoCode === "VN") {
    return VNName;
  }
  if (isoCode === "TH") {
    return THName;
  }
  if (isoCode === "ID") {
    return IDName;
  }
  if (isoCode === "MY") {
    return MYName;
  }

  throw new Error("Iso code not is VN or TH or ID or MY");
}

// Legacy code, move from customer support repo
export async function getFinancialAccountByIsoCode({
  fAccountId,
  isoCode,
}: {
  fAccountId: string;
  isoCode: string;
}): Promise<{ [key: string]: number | Date }> {
  if (!isoCode) {
    throw new Error('IsoCode is not valid');
  }

  const fAccount = await getModels(isoCode).financialAccount
    .findById(fAccountId)
    .lean<FinancialAccount>();

  if (!fAccount) {
    throw new Error('Financial account not found');
  }

  const fMainAccountFieldName = getFieldNameByIsoCode({
    isoCode,
    fieldName: 'FMainAccount',
  });
  const promotionFieldName = getFieldNameByIsoCode({
    isoCode,
    fieldName: 'Promotion',
  });

  return {
    [fMainAccountFieldName]:
      fAccount?.[fMainAccountFieldName as keyof FinancialAccount] || 0,
    [promotionFieldName]:
      fAccount?.[promotionFieldName as keyof FinancialAccount] || 0,
  };
}