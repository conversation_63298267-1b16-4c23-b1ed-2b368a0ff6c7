import { Schema } from "mongoose";
import { mongoClientApp } from "mongo-connection";

export const businessLevelName = "id_businessLevel";

const BusinessLevelSchema = new Schema<BusinessLevel>(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true,
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true }, // User who made the change
        changeDate: { $type: Date, required: true }, // Date of the change
        changes: { $type: String }, // Description of what changed
      },
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
  },
  { typeKey: "$type", collection: businessLevelName }
);

const BusinessLevelModel = mongoClientApp.model(
  businessLevelName,
  BusinessLevelSchema
);
export const businessLevel = BusinessLevelModel;
