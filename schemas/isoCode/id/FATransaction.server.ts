import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const FATransactionName = "id_FATransaction";

const FATransactionSchema = new Schema<FATransaction>(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String },
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date },
  },
  { typeKey: "$type", collection: FATransactionName }
);

const FATransactionModel = mongoClientApp.model(
  "id_FATransaction",
  FATransactionSchema
);

export const FATransaction = FATransactionModel;
