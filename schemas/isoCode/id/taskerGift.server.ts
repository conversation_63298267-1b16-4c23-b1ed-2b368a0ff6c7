import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const taskerGiftName = 'id_taskerGift';

const TaskerGiftSchema = new Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String },
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String },
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String },
        },
      ],
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String },
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String },
  },
  { typeKey: '$type', collection: taskerGiftName },
);

const TaskerGiftModel = mongoClientApp.model('id_taskerGift', TaskerGiftSchema);
export const taskerGift = TaskerGiftModel;
