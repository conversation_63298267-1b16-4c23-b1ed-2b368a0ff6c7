import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const taskerTrainingSubmissionName = 'id_trainingTaskerSubmission';

const TaskerTrainingSubmissionSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String },
          },
          video: {
            url: { $type: String },
            description: { $type: String },
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String },
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean },
                },
              ],
            },
          ],
        },
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date },
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String },
        },
        createdAt: { $type: Date },
        createdBy: { $type: String },
      },
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number },
  },
  { typeKey: '$type', collection: taskerTrainingSubmissionName },
);

const TaskerTrainingSubmissionModel = mongoClientApp.model(
  taskerTrainingSubmissionName,
  TaskerTrainingSubmissionSchema,
);

export const taskerTrainingSubmission = TaskerTrainingSubmissionModel;
