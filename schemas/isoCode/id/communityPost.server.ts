import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const communityPostName = 'id_communityPost';

const CommunityPostSchema = new Schema<CommunityPost>(
  {
    _id: {
      $type: String,
      required: true,
    },
    userId: {
      $type: String,
      required: true,
    },
    content: {
      $type: String,
      required: true,
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String,
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String },
      },
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String },
      },
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number },
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
    support: {
      userId: { $type: String },
      username: { $type: String },
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: Schema.Types.Mixed },
      },
    ],
    isReported: { $type: Boolean },
  },
  { typeKey: '$type', collection: communityPostName },
);

const CommunityPostModel = mongoClientApp.model(
  communityPostName,
  CommunityPostSchema,
);

export const communityPost = CommunityPostModel;
