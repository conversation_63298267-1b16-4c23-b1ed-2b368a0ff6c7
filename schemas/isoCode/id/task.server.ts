import { mongoClientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const taskName = 'id_task';
const TasksSchema = new Schema<Task>(
  {
    _id: {
      $type: String,
      required: true,
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date,
    },
    status: {
      $type: String,
      required: true,
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String },
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String },
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String },
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean },
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema.Types.Mixed },
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date,
        },
      ],
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String },
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: {$type: String},
          value: {$type: Number},
          promotionBy: {$type: String}
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean },
        }
      ]
    },
  },
  { typeKey: '$type', collection: taskName },
);

const TasksModel = mongoClientApp.model('id_tasks', TasksSchema);

export const task = TasksModel;
