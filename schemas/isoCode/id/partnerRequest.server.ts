import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const partnerRequestName = 'id_partnerRequest'

const PartnerRequestSchema = new Schema(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String],
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
    },
    createdAt: {
      $type: Date,
      default: Date.now,
    },
    updatedAt: {
      $type: Date,
    },
    isoCode: { $type: String },
  },
  { typeKey: '$type', collection: partnerRequestName },
);

const PartnerRequestModel = mongoClientApp.model(
  partnerRequestName,
  PartnerRequestSchema,
);
export const partnerRequest = PartnerRequestModel;
