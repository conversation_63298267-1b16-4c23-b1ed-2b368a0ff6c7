import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const taskerIncentiveName = 'id_taskerIncentive';

const TaskerIncentiveSchema = new Schema<TaskerIncentive>(
  {
    _id: {
      $type: String,
      required: true,
    },
    image: {
      $type: String,
      required: true,
    },
    startDate: {
      $type: Date,
      required: true,
    },
    endDate: {
      $type: Date,
      required: true,
    },
    from: {
      $type: String,
      required: true,
    },
    type: {
      $type: String,
      required: true,
    },
    createdAt: {
      $type: Date,
      required: true,
    },
    createdBy: {
      $type: String,
      required: true,
    },
    isoCode: {
      $type: String,
      required: true,
    },
    categoryName: {
      $type: String,
      required: true,
    },
    exchange: {
      by: {
        $type: String,
        required: true,
      },
      point: {
        $type: Number,
        required: true,
      },
    },
    title: {
      $type: Map,
      of: String,
    },
    content: {
      $type: String,
    },
    note: {
      $type: String,
    },
    isSpecialIncentive: {
      $type: Boolean,
    },
    promotion: {
      type: {
        $type: String,
      },
      maxValue: {
        $type: Number,
      },
      value: {
        $type: Number,
      },
      prefix: {
        $type: String,
      },
      limit: {
        $type: Number,
      },
      rankRequire: {
        $type: Number,
      },
    },
    partner: {
      $type: String,
    },
    isTesting: {
      $type: Boolean,
    },
    isRedeemOneTime: {
      $type: Boolean,
    },
    originalPoint: {
      $type: Schema.Types.Mixed,
    },
    status: {
      $type: String,
    },
    codeFromPartner: {
      $type: Number,
    },
    giftInfo: Schema.Types.Mixed,
    social: Schema.Types.Mixed,
    codeList: Schema.Types.Mixed,
    office: Schema.Types.Mixed,
    applyFor: Schema.Types.Mixed,
    brandInfo: Schema.Types.Mixed,
    redeemLink: Schema.Types.Mixed,
  },
  { typeKey: '$type', collection: taskerIncentiveName },
);

const TaskerIncentiveModel = mongoClientApp.model(
  'id_taskerIncentive',
  TaskerIncentiveSchema,
);

export const taskerIncentive = TaskerIncentiveModel;
