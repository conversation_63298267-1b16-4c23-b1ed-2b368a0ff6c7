import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const bEmployeeName = "id_bEmployee";

const BEmployeeSchema = new Schema<BEmployee>(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String },
  },
  { typeKey: "$type", collection: bEmployeeName }
);

const BEmployeeModel = mongoClientApp.model(bEmployeeName, BEmployeeSchema);

export const bEmployee = BEmployeeModel;
