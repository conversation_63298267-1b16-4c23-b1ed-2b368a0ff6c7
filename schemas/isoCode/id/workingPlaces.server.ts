import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const workingPlacesName = 'id_workingPlaces';

const WorkingPlacesSchema = new Schema(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String],
      },
    ],
  },
  { typeKey: '$type', collection: workingPlacesName },
);

const WorkingPlacesModel = mongoClientApp.model(
  'id_workingPlaces',
  WorkingPlacesSchema,
);

export const workingPlaces = WorkingPlacesModel;
