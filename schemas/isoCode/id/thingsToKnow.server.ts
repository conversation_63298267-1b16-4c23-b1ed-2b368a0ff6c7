import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const thingsToKnowName = 'id_thingsToKnow';

const ThingToKnowSchema = new Schema<ThingsToKnow>(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String },
      },
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String },
          },
        ],
      },
    ],
    isTesting: { $type: Boolean },
  },
  { typeKey: '$type', collection: thingsToKnowName },
);

const ThingsToKnowModel = mongoClientApp.model(
  'id_thingsToKnow',
  ThingToKnowSchema,
);

export const thingsToKnow = ThingsToKnowModel;
