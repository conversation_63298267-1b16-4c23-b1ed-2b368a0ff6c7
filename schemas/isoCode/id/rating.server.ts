import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const ratingName = 'id_rating';
const RatingSchema = new Schema<Rating>(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String],
  },
  { typeKey: '$type', collection: ratingName },
);

const RatingModel = mongoClientApp.model(ratingName, RatingSchema);

export const rating = RatingModel;
