import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const comboVoucherName = 'id_comboVoucher';

const ComboVoucherSchema = new Schema<ComboVoucher>(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      },
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      },
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now,
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String },
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String },
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean },
        },
        from: { $type: String, enum: ['SYSTEM'] },
      },
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: undefined },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean },
        },
      ],
      default: undefined,
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean },
  },
  { typeKey: '$type', collection: comboVoucherName },
);

const ComboVoucherModel = mongoClientApp.model(
  'id_comboVoucher',
  ComboVoucherSchema,
);
export const comboVoucher = ComboVoucherModel;
