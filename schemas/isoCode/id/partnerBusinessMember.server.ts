import { Schema } from 'mongoose';
import { mongoClientApp } from 'mongo-connection';

export const businessMemberName = 'id_businessMember'

const BusinessMemberSchema = new Schema(
  {
    _id: { $type: String, required: true }, // Unique identifier
    businessId: { $type: String, required: true }, // Reference to Business
    userId: { $type: String, required: true }, // Reference to User
    levelId: { $type: String }, // Role or level in the business
    phone: { $type: String }, // Phone number
    bPay: { $type: Number }, // Payment or salary (float64)
    status: { 
      $type: String, 
      required: true, 
      enum: ['ACTIVE', 'INACTIVE'], 
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true }, // User who made the change
        changeDate: { $type: Date, required: true }, // Date of the change
        changes: { $type: String }, // Description of what changed
      },
    ],
  },
  { typeKey: '$type', collection: businessMemberName },
);

const BusinessMemberModel = mongoClientApp.model(businessMemberName, BusinessMemberSchema);
export const businessMember = BusinessMemberModel;