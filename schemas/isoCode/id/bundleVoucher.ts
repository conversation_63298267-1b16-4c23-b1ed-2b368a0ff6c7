import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const bundleVoucherName = 'id_bundleVoucher';

const BundleVoucherSchema = new Schema<BundleVoucher>(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ['SECRET_BOX'] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number },
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number },
        },
      ],
      default: undefined,
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true }, // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema.Types.Mixed },
            nextData: { $type: Schema.Types.Mixed },
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema.Types.Mixed }, // Dữ liệu bổ sung khác, hiện tại không có field này
        },
      ],
      default: undefined,
    },
  },
  { typeKey: '$type', collection: bundleVoucherName },
);

const BundleVoucherModel = mongoClientApp.model(
  bundleVoucherName,
  BundleVoucherSchema,
);
export const bundleVoucher = BundleVoucherModel;
