import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const serviceName = 'id_service';

const ServiceSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    icon: {
      $type: String,
      required: true,
    },
    text: { $type: Schema.Types.Mixed },
    status: {
      $type: String,
      required: true,
    },
    name: {
      $type: String,
      required: true,
    },
    discountByDuration: {
      $type: Array,
    },
    discountByDoneTask: {
      $type: Array,
    },
    city: {
      $type: Array,
    },
    onlyShowTasker: {
      $type: Boolean,
    },
    shortText: { $type: Schema.Types.Mixed },
    isSubscription: {
      $type: Boolean,
    },
    detailService: { $type: Schema.Types.Mixed },
  },
  { typeKey: '$type', collection: serviceName },
);

const ServiceModel = mongoClientApp.model('id_service', ServiceSchema);
export const service = ServiceModel;
