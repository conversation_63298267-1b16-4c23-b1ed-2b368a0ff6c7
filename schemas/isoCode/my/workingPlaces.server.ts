import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const workingPlacesName = 'my_workingPlaces';

const WorkingPlacesSchema = new Schema(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String],
      },
    ],
  },
  { typeKey: '$type', collection: workingPlacesName },
);

const WorkingPlacesModel = mongoClientApp.model(
  workingPlacesName,
  WorkingPlacesSchema,
);

export const workingPlaces = WorkingPlacesModel;
