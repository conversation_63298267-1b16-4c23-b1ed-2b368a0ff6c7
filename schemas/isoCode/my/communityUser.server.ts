import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const communityUserName = 'my_communityUser';

const CommunityUserSchema = new Schema<CommunityUser>(
  {
    _id: {
      $type: String,
      required: true,
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String,
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean },
      },
    ],
    status: { $type: String },
    following: {
      $type: [String],
    },
    followers: {
      $type: [String],
    },
    favouriteTagIds: {
      $type: [String],
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String },
      },
    ],
    isVerifiedProfile: { type: Boolean },
  },
  { typeKey: '$type', collection: communityUserName },
);

const CommunityUserModel = mongoClientApp.model(
  communityUserName,
  CommunityUserSchema,
);

export const communityUser = CommunityUserModel;
