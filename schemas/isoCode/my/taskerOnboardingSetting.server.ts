import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const taskerOnboardingSettingName = 'my_taskerOnboardingSetting';

const TaskerOnboardingSettingSchema = new Schema<TaskerOnboardingSetting>(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            'NEEDS_UPDATE',
            'FAIL_UPDATED',
            'ELIMINATED',
            'FAIL_CALLING',
            'FAIL_INTERVIEW',
            'REJECTED',
            'REJECT_DOCUMENTS',
            'RESTORED'
          ],
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String },
      },
    ],
  },
  { typeKey: '$type', collection: taskerOnboardingSettingName },
);

const TaskerOnboardingSettingModel = mongoClientApp.model(
  taskerOnboardingSettingName,
  TaskerOnboardingSettingSchema,
);

export const taskerOnboardingSetting = TaskerOnboardingSettingModel;
