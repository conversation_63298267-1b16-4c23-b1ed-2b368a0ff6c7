import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const taskerSpecialCampaignTransactionName = 'my_taskerSpecialCampaignTransaction'

const TaskerSpecialCampaignTransactionSchema = new Schema<SpecialCampaignTransaction>(
  {
    _id: {
      $type: String,
      required: true,
    },
    phone: {
      $type: String,
    },
    taskerId: {
      $type: String,
    },
    campaignId: {
      $type: String,
    },
    campaignName: {
      $type: String,
    },
    status: {
      $type: String,
      enum: ['IN-PROGRESS', 'COMPLETED', 'REWARDED'],
    },
    completedDate: {
      $type: Date,
    },
    rewardedDate: {
      $type: Date,
    },
    createdAt: {
      $type: Date,
      default: Date.now,
    },
  },
  { typeKey: '$type', collection: taskerSpecialCampaignTransactionName },
)

const TaskerSpecialCampaignTransactionModel = mongoClientApp.model(
  taskerSpecialCampaignTransactionName,
  TaskerSpecialCampaignTransactionSchema,
);

export const taskerSpecialCampaignTransaction = TaskerSpecialCampaignTransactionModel;
