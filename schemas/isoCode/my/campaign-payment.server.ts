import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const marketingCampaignPaymentMethodName = 'my_paymentMethodCampaign';

const MarketingCampaignPaymentMethodSchema = new Schema<MarketingCampaignPaymentMethod>(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: Schema.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName }
);

const MarketingCampaignPaymentMethodModel = mongoClientApp.model(
  marketingCampaignPaymentMethodName,
  MarketingCampaignPaymentMethodSchema
);
export const marketingCampaignPaymentMethod = MarketingCampaignPaymentMethodModel;
