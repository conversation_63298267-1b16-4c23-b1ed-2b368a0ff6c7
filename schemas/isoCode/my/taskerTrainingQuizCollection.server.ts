import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const taskerTrainingQuizCollectionName =
  'my_trainingTaskerQuizCollection';

const QuizCollectionSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    title: {
      $type: String,
      required: true,
    },
    code: {
      $type: String,
      required: true,
    },
    description: {
      $type: String,
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true,
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true,
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false,
    },
    image: {
      url: {
        $type: String,
      },
      description: {
        $type: String,
      },
    },
    video: {
      url: {
        $type: String,
      },
      description: {
        $type: String,
      },
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number },
      },
    ],
    createdAt: {
      $type: Date,
      required: true,
    },
    updatedAt: {
      $type: Date,
    },
    createdByUserId: {
      $type: String,
      required: true,
    },
    createdByUsername: {
      $type: String,
      required: true,
    },
    updatedByUserId: {
      $type: String,
    },
    updatedByUsername: {
      $type: String,
    },
    isTesting: {
      $type: Boolean,
    },
  },
  { typeKey: '$type', collection: taskerTrainingQuizCollectionName },
);

const QuizCollectionModel = mongoClientApp.model(
  taskerTrainingQuizCollectionName,
  QuizCollectionSchema,
);

export const taskerTrainingQuizCollection = QuizCollectionModel;
