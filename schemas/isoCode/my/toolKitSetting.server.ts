import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const toolKitSettingName = 'my_toolKitSetting';

const ToolKitSettingSchema = new Schema<ToolkitSetting>(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number },
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }],
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
  },
  { typeKey: '$type', collection: toolKitSettingName },
);

const ToolKitSettingModel = mongoClientApp.model(
  toolKitSettingName,
  ToolKitSettingSchema,
);

export const toolKitSetting = ToolKitSettingModel;
