import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const partnerDirectoryName = 'my_partnerDirectory'

const PartnerDirectorySchema = new Schema(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String },
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String },
    },
    createdAt: {
      $type: Date,
    },
    updatedAt: {
      $type: Date,
    },
  },
  { typeKey: '$type', collection: partnerDirectoryName },
);

const PartnerDirectoryModel = mongoClientApp.model(
  partnerDirectoryName,
  PartnerDirectorySchema,
);
export const partnerDirectory = PartnerDirectoryModel;
