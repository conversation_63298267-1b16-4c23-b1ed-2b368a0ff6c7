import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const taskerPointTransactionName = 'my_taskerPointTransaction';

const TaskerPointTransactionSchema = new Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String },
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String },
  },
  { typeKey: '$type', collection: taskerPointTransactionName },
);

const TaskerPointTransactionModel = mongoClientApp.model(
  taskerPointTransactionName,
  TaskerPointTransactionSchema,
);
export const taskerPointTransaction = TaskerPointTransactionModel;
