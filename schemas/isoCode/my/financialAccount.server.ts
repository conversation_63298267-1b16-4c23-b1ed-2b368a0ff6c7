import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const financialAccountName = 'my_financialAccount';

const FinancialAccountSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    FMainAccount: {
      $type: Number,
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date },
  },
  { typeKey: '$type', collection: financialAccountName },
);

const FinancialAccountModel = mongoClientApp.model(
  'my_financialAccount',
  FinancialAccountSchema,
);

export const financialAccount = FinancialAccountModel;
