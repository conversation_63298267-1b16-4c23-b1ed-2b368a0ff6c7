import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const businessTransactionName = 'my_businessTransaction';

const BusinessTransactionSchema = new Schema<BusinessTransaction>(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String },
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String },
    },
    requester: { $type: String },
    createdBy: { $type: String },
  },
  { typeKey: '$type', collection: businessTransactionName },
);

const BusinessTransactionModel = mongoClientApp.model(
  businessTransactionName,
  BusinessTransactionSchema,
);
export const businessTransaction = BusinessTransactionModel;
