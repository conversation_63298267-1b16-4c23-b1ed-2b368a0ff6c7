import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const communityMedalName = "my_communityMedal";

const CommunityMedalSchema = new Schema<CommunityMedal>(
  {
    _id: {
      $type: String,
      required: true,
    },
    text: {
      $type: Schema.Types.Mixed,
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: Schema.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number },
        },
      ],
      default: undefined,
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: Schema.Types.Mixed,
    },
  },
  { typeKey: "$type", collection: communityMedalName }
);

const CommunityMedalModel = mongoClientApp.model(
  communityMedalName,
  CommunityMedalSchema
);

export const communityMedal = CommunityMedalModel;
