import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const taskerSpecialCampaignName = 'my_taskerSpecialCampaign';

const TaskerSpecialCampaignSchema = new Schema<SpecialCampaign>(
  {
    _id: {
      $type: String,
      required: true,
    },
    name: {
      $type: String,
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String },
    },
    type: {
      $type: String,
      enum: ['REFERRAL_CAMPAIGN', 'TASK_CAMPAIGN'],
    },
    status: {
      $type: String,
      enum: ['ACTIVE', 'INACTIVE'],
    },
    value: {
      $type: Number,
    },
    startDate: {
      $type: Date,
      required: true,
    },
    endDate: {
      $type: Date,
      required: true,
    },
    text: { $type: Schema.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ['BPOINT', 'MONEY', 'PROMOTION'] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ['M', 'P'] },
        taskerJourneyLevels: { $type: [String], default: undefined },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: undefined },
      },
    ],
    createdAt: {
      $type: Date,
      default: Date.now,
    },
    updatedAt: {
      $type: Date,
    },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
    city: {
      $type: [String],
      default: undefined,
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
      },
    ],
  },
  { typeKey: '$type', collection: taskerSpecialCampaignName, _id: false },
);

const TaskerSpecialCampaignModel = mongoClientApp.model(
  taskerSpecialCampaignName,
  TaskerSpecialCampaignSchema,
);

export const taskerSpecialCampaign = TaskerSpecialCampaignModel;
