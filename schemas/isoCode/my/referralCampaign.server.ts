import { mongoClientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const referralCampaignName = 'my_askerReferralCampaign'

const ReferralCampaignSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    image: {
      $type: String,
      required: true,
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      },
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      },
    },
    invitee: { $type: Schema.Types.Mixed, required: true },
    inviter: { $type: Schema.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true,
    },
    createdAt: {
      $type: Date,
      default: Date.now,
    },
    updatedAt: {
      $type: Date,
    },
    isTesting: {
      $type: Boolean,
    },
    status: {
      $type: String,
    },
    isoCode: { $type: String },
  },
  { typeKey: '$type', collection: referralCampaignName },
);

const ReferralCampaignModel = mongoClientApp.model(
  referralCampaignName,
  ReferralCampaignSchema,
);
export const referralCampaign = ReferralCampaignModel;
