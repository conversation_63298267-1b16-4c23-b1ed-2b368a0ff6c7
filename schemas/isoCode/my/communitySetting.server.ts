import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const communitySettingName = "my_communitySetting";

const CommunitySettingSchema = new Schema<CommunitySetting>(
  {
    _id: {
      $type: String,
      required: true,
    },
    isTesting: {
      $type: Boolean,
      required: true,
    },
    tester: {
      $type: [String],
      required: true,
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true,
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: Schema.Types.ObjectId,
            required: true,
          },
          themeImageUrl: {
            $type: String,
            required: true,
          },
        },
      ],
      required: true,
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true,
    },
    postPinned: {
      $type: [String],
      required: false,
      default: [],
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true,
          },
          en: {
            $type: String,
            required: true,
          },
          ko: {
            $type: String,
            required: true,
          },
          th: {
            $type: String,
            required: true,
          },
          id: {
            $type: String,
            required: true,
          },
          ms: {
            $type: String,
            required: true,
          },
        },
      ],
      required: true,
    },
  },
  {
    typeKey: "$type",
    collection: communitySettingName,
  }
);

const CommunitySettingModel = mongoClientApp.model(
  communitySettingName,
  CommunitySettingSchema
);

export const communitySetting = CommunitySettingModel;
