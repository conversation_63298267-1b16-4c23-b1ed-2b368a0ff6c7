import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const businessMemberTransactionName = 'my_businessMemberTransaction';

const BusinessMemberTransactionSchema = new Schema<BusinessMemberTransaction>(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    taskId: { $type: String, required: true },
    userId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String },
    },
    createdAt: { $type: Date, default: Date.now },
  },
  { typeKey: '$type', collection: businessMemberTransactionName },
);

const BusinessMemberTransactionModel = mongoClientApp.model(businessMemberTransactionName, BusinessMemberTransactionSchema);
export const businessMemberTransaction = BusinessMemberTransactionModel;
