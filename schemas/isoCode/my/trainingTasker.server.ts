import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const trainingTaskerName = 'my_trainingTasker';

const TrainingTaskerSchema = new Schema<TrainingTasker>(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String],
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
  },
  { typeKey: '$type', collection: trainingTaskerName },
);

const TrainingTaskerModel = mongoClientApp.model(
  trainingTaskerName,
  TrainingTaskerSchema,
);

export const trainingTasker = TrainingTaskerModel;
