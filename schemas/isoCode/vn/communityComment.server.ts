import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const communityCommentName = 'vn_communityComment';

const CommunityCommentSchema = new Schema<CommunityComment>(
  {
    _id: {
      $type: String,
      required: true,
    },
    postId: {
      $type: String,
    },
    userId: {
      $type: String,
    },
    content: {
      $type: String,
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String,
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String },
      },
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
    commentId: { $type: String },
    isReported: { $type: Boolean },
  },
  { typeKey: '$type', collection: communityCommentName },
);

const CommunityCommentModel = mongoClientApp.model(
  communityCommentName,
  CommunityCommentSchema,
);

export const communityComment = CommunityCommentModel;
