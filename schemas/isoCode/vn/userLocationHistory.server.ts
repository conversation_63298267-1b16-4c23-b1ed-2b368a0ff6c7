import { Schema } from 'mongoose';
import { mongoClientApp } from 'mongo-connection';

export const userLocationHistoryName = 'vn_userLocationHistory';

const UserLocationHistorySchema = new Schema<UserLocationHistory>(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number },
      },
    ],
    createdAt: { $type: Date },
  },
  { typeKey: '$type', collection: userLocationHistoryName },
);

const UserLocationHistoryModel = mongoClientApp.model(
  userLocationHistoryName,
  UserLocationHistorySchema,
);

export const userLocationHistory = UserLocationHistoryModel;