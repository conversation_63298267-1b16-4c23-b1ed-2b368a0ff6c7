import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const marketingCampaignName = 'vn_marketingCampaign';

const MarketingCampaignSchema = new Schema<MarketingCampaign>(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: Schema.Types.Mixed },
    title: {
      $type: Map,
      of: String,
    },
    description: {
      $type: Map,
      of: String,
    },
    subDescription: {
      $type: Map,
      of: String,
    },
    content: {
      $type: Map,
      of: String,
    },
    subContent: {
      $type: Map,
      of: String,
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: Schema.Types.Mixed },
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean },
      },
      text: { $type: Schema.Types.Mixed },
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean },
      },
      text: { $type: Schema.Types.Mixed },
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean },
  },
  { typeKey: '$type', collection: marketingCampaignName },
);

const MarketingCampaignModel = mongoClientApp.model(
  marketingCampaignName,
  MarketingCampaignSchema,
);
export const marketingCampaign = MarketingCampaignModel;
