import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const taskerBNPLTransactionName = "vn_taskerBNPLTransaction";
const TaskerBNPLTransaction = new Schema<TaskerBNPLTransactionSchema>(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String },
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName }
);

const TaskerBNPLTransactionModel = mongoClientApp.model(
  taskerBNPLTransactionName,
  TaskerBNPLTransaction
);

export const taskerBNPLTransaction = TaskerBNPLTransactionModel;
