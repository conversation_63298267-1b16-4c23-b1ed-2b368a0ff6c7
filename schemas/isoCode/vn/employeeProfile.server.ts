import { Schema } from 'mongoose';
import { mongoClientApp } from 'mongo-connection';

export const employeeProfileName = 'vn_employeeProfile';

const EmployeeProfileSchema = new Schema<StaffProfile>(
  {
    _id: {
      $type: String,
      required: true,
    },
    idNumber: {
      $type: String,
      required: true,
    },
    companyId: {
      $type: String,
      required: true,
    },
    name: {
      $type: String,
      required: true,
    },
    phone: {
      $type: String,
      required: true,
    },
    gender: {
      $type: String,
      required: true,
    },
    identityCard: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    portrait: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    dob: {
      $type: Date,
    },
    createdAt: {
      $type: Date,
      required: true,
    },
    updatedAt: {
      $type: Date,
    },
    status: {
      $type: String,
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date },
      },
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
      },
    ],
  },
  { typeKey: '$type', collection: employeeProfileName },
);

const EmployeeProfileModel = mongoClientApp.model(
  'vn_employeeProfile',
  EmployeeProfileSchema,
);

export const employeeProfile = EmployeeProfileModel;
