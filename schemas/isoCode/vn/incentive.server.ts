import { mongoClientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const incentiveName = 'vn_incentive';

const IncentiveSchema = new Schema(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      },
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      },
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number },
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: undefined },
      cities: { $type: [{ $type: String }], default: undefined },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: undefined },
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String },
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now,
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false },
        },
      ],
      default: undefined,
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number },
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number },
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String },
        },
      ],
      default: undefined,
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String },
  },
  { typeKey: '$type', collection: incentiveName },
);

const IncentiveModel = mongoClientApp.model(incentiveName, IncentiveSchema);
export const incentive = IncentiveModel;
