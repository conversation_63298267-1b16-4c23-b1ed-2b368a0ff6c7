import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const taskerTrainingCourseName = 'vn_trainingTaskerCourse';

const CourseSchema = new Schema<Course>(
  {
    _id: {
      $type: String,
      required: true,
    },
    title: {
      $type: String,
      required: true,
    },
    code: {
      $type: String,
      required: true,
    },
    type: {
      $type: String,
      required: true,
    },
    timeToCompleteByMinutes: {
      $type: Number,
    },
    maximumNumberOfRetries: {
      $type: Number,
    },
    percentageToPass: {
      $type: Number,
    },
    deadlineIn: {
      $type: Number,
    },
    isDisplayAllAnswer: {
      $type: Boolean,
    },
    status: {
      $type: String,
      required: true,
    },
    relatedServices: [
      {
        _id: {
          $type: String,
        },
        name: {
          $type: String,
        },
      },
    ],
    quizCollections: [
      {
        _id: {
          $type: String,
        },
        order: {
          $type: Number,
        },
      },
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 },
      },
      byTasker: {
        minimumStar: {
          $type: Number,
        },
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 },
      },
    },
    createdAt: {
      $type: Date,
      required: true,
    },
    updatedAt: {
      $type: Date,
    },
    createdByUserId: {
      $type: String,
      required: true,
    },
    createdByUsername: {
      $type: String,
      required: true,
    },
    updatedByUserId: {
      $type: String,
    },
    updatedByUsername: {
      $type: String,
    },
    isTesting: {
      $type: Boolean,
    },
    displayPosition: {
      $type: String,
    },
    cities: {
      $type: [String],
      default: undefined,
    },
  },
  { typeKey: '$type', collection: taskerTrainingCourseName },
);

const CourseModel = mongoClientApp.model(
  taskerTrainingCourseName,
  CourseSchema,
);

export const taskerTrainingCourse = CourseModel;
