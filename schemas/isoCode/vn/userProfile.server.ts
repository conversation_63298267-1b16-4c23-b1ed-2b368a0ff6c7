import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const userProfileName = 'vn_userProfile';

const UserProfileSchema = new Schema<UserProfile>(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date }, // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String],
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String,
        },
      ],
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String,
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number },
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String },
        },
      ],
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String },
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Schema.Types.Mixed },
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean },
        },
      ],
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date,
        },
      ],
    },
    blackList: {
      $type: [String],
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String,
        },
      ],
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String },
  },
  { typeKey: '$type', collection: userProfileName },
);

const UserProfileModel = mongoClientApp.model(userProfileName, UserProfileSchema);

export const userProfile = UserProfileModel;
