import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const promotionCodeName = 'vn_promotionCode';

const PromotionCodeSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    code: {
      $type: String,
      required: true,
    },
    limit: {
      $type: Number,
      required: true,
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number },
    },
    taskPlace: { $type: Schema.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true,
    },
    source: {
      $type: String,
      required: true,
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      },
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      },
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now,
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: undefined,
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: undefined },
          _id: false,
        },
      ],
      default: undefined,
    },
    target: { $type: String, enum: ['BOTH', 'ASKER', 'TASKER'] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number },
    },
  },
  { typeKey: '$type', collection: promotionCodeName },
);

const PromotionCodeModel = mongoClientApp.model(
  promotionCodeName,
  PromotionCodeSchema,
);
export const promotionCode = PromotionCodeModel;
