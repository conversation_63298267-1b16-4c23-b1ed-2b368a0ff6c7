import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const notificationName = 'vn_notification';

const NotificationSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    userId: {
      $type: String,
      required: true,
    },
    taskId: {
      $type: String,
    },
    title: {
      $type: String,
      required: true,
    },
    description: {
      $type: String,
      required: true,
    },
    createdAt: {
      $type: Date,
      required: true,
    },
    isForce: {
      $type: Boolean,
    },
    type: {
      $type: Number,
    },
  },
  { typeKey: '$type', collection: notificationName },
);

const NotificationModel = mongoClientApp.model(
  'vn_notification',
  NotificationSchema,
);

export const notification = NotificationModel;
