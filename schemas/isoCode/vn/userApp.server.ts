import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const usersName = 'users';

const UsersAppSchema = new Schema<UserApp>(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String],
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String,
        },
      ],
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String,
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number },
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String },
        },
      ],
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String },
      },
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String },
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean },
        },
      ],
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String },
        },
      ],
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date,
        },
      ],
    },
    blackList: {
      $type: [String],
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String,
        },
      ],
    },
    // Identity Card Verification System
    identityCard: {
      images: {
        $type: [String],
        default: [],
        validate: {
          validator: function(v) {
            return v.length <= 10; // Limit to 10 images maximum
          },
          message: 'Maximum 10 identity card images allowed'
        }
      },
      status: {
        $type: String,
        enum: [
          'Not Uploaded',
          'Verifying',
          'Need updates',
          'Updated',
          'Approved'
        ],
        default: 'Not Uploaded',
        required: true,
        index: true, // Index for efficient tab filtering
      },
      uploadTimestamp: {
        $type: Date,
        index: true, // Index for sorting by upload time
      },
      detectedPhone: {
        $type: String,
        validate: {
          validator: function(v) {
            // Vietnamese phone number validation (optional field)
            return !v || /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/.test(v);
          },
          message: 'Invalid Vietnamese phone number format'
        }
      },
      reason: {
        $type: String,
        maxlength: 500, // Limit reason length
      },
      // OCR extracted information (temporary storage before approval)
      ocrData: {
        cccdNumber: { $type: String, maxlength: 20 },
        fullName: { $type: String, maxlength: 100 },
        dateOfBirth: { $type: Date },
        placeOfOrigin: { $type: String, maxlength: 200 },
        placeOfResidence: { $type: String, maxlength: 200 },
        extractedAt: { $type: Date },
        confidence: { $type: Number, min: 0, max: 1 }, // OCR confidence score
      },
    },
    // Action History for Identity Card Verification
    actionHistories: {
      $type: [
        {
          userId: {
            $type: String,
            required: true,
            ref: 'Users'
          },
          oldStatus: {
            $type: String,
            enum: [
              'Not Uploaded',
              'Verifying',
              'Need updates',
              'Updated',
              'Approved'
            ]
          },
          newStatus: {
            $type: String,
            required: true,
            enum: [
              'Not Uploaded',
              'Verifying',
              'Need updates',
              'Updated',
              'Approved'
            ]
          },
          updatedByUsername: {
            $type: String,
            required: true,
            maxlength: 100
          },
          reason: {
            $type: String,
            maxlength: 500
          },
          createdAt: {
            $type: Date,
            required: true,
            default: Date.now,
            index: true // Index for sorting action history
          },
          // Additional metadata for admin actions
          adminAction: {
            type: {
              $type: String,
              enum: ['approve', 'reject', 'request_update', 'system_update', 'upload']
            },
            ipAddress: { $type: String, maxlength: 45 }, // Support IPv6
            userAgent: { $type: String, maxlength: 500 }
          }
        },
      ],
      default: [],
    },
  },
  { typeKey: '$type', collection: usersName },
);

// Compound indexes for efficient identity card verification queries
UsersAppSchema.index({ 'identityCard.status': 1, 'identityCard.uploadTimestamp': -1 });
UsersAppSchema.index({ 'identityCard.status': 1, 'createdAt': -1 });
UsersAppSchema.index({ 'identityCard.detectedPhone': 1 }, { sparse: true });
UsersAppSchema.index({ 'identityCard.ocrData.cccdNumber': 1 }, { sparse: true });
UsersAppSchema.index({ 'actionHistories.createdAt': -1 }, { sparse: true });

const UsersAppModel = mongoClientApp.model('vn_users', UsersAppSchema);

export const users = UsersAppModel;
