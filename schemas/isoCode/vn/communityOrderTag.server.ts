import { mongoClientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const communityTagOrderName = 'vn_communityTagOrder';

const CommunityTagOrderSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: Schema.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: Schema.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String },
  },
  { typeKey: '$type', collection: communityTagOrderName },
);

const CommunityTagOrderModel = mongoClientApp.model(
  communityTagOrderName,
  CommunityTagOrderSchema,
);

export const communityTagOrder = CommunityTagOrderModel;
