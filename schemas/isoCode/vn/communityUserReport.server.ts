import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const communityUserReportName = 'vn_communityUserReport';

const CommunityUserReportSchema = new Schema<CommunityUserReport>(
  {
    _id: {
      $type: String,
      required: true,
    },
    userId: {
      $type: String,
      required: true,
    },
    reportedUserId: { $type: String },
    status: {
      $type: String,
    },
    commentId: {
      $type: String,
    },
    reason: {
      name: { $type: String },
      content: { $type: String },
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
    profileId: {
      $type: String,
    },
    postId: {
      $type: String,
    },
  },
  { typeKey: '$type', collection: communityUserReportName },
);

const CommunityUserReportModel = mongoClientApp.model(
  communityUserReportName,
  CommunityUserReportSchema,
);

export const communityUserReport = CommunityUserReportModel;
