import { mongoClientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const promotionHistoryName = 'vn_promotionHistory';

const PromotionHistorySchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    promotionCode: {
      $type: String,
      required: true,
    },
    userId: {
      $type: String,
      required: true,
    },
    phone: {
      $type: String,
      required: true,
    },
    name: {
      $type: String,
      required: true,
    },
    date: {
      $type: Date,
      required: true,
    },
    value: {
      $type: Number,
      required: true,
    },
    createdAt: {
      $type: Number,
      required: true,
    },
  },
  { typeKey: '$type', collection: promotionHistoryName },
);

const PromotionHistoryModel = mongoClientApp.model(
  promotionHistoryName,
  PromotionHistorySchema,
);
export const promotionHistory = PromotionHistoryModel;
