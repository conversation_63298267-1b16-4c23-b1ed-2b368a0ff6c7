import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const serviceChannelName = 'vn_serviceChannel';

const ServiceChannelSchema = new Schema<ServiceChannel>(
  {
    _id: {
      $type: String,
      required: true,
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String,
    },
  },
  { typeKey: '$type', collection: serviceChannelName },
);

const ServiceChannelModel = mongoClientApp.model(
  'vn_serviceChannel',
  ServiceChannelSchema,
);

export const serviceChannel = ServiceChannelModel;
