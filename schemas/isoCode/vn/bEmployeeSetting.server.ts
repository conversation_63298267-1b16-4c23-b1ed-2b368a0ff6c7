import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const bEmployeeSettingName = "vn_bEmployeeSetting";

const BEmployeeSettingSchema = new Schema<EmployeeSetting>(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] },
  },
  { typeKey: "$type", collection: bEmployeeSettingName }
);

const BEmployeeSettingModel = mongoClientApp.model(
  "vn_bEmployeeSetting",
  BEmployeeSettingSchema
);

export const bEmployeeSetting = BEmployeeSettingModel;
