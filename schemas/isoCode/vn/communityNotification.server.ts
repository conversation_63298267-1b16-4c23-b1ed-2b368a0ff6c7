import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const communityNotificationName = 'vn_communityNotification';

const CommunityNotificationSchema = new Schema<CommunityNotification>(
  {
    _id: {
      $type: String,
      required: true,
    },
    userId: {
      $type: String,
    },
    message: {
      $type: Schema.Types.Mixed,
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String,
    },
    navigateTo: {
      $type: String,
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String },
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
  },
  { typeKey: '$type', collection: communityNotificationName },
);

const CommunityNotificationModel = mongoClientApp.model(
  communityNotificationName,
  CommunityNotificationSchema,
);

export const communityNotification = CommunityNotificationModel;
