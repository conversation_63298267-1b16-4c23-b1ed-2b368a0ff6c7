import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const taskerTrainingCourseStartDateName =
  'th_trainingTaskerCourseStartDate';

const CourseStartDateSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    courseId: {
      $type: String,
      required: true,
    },
    taskerId: {
      $type: String,
      required: true,
    },
    startDate: {
      $type: Date,
    },
    isUnblocked: {
      $type: Boolean,
    },
    deadlineIn: {
      $type: Number,
    },
  },
  { typeKey: '$type', collection: taskerTrainingCourseStartDateName },
);

const CourseStartDateModel = mongoClientApp.model(
  taskerTrainingCourseStartDateName,
  CourseStartDateSchema,
);

export const taskerTrainingCourseStartDate = CourseStartDateModel;
