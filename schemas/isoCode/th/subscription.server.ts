import { mongo<PERSON>lientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const subscriptionName = 'th_subscription';
const SubscriptionSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date,
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String },
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number },
    },
    schedule: {
      $type: Schema.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: Schema.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String },
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number },
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number },
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number },
        }
      ]
    }
  },
  { typeKey: '$type', collection: subscriptionName },
);

const SubscriptionModel = mongoClientApp.model(subscriptionName, SubscriptionSchema);

export const subscription = SubscriptionModel;
