import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const usersName = 'users';

const UsersAppSchema = new Schema<UserApp>(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String],
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String,
        },
      ],
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String,
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number },
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String },
        },
      ],
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String },
      },
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String },
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean },
        },
      ],
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String },
        },
      ],
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date,
        },
      ],
    },
    blackList: {
      $type: [String],
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String,
        },
      ],
    },
  },
  { typeKey: '$type', collection: usersName },
);

const UsersAppModel = mongoClientApp.model('th_users', UsersAppSchema);

export const users = UsersAppModel;
