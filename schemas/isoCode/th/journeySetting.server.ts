import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const journeySettingName = 'th_journeySetting';

const JourneySettingSchema = new Schema(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String },
        },
      ],
      default: undefined,
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
  },
  { typeKey: '$type', collection: journeySettingName },
);

const JourneySettingModel = mongoClientApp.model(
  journeySettingName,
  JourneySettingSchema,
);
export const journeySetting = JourneySettingModel;
