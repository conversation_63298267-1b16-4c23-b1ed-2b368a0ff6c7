import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const toolKitItemsName = 'th_toolKitItems';

const ToolKitItemsSchema = new Schema<ToolKitItem>(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
  },
  { typeKey: '$type', collection: toolKitItemsName },
);

const ToolKitItemsModel = mongoClientApp.model(
  'th_toolKitItems',
  ToolKitItemsSchema,
);

export const toolKitItems = ToolKitItemsModel;
