import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const communityTagName = 'th_communityTag';

const CommunityTagSchema = new Schema<CommunityTag>(
  {
    _id: {
      $type: String,
      required: true,
    },
    status: {
      $type: String,
    },
    text: {
      $type: Schema.Types.Mixed,
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String,
    },
    updatedBy: {
      $type: String,
    },
    startDate: { $type: Date, default: undefined },
    endDate: { $type: Date, default: undefined },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: undefined },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: Schema.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now },
      }
    ]
  },
  { typeKey: '$type', collection: communityTagName },
);

const CommunityTagModel = mongoClientApp.model(
  communityTagName,
  CommunityTagSchema,
);

export const communityTag = CommunityTagModel;
