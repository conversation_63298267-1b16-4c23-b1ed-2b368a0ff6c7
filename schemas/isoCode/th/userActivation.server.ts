import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const userActivationName = 'th_userActivation';

const UserActivationSchema = new Schema<UserActivation>(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String },
  },
  { typeKey: '$type', collection: userActivationName },
);

const UserActivationModel = mongoClientApp.model(
  userActivationName,
  UserActivationSchema,
);
export const userActivation = UserActivationModel;
