import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const trainingJourneyName = 'th_trainingJourney';

const TrainingJourneySchema = new Schema<TrainingJourney>(
  {
    _id: {
      $type: String,
      required: true,
    },
    quizzes: {
      $type: [String],
    },
    videos: {
      $type: [String],
    },
    status: {
      $type: String,
      required: true,
    },
    name: {
      $type: String,
    },
    title: {
      $type: Map,
      of: String,
    },
    target: {
      $type: Number,
    },
    readingTime: {
      $type: Number,
    },
    createdAt: {
      $type: Date,
      required: true,
    },
    level: {
      $type: String,
    },
    isTesting: {
      $type: Boolean,
    },
  },
  { typeKey: '$type', collection: trainingJourneyName },
);

const TrainingJourneyModel = mongoClientApp.model(
  'th_trainingJourney',
  TrainingJourneySchema,
);

export const trainingJourney = TrainingJourneyModel;
