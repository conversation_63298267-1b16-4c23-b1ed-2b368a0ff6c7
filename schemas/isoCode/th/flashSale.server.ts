import { mongoClientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const flashSaleName = 'th_askerFlashSaleIncentive'

const FlashSaleSchema = new Schema(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      },
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      },
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String,
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true },
      },
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String },
  },
  { typeKey: '$type', collection: flashSaleName },
);

const FlashSaleModel = mongoClientApp.model(
  'th_askerFlashSaleIncentive',
  FlashSaleSchema,
);
export const flashSale = FlashSaleModel;
