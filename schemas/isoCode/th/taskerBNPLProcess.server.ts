import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const taskerBNPLProcessName = "th_taskerBNPLProcess";
const TaskerBNPLProcess = new Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date },
  },
  { typeKey: "$type", collection: taskerBNPLProcessName }
);

const TaskerBNPLProcessModel = mongoClientApp.model(
  "th_taskerBNPLProcess",
  TaskerBNPLProcess
);

export const taskerBNPLProcess = TaskerBNPLProcessModel;
