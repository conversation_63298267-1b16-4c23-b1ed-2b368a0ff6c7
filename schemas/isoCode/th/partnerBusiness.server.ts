import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const businessName = 'th_business';

const BusinessSchema = new Schema<AccountBusiness>(
  {
    _id: { $type: String, required: true }, // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        'E_COMMERCE',
        'INFORMATION_TECHNOLOGY',
        'SERVICE_FINANCE',
        'TRAVEL_TOURISM',
        'EDUCATION_TRAINING',
        'FOOD_BEVERAGE',
        'MANUFACTURING',
        'HEALTHCARE_SERVICE',
        'OTHER',
      ],
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ['UNDER_50', 'UNDER_250', 'UNDER_500', 'UNDER_1000'],
    },
    businessLicense: [{ name: { $type: String }, url: { $type: String } }], // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ['REGISTERED', 'VERIFYING', 'REJECTED', 'BLOCKED', 'ACTIVE'],
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true }, // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema.Types.Mixed },
            nextData: { $type: Schema.Types.Mixed },
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema.Types.Mixed }, // Dữ liệu bổ sung khác, hiện tại không có field này
        },
      ],
      default: undefined,
    },
    topUpSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ['ACTIVE', 'INACTIVE'],
      },
    },
    revokeSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ['ACTIVE', 'INACTIVE'],
      },
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date },
        },
      ],
      default: undefined,
    },
  },
  { typeKey: '$type', collection: businessName },
);

const BusinessModel = mongoClientApp.model(businessName, BusinessSchema);
export const business = BusinessModel;
