import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const serviceChannelName = 'th_serviceChannel';

const ServiceChannelSchema = new Schema<ServiceChannel>(
  {
    _id: {
      $type: String,
      required: true,
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String,
    },
  },
  { typeKey: '$type', collection: serviceChannelName },
);

const ServiceChannelModel = mongoClientApp.model(
  'th_serviceChannel',
  ServiceChannelSchema,
);

export const serviceChannel = ServiceChannelModel;
