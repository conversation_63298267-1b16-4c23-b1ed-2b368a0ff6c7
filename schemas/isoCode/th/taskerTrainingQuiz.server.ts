import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const taskerTrainingQuizName = 'th_trainingTaskerQuiz';

const QuizSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    title: {
      $type: String,
      required: true,
    },
    code: {
      $type: String,
      required: true,
    },
    description: {
      $type: String,
    },
    isRandomAnswer: {
      $type: Boolean,
    },
    image: {
      url: {
        $type: String,
      },
      description: {
        $type: String,
      },
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean },
      },
    ],
    createdAt: {
      $type: Date,
      required: true,
    },
    updatedAt: {
      $type: Date,
    },
    createdByUserId: {
      $type: String,
      required: true,
    },
    createdByUsername: {
      $type: String,
      required: true,
    },
    updatedByUserId: {
      $type: String,
    },
    updatedByUsername: {
      $type: String,
    },
    isTesting: {
      $type: Boolean,
    },
  },
  { typeKey: '$type', collection: taskerTrainingQuizName },
);

const QuizModel = mongoClientApp.model(taskerTrainingQuizName, QuizSchema);

export const taskerTrainingQuiz = QuizModel;
