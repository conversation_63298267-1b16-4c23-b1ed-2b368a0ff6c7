import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";

export const paymentToolKitTransactionName = "th_paymentToolKitTransaction";

const PaymentToolKitTransactionSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String },
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date },
      },
    ],
    createdAt: { $type: Date },
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName }
);

const PaymentToolKitTransactionModel = mongoClientApp.model(
  paymentToolKitTransactionName,
  PaymentToolKitTransactionSchema
);

export const paymentToolKitTransaction = PaymentToolKitTransactionModel;
