import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const userComboVoucherName = 'th_userComboVoucher';

const UserComboVoucherSchema = new Schema<UserComboVoucher>(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ['ACTIVE', 'INACTIVE'] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: Schema.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: Schema.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] },
      },
    ],
  },
  { typeKey: '$type', collection: userComboVoucherName },
);

const UserComboVoucherModel = mongoClientApp.model(
  userComboVoucherName,
  UserComboVoucherSchema,
);
export const userComboVoucher = UserComboVoucherModel;
