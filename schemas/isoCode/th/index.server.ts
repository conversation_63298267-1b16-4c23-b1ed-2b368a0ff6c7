import { bEmployee, bEmployeeName } from './bEmployee.server';
import {
  bEmployeeSetting,
  bEmployeeSettingName,
} from './bEmployeeSetting.server';
import { bundleVoucher, bundleVoucherName } from './bundleVoucher';
import {
  marketingCampaignPaymentMethod,
  marketingCampaignPaymentMethodName,
} from './campaign-payment.server';
import { marketingCampaign, marketingCampaignName } from './campaign.server';
import { comboVoucher, comboVoucherName } from './comboVoucher.server';
import {
  communityComment,
  communityCommentName,
} from './communityComment.server';
import { communityMedal, communityMedalName } from './communityMedal.server';
import {
  communityNotification,
  communityNotificationName,
} from './communityNotification.server';
import {
  communityTagOrder,
  communityTagOrderName,
} from './communityOrderTag.server';
import { communityPost, communityPostName } from './communityPost.server';
import { communitySetting, communitySettingName } from './communitySetting.server';
import { communityTag, communityTagName } from './communityTag.server';
import { communityUser, communityUserName } from './communityUser.server';
import {
  communityUserReport,
  communityUserReportName,
} from './communityUserReport.server';
import { employeeProfile, employeeProfileName } from './employeeProfile.server';
import { FATransaction, FATransactionName } from './FATransaction.server';
import {
  financialAccount,
  financialAccountName,
} from './financialAccount.server';
import { flashSale, flashSaleName } from './flashSale.server';
import { historyTasks, historyTasksName } from './historyTasks.server';
import { incentive, incentiveName } from './incentive.server';
import { journeySetting, journeySettingName } from './journeySetting.server';
import { notification, notificationName } from './notification.server';
import { business, businessName } from './partnerBusiness.server';
import {
  businessLevel,
  businessLevelName,
} from './partnerBusinessLevel.server';
import {
  businessMember,
  businessMemberName,
} from './partnerBusinessMember.server';
import {
  businessMemberTransaction,
  businessMemberTransactionName,
} from './partnerBusinessMemberTransaction.server';
import {
  businessSetupAllocationAndReallocation,
  businessSetupAllocationAndReallocationName,
} from './partnerBusinessSetupAllocateAndReallocate.server';
import {
  businessTransaction,
  businessTransactionName,
} from './partnerBusinessTransaction.server';
import {
  partnerDirectory,
  partnerDirectoryName,
} from './partnerDirectory.server';
import { partnerRequest, partnerRequestName } from './partnerRequest.server';
import {
  paymentToolKitTransaction,
  paymentToolKitTransactionName,
} from './paymentToolKitTransaction.server';
import { promotionCode, promotionCodeName } from './promotionCode.server';
import {
  promotionHistory,
  promotionHistoryName,
} from './promotionHistory.server';
import { promotionSource, promotionSourceName } from './promotionSource.server';
import { rating, ratingName } from './rating.server';
import {
  referralCampaign,
  referralCampaignName,
} from './referralCampaign.server';
import { service, serviceName } from './service.server';
import { serviceChannel, serviceChannelName } from './serviceChannel.server';
import { settingCountry, settingCountryName } from './settingCountry.server';
import { settingSystem, settingSystemName } from './settingSystem.server';
import { subscription, subscriptionName } from './subscription.server';
import { task, taskName } from './task.server';
import {
  taskerBNPLProcess,
  taskerBNPLProcessName,
} from './taskerBNPLProcess.server';
import {
  taskerBNPLTransaction,
  taskerBNPLTransactionName,
} from './taskerBNPLTransaction.server';
import { taskerGift, taskerGiftName } from './taskerGift.server';
import { taskerIncentive, taskerIncentiveName } from './taskerIncentive.server';
import {
  taskerOnboardingSetting,
  taskerOnboardingSettingName,
} from './taskerOnboardingSetting.server';
import {
  taskerPointTransaction,
  taskerPointTransactionName,
} from './taskerPointTransaction.server';
import { taskerProfile, taskerProfileName } from './taskerProfile.server';
import {
  taskerSpecialCampaign,
  taskerSpecialCampaignName,
} from './taskerSpecialCampaign.server';
import {
  taskerSpecialCampaignTransaction,
  taskerSpecialCampaignTransactionName,
} from './taskerSpecialCampaignTransaction.server';
import {
  taskerToolkitLadingDetails,
  taskerToolkitLadingDetailsName,
} from './taskerToolkitLadingDetails.server';
import {
  taskerTrainingCourse,
  taskerTrainingCourseName,
} from './taskerTrainingCourse.server';
import {
  taskerTrainingCourseStartDate,
  taskerTrainingCourseStartDateName,
} from './taskerTrainingCourseStartDate';
import {
  taskerTrainingQuiz,
  taskerTrainingQuizName,
} from './taskerTrainingQuiz.server';
import {
  taskerTrainingQuizCollection,
  taskerTrainingQuizCollectionName,
} from './taskerTrainingQuizCollection.server';
import {
  taskerTrainingSubmission,
  taskerTrainingSubmissionName,
} from './taskerTrainingSubmission.server';
import { thingsToKnow, thingsToKnowName } from './thingsToKnow.server';
import { toolKitItems, toolKitItemsName } from './toolKitItems.server';
import { toolKitSetting, toolKitSettingName } from './toolKitSetting.server';
import { trainingJourney, trainingJourneyName } from './trainingJourney.server';
import { trainingTasker, trainingTaskerName } from './trainingTasker.server';
import { userActivation, userActivationName } from './userActivation.server';
import { users, usersName } from './userApp.server';
import {
  userComboVoucher,
  userComboVoucherName,
} from './userComboVoucher.server';
import {
  userLocationHistory,
  userLocationHistoryName,
} from './userLocationHistory.server';
import { userProfile, userProfileName } from './userProfile.server';
import { workingPlaces, workingPlacesName } from './workingPlaces.server';

const TH = {
  task,
  historyTasks,
  userLocationHistory,
  trainingTasker,
  trainingJourney,
  toolKitSetting,
  toolKitItems,
  thingsToKnow,
  taskerTrainingSubmission,
  taskerTrainingQuizCollection,
  taskerTrainingQuiz,
  taskerTrainingCourse,
  taskerToolkitLadingDetails,
  financialAccount,
  taskerIncentive,
  taskerPointTransaction,
  taskerGift,
  notification,
  taskerProfile,
  employeeProfile,
  workingPlaces,
  flashSale,
  incentive,
  promotionCode,
  promotionSource,
  service,
  promotionHistory,
  settingCountry,
  comboVoucher,
  referralCampaign,
  marketingCampaign,
  settingSystem,
  partnerDirectory,
  partnerRequest,
  communityComment,
  communityMedal,
  communityNotification,
  communityPost,
  communitySetting,
  communityTag,
  communityUser,
  communityUserReport,
  subscription,
  taskerOnboardingSetting,
  serviceChannel,
  users,
  bEmployee,
  bEmployeeSetting,
  FATransaction,
  taskerBNPLTransaction,
  taskerBNPLProcess,
  paymentToolKitTransaction,
  rating,
  userActivation,
  business,
  businessLevel,
  businessMember,
  businessMemberTransaction,
  businessTransaction,
  businessSetupAllocationAndReallocation,
  taskerTrainingCourseStartDate,
  taskerSpecialCampaign,
  taskerSpecialCampaignTransaction,
  marketingCampaignPaymentMethod,
  userProfile,
  communityTagOrder,
  journeySetting,
  userComboVoucher,
  bundleVoucher,
};

export const THName = {
  task: taskName,
  historyTasks: historyTasksName,
  userLocationHistory: userLocationHistoryName,
  trainingTasker: trainingTaskerName,
  trainingJourney: trainingJourneyName,
  toolKitSetting: toolKitSettingName,
  toolKitItems: toolKitItemsName,
  thingsToKnow: thingsToKnowName,
  taskerTrainingSubmission: taskerTrainingSubmissionName,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName,
  taskerTrainingQuiz: taskerTrainingQuizName,
  taskerTrainingCourse: taskerTrainingCourseName,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName,
  taskerPointTransaction: taskerPointTransactionName,
  taskerIncentive: taskerIncentiveName,
  taskerGift: taskerGiftName,
  notification: notificationName,
  financialAccount: financialAccountName,
  taskerProfile: taskerProfileName,
  employeeProfile: employeeProfileName,
  workingPlaces: workingPlacesName,
  service: serviceName,
  settingCountry: settingCountryName,
  settingSystem: settingSystemName,
  incentive: incentiveName,
  communityComment: communityCommentName,
  communityMedal: communityMedalName,
  communityNotification: communityNotificationName,
  communityPost: communityPostName,
  communitySetting: communitySettingName,
  communityTag: communityTagName,
  communityUser: communityUserName,
  communityUserReport: communityUserReportName,
  subscription: subscriptionName,
  taskerOnboardingSetting: taskerOnboardingSettingName,
  users: usersName,
  serviceChannel: serviceChannelName,
  bEmployee: bEmployeeName,
  bEmployeeSetting: bEmployeeSettingName,
  FATransaction: FATransactionName,
  taskerBNPLTransaction: taskerBNPLTransactionName,
  taskerBNPLProcess: taskerBNPLProcessName,
  paymentToolKitTransaction: paymentToolKitTransactionName,
  promotionCode: promotionCodeName,
  promotionHistory: promotionHistoryName,
  marketingCampaign: marketingCampaignName,
  rating: ratingName,
  userActivation: userActivationName,
  business: businessName,
  businessMember: businessMemberName,
  businessLevel: businessLevelName,
  businessMemberTransaction: businessMemberTransactionName,
  businessTransaction: businessTransactionName,
  businessSetupAllocationAndReallocation:
    businessSetupAllocationAndReallocationName,
  comboVoucher: comboVoucherName,
  flashSale: flashSaleName,
  partnerDirectory: partnerDirectoryName,
  partnerRequest: partnerRequestName,
  promotionSource: promotionSourceName,
  referralCampaign: referralCampaignName,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName,
  taskerSpecialCampaign: taskerSpecialCampaignName,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName,
  userProfile: userProfileName,
  communityTagOrder: communityTagOrderName,
  journeySetting: journeySettingName,
  userComboVoucher: userComboVoucherName,
  bundleVoucher: bundleVoucherName,
};

export default TH;
