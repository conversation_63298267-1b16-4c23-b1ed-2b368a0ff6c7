import { mongoClientApp } from "mongo-connection";
import { Schema } from 'mongoose';

export const settingSystemName = 'th_settingSystem';

const SettingSystemSchema = new Schema<SettingSystem>(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String,
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number },
        },
      ],
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number },
        },
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: Schema.Types.Mixed
          }
        },
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date },
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date },
      },
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date },
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date },
      },
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: {$type: [Number]},
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String },
          }
        ]
      }
    ]
  },
  { typeKey: '$type', collection: settingSystemName },
);

const SettingSystemModel = mongoClientApp.model(
  'th_settingSystem',
  SettingSystemSchema,
);
export const settingSystem = SettingSystemModel;
