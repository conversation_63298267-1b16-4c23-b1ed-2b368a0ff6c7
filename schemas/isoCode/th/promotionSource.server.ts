import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const promotionSourceName = 'th_promotionSource'

const PromotionSourceSchema = new Schema(
  {
    _id: {
      $type: String,
      required: true,
    },
    name: {
      $type: String,
      required: true,
    },
    description: {
      $type: String,
      required: true,
    },
    status: {
      $type: String,
    },
    createdAt: {
      $type: Date,
      default: Date.now,
    },
    updatedAt: {
      $type: Date,
    },
    isTesting: {
      $type: Boolean,
    },
    createdBy: {
      $type: String,
      required: true,
    },
  },
  { typeKey: '$type', collection: promotionSourceName },
);

const PromotionSourceModel = mongoClientApp.model(
  promotionSourceName,
  PromotionSourceSchema,
);

export const promotionSource = PromotionSourceModel;
