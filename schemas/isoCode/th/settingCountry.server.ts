import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const settingCountryName = 'th_settingCountry';

const SettingCountrySchema = new Schema<SettingCountry>(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String },
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String },
          },
        ],
      },
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ['ACTIVE', 'INACTIVE'] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: undefined },
          isTesting: { $type: Boolean },
        },
      ],
    },
  },
  { typeKey: '$type', collection: settingCountryName },
);

const SettingCountryModel = mongoClientApp.model(
  settingCountryName,
  SettingCountrySchema,
);

export const settingCountry = SettingCountryModel;
