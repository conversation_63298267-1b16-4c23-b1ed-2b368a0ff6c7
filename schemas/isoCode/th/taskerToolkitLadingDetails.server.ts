import { Schema } from 'mongoose';
import { mongoClientApp } from "mongo-connection";

export const taskerToolkitLadingDetailsName = 'th_taskerToolkitLadingDetails';

const TaskerToolkitLadingDetailsSchema = new Schema<TaskerToolkitLadingDetails>(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number },
  },
  { typeKey: '$type', collection: taskerToolkitLadingDetailsName },
);

const TaskerToolkitLadingDetailsModel = mongoClientApp.model(
  'th_taskerToolkitLadingDetails',
  TaskerToolkitLadingDetailsSchema,
);

export const taskerToolkitLadingDetails = TaskerToolkitLadingDetailsModel;
