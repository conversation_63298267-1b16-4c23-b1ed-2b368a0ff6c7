import { Schema } from 'mongoose';
import { mongoClientApp } from 'mongo-connection';

export const taskerProfileName = 'th_taskerProfile';

const TaskerProfileSchema = new Schema<TaskerProfile>(
  {
    _id: {
      $type: String,
      required: true,
    },
    taskerId: {
      $type: String,
      required: true,
      ref: 'Users',
    },
    taskerPhone: {
      $type: String,
      required: true,
    },
    taskerIdNumber: {
      $type: String,
      required: true,
    },
    identityCard: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    isPartner: {
      $type: Boolean,
    },
    criminalRecords: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    massagePracticeCertificate: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    curriculumVitae: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    confirmationConduct: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    passport: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    houseElectricBill: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    workPermit: {
      images: {
        $type: Array,
      },
      status: {
        $type: String,
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date },
        },
      ],
    },
    taskerName: {
      $type: String,
      required: true,
    },
    updatedAt: {
      $type: Date,
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true,
      },
      city: {
        $type: String,
        required: true,
      },
      name: {
        $type: String,
        required: true,
      },
      phoneNumber: {
        $type: String,
        required: true,
      },
      date: {
        $type: Date,
        required: true,
      },
    },
    status: {
      $type: String,
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date },
      },
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date },
      },
    ],
    processStatus: {
      $type: String,
    },
  },
  {
    typeKey: '$type',
    collection: taskerProfileName,
  },
);

const TaskerProfileModel = mongoClientApp.model(
  'th_taskerProfile',
  TaskerProfileSchema,
);

export const taskerProfile = TaskerProfileModel;
