import { mongoClientApp } from 'mongo-connection';
import { Schema } from 'mongoose';

export const businessSetupAllocationAndReallocationName = 'th_businessSetupAllocateAndReallocate';

const BusinessSetupAllocationAndReallocationSchema = new Schema<BusinessAllocationReallocation>(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
        type: { $type: String, required: true },
        freq: { $type: Number },
        date: { $type: String },
        otherDate: { $type: Date},
        status: { $type: String },
    },
    reallocate: {
        type: { $type: String, required: true },
        freq: { $type: Number },
        date: { $type: String },
        otherDate: { $type: Date},
        status: { $type: String},
    }
  },
  { typeKey: '$type', collection: businessSetupAllocationAndReallocationName },
);

const BusinessSetupAllocationAndReallocationModel = mongoClientApp.model(businessSetupAllocationAndReallocationName, BusinessSetupAllocationAndReallocationSchema);
export const businessSetupAllocationAndReallocation = BusinessSetupAllocationAndReallocationModel;
