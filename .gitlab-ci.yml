default:
  image: node:latest

stages:
  - version-patch

Version patch:
  stage: version-patch
  only:
    - main
  script:
    - "which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )"
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan gitlab.com >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - npm i pnpm -g
    - pnpm install

    - git remote add gitlab_origin https://oauth2:$<EMAIL>/btaskee/btaskee-ops/schemas.git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Minhlee Bot"
    - npm run build
    - |
      # Check if there are changes in the dist/ folder
      if [[ -n $(git status dist/ --porcelain) ]]; then
        echo "Changes detected in dist/, committing and pushing..."
        git add dist/
        git commit -m "Auto build package from CI"
        git push gitlab_origin HEAD:main -o ci.skip
      else
        echo "No changes in dist/, skipping commit and push."
      fi

    - git checkout -- .
    - npm version patch
    - git push gitlab_origin HEAD:main -o ci.skip
    - git push gitlab_origin --tags -o ci.skip
