{"name": "schemas", "version": "1.0.72", "description": "schemas for Remix micro services", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "node build.mjs", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"btaskee-logger": "git+ssh://**************:btaskee/btaskee-ops/logger.git#v1.0.3", "btaskee-dotenv": "git+ssh://**************:btaskee/btaskee-ops/dotenv.git#v1.0.5", "btaskee-types": "git+ssh://**************:btaskee/btaskee-ops/types.git#6b6027cd9516efabea7ccb7c5478b3aaa98daa6b", "btaskee-constants": "git+ssh://**************:btaskee/btaskee-ops/constants.git#v0.0.23", "mongo-connection": "git+ssh://**************:btaskee/btaskee-ops/mongo-connection.git#v1.0.2"}, "peerDependencies": {"btaskee-logger": "1.0.3", "btaskee-dotenv": "1.0.5", "btaskee-types": "1.8.81", "btaskee-constants": "0.0.23", "mongo-connection": "1.0.2", "mongoose": "8.8.4"}, "devDependencies": {"@types/mongoose": "^5.11.97", "@types/node": "^20.14.2", "esbuild": "^0.21.4", "tsup": "^8.1.0", "typescript": "^5.1.6"}}