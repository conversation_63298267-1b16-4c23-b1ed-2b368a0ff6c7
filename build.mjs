import * as esbuild from 'esbuild';
import * as tsup from 'tsup';

async function build() {
  const file = `./schemas/index.ts`;
  const dist = `./dist`;

  const esbuildConfig = {
    entryPoints: [file],
    bundle: true,
    sourcemap: true,
    format: 'cjs',
    target: 'node22.0',
    platform: 'node',
    outdir: dist,
    packages: "external"
  };

  await esbuild.build(esbuildConfig);
  console.log(`Built ./dist/index.js`);

  await esbuild.build({
    ...esbuildConfig,
    format: 'esm',
    outExtension: { '.js': '.mjs' },
  });
  console.log(`Built ./dist/index.mjs`);

  // tsup is used to emit d.ts files only (esbuild can't do that).
  //
  // Notes:
  // 1. Emitting d.ts files is super slow for whatever reason.
  // 2. It could have fully replaced esbuild (as it uses that internally),
  //    but at the moment its esbuild version is somewhat outdated.
  //    It's also harder to configure and esbuild docs are more thorough.
  await tsup.build({
    entry: [file],
    format: ['cjs', 'esm'],
    dts: { only: true },
    outDir: dist,
    silent: true,
  });
  console.log(`Built ./dist/index.d.ts`);
}
build();