"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// schemas/index.ts
var schemas_exports = {};
__export(schemas_exports, {
  EnumIsoCode: () => EnumIsoCode,
  getCollectionNameByIsoCode: () => getCollectionNameByIsoCode,
  getFieldNameByIsoCode: () => getFieldNameByIsoCode,
  getFinancialAccountByIsoCode: () => getFinancialAccountByIsoCode,
  getModels: () => getModels
});
module.exports = __toCommonJS(schemas_exports);

// schemas/isoCode/my/bEmployee.server.ts
var import_mongo_connection = require("mongo-connection");
var import_mongoose = require("mongoose");
var bEmployeeName = "my_bEmployee";
var BEmployeeSchema = new import_mongoose.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName }
);
var BEmployeeModel = import_mongo_connection.mongoClientApp.model(bEmployeeName, BEmployeeSchema);
var bEmployee = BEmployeeModel;

// schemas/isoCode/my/bEmployeeSetting.server.ts
var import_mongo_connection2 = require("mongo-connection");
var import_mongoose2 = require("mongoose");
var bEmployeeSettingName = "my_bEmployeeSetting";
var BEmployeeSettingSchema = new import_mongoose2.Schema(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName }
);
var BEmployeeSettingModel = import_mongo_connection2.mongoClientApp.model(
  bEmployeeSettingName,
  BEmployeeSettingSchema
);
var bEmployeeSetting = BEmployeeSettingModel;

// schemas/isoCode/my/bundleVoucher.ts
var import_mongo_connection3 = require("mongo-connection");
var import_mongoose3 = require("mongoose");
var bundleVoucherName = "my_bundleVoucher";
var BundleVoucherSchema = new import_mongoose3.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose3.Schema.Types.Mixed },
            nextData: { $type: import_mongoose3.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose3.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName }
);
var BundleVoucherModel = import_mongo_connection3.mongoClientApp.model(
  bundleVoucherName,
  BundleVoucherSchema
);
var bundleVoucher = BundleVoucherModel;

// schemas/isoCode/my/campaign-payment.server.ts
var import_mongo_connection4 = require("mongo-connection");
var import_mongoose4 = require("mongoose");
var marketingCampaignPaymentMethodName = "my_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema = new import_mongoose4.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: import_mongoose4.Schema.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName }
);
var MarketingCampaignPaymentMethodModel = import_mongo_connection4.mongoClientApp.model(
  marketingCampaignPaymentMethodName,
  MarketingCampaignPaymentMethodSchema
);
var marketingCampaignPaymentMethod = MarketingCampaignPaymentMethodModel;

// schemas/isoCode/my/campaign.server.ts
var import_mongo_connection5 = require("mongo-connection");
var import_mongoose5 = require("mongoose");
var marketingCampaignName = "my_marketingCampaign";
var MarketingCampaignSchema = new import_mongoose5.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: import_mongoose5.Schema.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: import_mongoose5.Schema.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose5.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose5.Schema.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose5.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose5.Schema.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName }
);
var MarketingCampaignModel = import_mongo_connection5.mongoClientApp.model(
  marketingCampaignName,
  MarketingCampaignSchema
);
var marketingCampaign = MarketingCampaignModel;

// schemas/isoCode/my/comboVoucher.server.ts
var import_mongo_connection6 = require("mongo-connection");
var import_mongoose6 = require("mongoose");
var comboVoucherName = "my_comboVoucher";
var ComboVoucherSchema = new import_mongoose6.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName }
);
var ComboVoucherModel = import_mongo_connection6.mongoClientApp.model(
  "my_comboVoucher",
  ComboVoucherSchema
);
var comboVoucher = ComboVoucherModel;

// schemas/isoCode/my/communityComment.server.ts
var import_mongo_connection7 = require("mongo-connection");
var import_mongoose7 = require("mongoose");
var communityCommentName = "my_communityComment";
var CommunityCommentSchema = new import_mongoose7.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName }
);
var CommunityCommentModel = import_mongo_connection7.mongoClientApp.model(
  communityCommentName,
  CommunityCommentSchema
);
var communityComment = CommunityCommentModel;

// schemas/isoCode/my/communityMedal.server.ts
var import_mongo_connection8 = require("mongo-connection");
var import_mongoose8 = require("mongoose");
var communityMedalName = "my_communityMedal";
var CommunityMedalSchema = new import_mongoose8.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: import_mongoose8.Schema.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: import_mongoose8.Schema.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: import_mongoose8.Schema.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName }
);
var CommunityMedalModel = import_mongo_connection8.mongoClientApp.model(
  communityMedalName,
  CommunityMedalSchema
);
var communityMedal = CommunityMedalModel;

// schemas/isoCode/my/communityNotification.server.ts
var import_mongo_connection9 = require("mongo-connection");
var import_mongoose9 = require("mongoose");
var communityNotificationName = "my_communityNotification";
var CommunityNotificationSchema = new import_mongoose9.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: import_mongoose9.Schema.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName }
);
var CommunityNotificationModel = import_mongo_connection9.mongoClientApp.model(
  communityNotificationName,
  CommunityNotificationSchema
);
var communityNotification = CommunityNotificationModel;

// schemas/isoCode/my/communityOrderTag.server.ts
var import_mongo_connection10 = require("mongo-connection");
var import_mongoose10 = require("mongoose");
var communityTagOrderName = "my_communityTagOrder";
var CommunityTagOrderSchema = new import_mongoose10.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: import_mongoose10.Schema.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: import_mongoose10.Schema.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName }
);
var CommunityTagOrderModel = import_mongo_connection10.mongoClientApp.model(
  communityTagOrderName,
  CommunityTagOrderSchema
);
var communityTagOrder = CommunityTagOrderModel;

// schemas/isoCode/my/communityPost.server.ts
var import_mongo_connection11 = require("mongo-connection");
var import_mongoose11 = require("mongoose");
var communityPostName = "my_communityPost";
var CommunityPostSchema = new import_mongoose11.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: import_mongoose11.Schema.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityPostName }
);
var CommunityPostModel = import_mongo_connection11.mongoClientApp.model(
  communityPostName,
  CommunityPostSchema
);
var communityPost = CommunityPostModel;

// schemas/isoCode/my/communitySetting.server.ts
var import_mongo_connection12 = require("mongo-connection");
var import_mongoose12 = require("mongoose");
var communitySettingName = "my_communitySetting";
var CommunitySettingSchema = new import_mongoose12.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: import_mongoose12.Schema.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName
  }
);
var CommunitySettingModel = import_mongo_connection12.mongoClientApp.model(
  communitySettingName,
  CommunitySettingSchema
);
var communitySetting = CommunitySettingModel;

// schemas/isoCode/my/communityTag.server.ts
var import_mongo_connection13 = require("mongo-connection");
var import_mongoose13 = require("mongoose");
var communityTagName = "my_communityTag";
var CommunityTagSchema = new import_mongoose13.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: import_mongoose13.Schema.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: import_mongoose13.Schema.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName }
);
var CommunityTagModel = import_mongo_connection13.mongoClientApp.model(
  communityTagName,
  CommunityTagSchema
);
var communityTag = CommunityTagModel;

// schemas/isoCode/my/communityUser.server.ts
var import_mongo_connection14 = require("mongo-connection");
var import_mongoose14 = require("mongoose");
var communityUserName = "my_communityUser";
var CommunityUserSchema = new import_mongoose14.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName }
);
var CommunityUserModel = import_mongo_connection14.mongoClientApp.model(
  communityUserName,
  CommunityUserSchema
);
var communityUser = CommunityUserModel;

// schemas/isoCode/my/communityUserReport.server.ts
var import_mongo_connection15 = require("mongo-connection");
var import_mongoose15 = require("mongoose");
var communityUserReportName = "my_communityUserReport";
var CommunityUserReportSchema = new import_mongoose15.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName }
);
var CommunityUserReportModel = import_mongo_connection15.mongoClientApp.model(
  communityUserReportName,
  CommunityUserReportSchema
);
var communityUserReport = CommunityUserReportModel;

// schemas/isoCode/my/employeeProfile.server.ts
var import_mongo_connection16 = require("mongo-connection");
var import_mongoose16 = require("mongoose");
var employeeProfileName = "my_employeeProfile";
var EmployeeProfileSchema = new import_mongoose16.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName }
);
var EmployeeProfileModel = import_mongo_connection16.mongoClientApp.model(
  employeeProfileName,
  EmployeeProfileSchema
);
var employeeProfile = EmployeeProfileModel;

// schemas/isoCode/my/FATransaction.server.ts
var import_mongo_connection17 = require("mongo-connection");
var import_mongoose17 = require("mongoose");
var FATransactionName = "my_FATransaction";
var FATransactionSchema = new import_mongoose17.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName }
);
var FATransactionModel = import_mongo_connection17.mongoClientApp.model(
  "my_FATransaction",
  FATransactionSchema
);
var FATransaction = FATransactionModel;

// schemas/isoCode/my/financialAccount.server.ts
var import_mongoose18 = require("mongoose");
var import_mongo_connection18 = require("mongo-connection");
var financialAccountName = "my_financialAccount";
var FinancialAccountSchema = new import_mongoose18.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName }
);
var FinancialAccountModel = import_mongo_connection18.mongoClientApp.model(
  "my_financialAccount",
  FinancialAccountSchema
);
var financialAccount = FinancialAccountModel;

// schemas/isoCode/my/flashSale.server.ts
var import_mongo_connection19 = require("mongo-connection");
var import_mongoose19 = require("mongoose");
var flashSaleName = "my_askerFlashSaleIncentive";
var FlashSaleSchema = new import_mongoose19.Schema(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName }
);
var FlashSaleModel = import_mongo_connection19.mongoClientApp.model(
  "my_askerFlashSaleIncentive",
  FlashSaleSchema
);
var flashSale = FlashSaleModel;

// schemas/isoCode/my/historyTasks.server.ts
var import_mongo_connection20 = require("mongo-connection");
var import_mongoose20 = require("mongoose");
var historyTasksName = "my_history_tasks";
var HistoryTasksSchema = new import_mongoose20.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose20.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose20.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose20.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose20.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName }
);
var HistoryTasksModel = import_mongo_connection20.mongoClientApp.model("my_history_tasks", HistoryTasksSchema);
var historyTasks = HistoryTasksModel;

// schemas/isoCode/my/incentive.server.ts
var import_mongo_connection21 = require("mongo-connection");
var import_mongoose21 = require("mongoose");
var incentiveName = "my_incentive";
var IncentiveSchema = new import_mongoose21.Schema(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName }
);
var IncentiveModel = import_mongo_connection21.mongoClientApp.model(incentiveName, IncentiveSchema);
var incentive = IncentiveModel;

// schemas/isoCode/my/journeySetting.server.ts
var import_mongo_connection22 = require("mongo-connection");
var import_mongoose22 = require("mongoose");
var journeySettingName = "my_journeySetting";
var JourneySettingSchema = new import_mongoose22.Schema(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName }
);
var JourneySettingModel = import_mongo_connection22.mongoClientApp.model(
  journeySettingName,
  JourneySettingSchema
);
var journeySetting = JourneySettingModel;

// schemas/isoCode/my/notification.server.ts
var import_mongo_connection23 = require("mongo-connection");
var import_mongoose23 = require("mongoose");
var notificationName = "my_notification";
var NotificationSchema = new import_mongoose23.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName }
);
var NotificationModel = import_mongo_connection23.mongoClientApp.model(
  notificationName,
  NotificationSchema
);
var notification = NotificationModel;

// schemas/isoCode/my/partnerBusiness.server.ts
var import_mongo_connection24 = require("mongo-connection");
var import_mongoose24 = require("mongoose");
var businessName = "my_business";
var BusinessSchema = new import_mongoose24.Schema(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [{ name: { $type: String }, url: { $type: String } }],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose24.Schema.Types.Mixed },
            nextData: { $type: import_mongoose24.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose24.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName }
);
var BusinessModel = import_mongo_connection24.mongoClientApp.model(businessName, BusinessSchema);
var business = BusinessModel;

// schemas/isoCode/my/partnerBusinessLevel.server.ts
var import_mongoose25 = require("mongoose");
var import_mongo_connection25 = require("mongo-connection");
var businessLevelName = "my_businessLevel";
var BusinessLevelSchema = new import_mongoose25.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName }
);
var BusinessLevelModel = import_mongo_connection25.mongoClientApp.model(
  businessLevelName,
  BusinessLevelSchema
);
var businessLevel = BusinessLevelModel;

// schemas/isoCode/my/partnerBusinessMember.server.ts
var import_mongoose26 = require("mongoose");
var import_mongo_connection26 = require("mongo-connection");
var businessMemberName = "my_businessMember";
var BusinessMemberSchema = new import_mongoose26.Schema(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName }
);
var BusinessMemberModel = import_mongo_connection26.mongoClientApp.model(businessMemberName, BusinessMemberSchema);
var businessMember = BusinessMemberModel;

// schemas/isoCode/my/partnerBusinessMemberTransaction.server.ts
var import_mongo_connection27 = require("mongo-connection");
var import_mongoose27 = require("mongoose");
var businessMemberTransactionName = "my_businessMemberTransaction";
var BusinessMemberTransactionSchema = new import_mongoose27.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    taskId: { $type: String, required: true },
    userId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName }
);
var BusinessMemberTransactionModel = import_mongo_connection27.mongoClientApp.model(businessMemberTransactionName, BusinessMemberTransactionSchema);
var businessMemberTransaction = BusinessMemberTransactionModel;

// schemas/isoCode/my/partnerBusinessSetupAllocateAndReallocate.server.ts
var import_mongo_connection28 = require("mongo-connection");
var import_mongoose28 = require("mongoose");
var businessSetupAllocationAndReallocationName = "my_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema = new import_mongoose28.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName }
);
var BusinessSetupAllocationAndReallocationModel = import_mongo_connection28.mongoClientApp.model(businessSetupAllocationAndReallocationName, BusinessSetupAllocationAndReallocationSchema);
var businessSetupAllocationAndReallocation = BusinessSetupAllocationAndReallocationModel;

// schemas/isoCode/my/partnerBusinessTransaction.server.ts
var import_mongo_connection29 = require("mongo-connection");
var import_mongoose29 = require("mongoose");
var businessTransactionName = "my_businessTransaction";
var BusinessTransactionSchema = new import_mongoose29.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName }
);
var BusinessTransactionModel = import_mongo_connection29.mongoClientApp.model(
  businessTransactionName,
  BusinessTransactionSchema
);
var businessTransaction = BusinessTransactionModel;

// schemas/isoCode/my/partnerDirectory.server.ts
var import_mongoose30 = require("mongoose");
var import_mongo_connection30 = require("mongo-connection");
var partnerDirectoryName = "my_partnerDirectory";
var PartnerDirectorySchema = new import_mongoose30.Schema(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName }
);
var PartnerDirectoryModel = import_mongo_connection30.mongoClientApp.model(
  partnerDirectoryName,
  PartnerDirectorySchema
);
var partnerDirectory = PartnerDirectoryModel;

// schemas/isoCode/my/partnerRequest.server.ts
var import_mongoose31 = require("mongoose");
var import_mongo_connection31 = require("mongo-connection");
var partnerRequestName = "my_partnerRequest";
var PartnerRequestSchema = new import_mongoose31.Schema(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName }
);
var PartnerRequestModel = import_mongo_connection31.mongoClientApp.model(
  partnerRequestName,
  PartnerRequestSchema
);
var partnerRequest = PartnerRequestModel;

// schemas/isoCode/my/paymentToolKitTransaction.server.ts
var import_mongo_connection32 = require("mongo-connection");
var import_mongoose32 = require("mongoose");
var paymentToolKitTransactionName = "my_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema = new import_mongoose32.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName }
);
var PaymentToolKitTransactionModel = import_mongo_connection32.mongoClientApp.model(
  paymentToolKitTransactionName,
  PaymentToolKitTransactionSchema
);
var paymentToolKitTransaction = PaymentToolKitTransactionModel;

// schemas/isoCode/my/promotionCode.server.ts
var import_mongo_connection33 = require("mongo-connection");
var import_mongoose33 = require("mongoose");
var promotionCodeName = "my_promotionCode";
var PromotionCodeSchema = new import_mongoose33.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: import_mongoose33.Schema.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName }
);
var PromotionCodeModel = import_mongo_connection33.mongoClientApp.model(
  promotionCodeName,
  PromotionCodeSchema
);
var promotionCode = PromotionCodeModel;

// schemas/isoCode/my/promotionHistory.server.ts
var import_mongo_connection34 = require("mongo-connection");
var import_mongoose34 = require("mongoose");
var promotionHistoryName = "my_promotionHistory";
var PromotionHistorySchema = new import_mongoose34.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName }
);
var PromotionHistoryModel = import_mongo_connection34.mongoClientApp.model(
  promotionHistoryName,
  PromotionHistorySchema
);
var promotionHistory = PromotionHistoryModel;

// schemas/isoCode/my/promotionSource.server.ts
var import_mongoose35 = require("mongoose");
var import_mongo_connection35 = require("mongo-connection");
var promotionSourceName = "my_promotionSource";
var PromotionSourceSchema = new import_mongoose35.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName }
);
var PromotionSourceModel = import_mongo_connection35.mongoClientApp.model(
  promotionSourceName,
  PromotionSourceSchema
);
var promotionSource = PromotionSourceModel;

// schemas/isoCode/my/rating.server.ts
var import_mongo_connection36 = require("mongo-connection");
var import_mongoose36 = require("mongoose");
var ratingName = "my_rating";
var RatingSchema = new import_mongoose36.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName }
);
var RatingModel = import_mongo_connection36.mongoClientApp.model(ratingName, RatingSchema);
var rating = RatingModel;

// schemas/isoCode/my/referralCampaign.server.ts
var import_mongo_connection37 = require("mongo-connection");
var import_mongoose37 = require("mongoose");
var referralCampaignName = "my_askerReferralCampaign";
var ReferralCampaignSchema = new import_mongoose37.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: import_mongoose37.Schema.Types.Mixed, required: true },
    inviter: { $type: import_mongoose37.Schema.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName }
);
var ReferralCampaignModel = import_mongo_connection37.mongoClientApp.model(
  referralCampaignName,
  ReferralCampaignSchema
);
var referralCampaign = ReferralCampaignModel;

// schemas/isoCode/my/service.server.ts
var import_mongo_connection38 = require("mongo-connection");
var import_mongoose38 = require("mongoose");
var serviceName = "my_service";
var ServiceSchema = new import_mongoose38.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: import_mongoose38.Schema.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: import_mongoose38.Schema.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: import_mongoose38.Schema.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName }
);
var ServiceModel = import_mongo_connection38.mongoClientApp.model(serviceName, ServiceSchema);
var service = ServiceModel;

// schemas/isoCode/my/serviceChannel.server.ts
var import_mongo_connection39 = require("mongo-connection");
var import_mongoose39 = require("mongoose");
var serviceChannelName = "my_serviceChannel";
var ServiceChannelSchema = new import_mongoose39.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName }
);
var ServiceChannelModel = import_mongo_connection39.mongoClientApp.model(
  serviceChannelName,
  ServiceChannelSchema
);
var serviceChannel = ServiceChannelModel;

// schemas/isoCode/my/settingCountry.server.ts
var import_mongo_connection40 = require("mongo-connection");
var import_mongoose40 = require("mongoose");
var settingCountryName = "my_settingCountry";
var SettingCountrySchema = new import_mongoose40.Schema(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName }
);
var SettingCountryModel = import_mongo_connection40.mongoClientApp.model(
  settingCountryName,
  SettingCountrySchema
);
var settingCountry = SettingCountryModel;

// schemas/isoCode/my/settingSystem.server.ts
var import_mongo_connection41 = require("mongo-connection");
var import_mongoose41 = require("mongoose");
var settingSystemName = "my_settingSystem";
var SettingSystemSchema = new import_mongoose41.Schema(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: import_mongoose41.Schema.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName }
);
var SettingSystemModel = import_mongo_connection41.mongoClientApp.model(
  "my_settingSystem",
  SettingSystemSchema
);
var settingSystem = SettingSystemModel;

// schemas/isoCode/my/subscription.server.ts
var import_mongo_connection42 = require("mongo-connection");
var import_mongoose42 = require("mongoose");
var subscriptionName = "my_subscription";
var SubscriptionSchema = new import_mongoose42.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: import_mongoose42.Schema.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: import_mongoose42.Schema.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName }
);
var SubscriptionModel = import_mongo_connection42.mongoClientApp.model(subscriptionName, SubscriptionSchema);
var subscription = SubscriptionModel;

// schemas/isoCode/my/task.server.ts
var import_mongo_connection43 = require("mongo-connection");
var import_mongoose43 = require("mongoose");
var taskName = "my_task";
var TasksSchema = new import_mongoose43.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose43.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose43.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose43.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose43.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: taskName }
);
var TasksModel = import_mongo_connection43.mongoClientApp.model("my_tasks", TasksSchema);
var task = TasksModel;

// schemas/isoCode/my/taskerBNPLProcess.server.ts
var import_mongo_connection44 = require("mongo-connection");
var import_mongoose44 = require("mongoose");
var taskerBNPLProcessName = "my_taskerBNPLProcess";
var TaskerBNPLProcess = new import_mongoose44.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName }
);
var TaskerBNPLProcessModel = import_mongo_connection44.mongoClientApp.model(
  taskerBNPLProcessName,
  TaskerBNPLProcess
);
var taskerBNPLProcess = TaskerBNPLProcessModel;

// schemas/isoCode/my/taskerBNPLTransaction.server.ts
var import_mongo_connection45 = require("mongo-connection");
var import_mongoose45 = require("mongoose");
var taskerBNPLTransactionName = "my_taskerBNPLTransaction";
var TaskerBNPLTransaction = new import_mongoose45.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName }
);
var TaskerBNPLTransactionModel = import_mongo_connection45.mongoClientApp.model(
  taskerBNPLTransactionName,
  TaskerBNPLTransaction
);
var taskerBNPLTransaction = TaskerBNPLTransactionModel;

// schemas/isoCode/my/taskerGift.server.ts
var import_mongoose46 = require("mongoose");
var import_mongo_connection46 = require("mongo-connection");
var taskerGiftName = "my_taskerGift";
var TaskerGiftSchema = new import_mongoose46.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName }
);
var TaskerGiftModel = import_mongo_connection46.mongoClientApp.model(taskerGiftName, TaskerGiftSchema);
var taskerGift = TaskerGiftModel;

// schemas/isoCode/my/taskerIncentive.server.ts
var import_mongoose47 = require("mongoose");
var import_mongo_connection47 = require("mongo-connection");
var taskerIncentiveName = "my_taskerIncentive";
var TaskerIncentiveSchema = new import_mongoose47.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: import_mongoose47.Schema.Types.Mixed
    },
    status: {
      $type: String
    },
    codeFromPartner: {
      $type: Number
    },
    giftInfo: import_mongoose47.Schema.Types.Mixed,
    social: import_mongoose47.Schema.Types.Mixed,
    codeList: import_mongoose47.Schema.Types.Mixed,
    office: import_mongoose47.Schema.Types.Mixed,
    applyFor: import_mongoose47.Schema.Types.Mixed,
    brandInfo: import_mongoose47.Schema.Types.Mixed,
    redeemLink: import_mongoose47.Schema.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName }
);
var TaskerIncentiveModel = import_mongo_connection47.mongoClientApp.model(
  taskerIncentiveName,
  TaskerIncentiveSchema
);
var taskerIncentive = TaskerIncentiveModel;

// schemas/isoCode/my/taskerOnboardingSetting.server.ts
var import_mongo_connection48 = require("mongo-connection");
var import_mongoose48 = require("mongoose");
var taskerOnboardingSettingName = "my_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema = new import_mongoose48.Schema(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName }
);
var TaskerOnboardingSettingModel = import_mongo_connection48.mongoClientApp.model(
  taskerOnboardingSettingName,
  TaskerOnboardingSettingSchema
);
var taskerOnboardingSetting = TaskerOnboardingSettingModel;

// schemas/isoCode/my/taskerPointTransaction.server.ts
var import_mongoose49 = require("mongoose");
var import_mongo_connection49 = require("mongo-connection");
var taskerPointTransactionName = "my_taskerPointTransaction";
var TaskerPointTransactionSchema = new import_mongoose49.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName }
);
var TaskerPointTransactionModel = import_mongo_connection49.mongoClientApp.model(
  taskerPointTransactionName,
  TaskerPointTransactionSchema
);
var taskerPointTransaction = TaskerPointTransactionModel;

// schemas/isoCode/my/taskerProfile.server.ts
var import_mongoose50 = require("mongoose");
var import_mongo_connection50 = require("mongo-connection");
var taskerProfileName = "my_taskerProfile";
var TaskerProfileSchema = new import_mongoose50.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName
  }
);
var TaskerProfileModel = import_mongo_connection50.mongoClientApp.model(
  taskerProfileName,
  TaskerProfileSchema
);
var taskerProfile = TaskerProfileModel;

// schemas/isoCode/my/taskerSpecialCampaign.server.ts
var import_mongo_connection51 = require("mongo-connection");
var import_mongoose51 = require("mongoose");
var taskerSpecialCampaignName = "my_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema = new import_mongoose51.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: import_mongoose51.Schema.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName, _id: false }
);
var TaskerSpecialCampaignModel = import_mongo_connection51.mongoClientApp.model(
  taskerSpecialCampaignName,
  TaskerSpecialCampaignSchema
);
var taskerSpecialCampaign = TaskerSpecialCampaignModel;

// schemas/isoCode/my/taskerSpecialCampaignTransaction.server.ts
var import_mongo_connection52 = require("mongo-connection");
var import_mongoose52 = require("mongoose");
var taskerSpecialCampaignTransactionName = "my_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema = new import_mongoose52.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName }
);
var TaskerSpecialCampaignTransactionModel = import_mongo_connection52.mongoClientApp.model(
  taskerSpecialCampaignTransactionName,
  TaskerSpecialCampaignTransactionSchema
);
var taskerSpecialCampaignTransaction = TaskerSpecialCampaignTransactionModel;

// schemas/isoCode/my/taskerToolkitLadingDetails.server.ts
var import_mongoose53 = require("mongoose");
var import_mongo_connection53 = require("mongo-connection");
var taskerToolkitLadingDetailsName = "my_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema = new import_mongoose53.Schema(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName }
);
var TaskerToolkitLadingDetailsModel = import_mongo_connection53.mongoClientApp.model(
  taskerToolkitLadingDetailsName,
  TaskerToolkitLadingDetailsSchema
);
var taskerToolkitLadingDetails = TaskerToolkitLadingDetailsModel;

// schemas/isoCode/my/taskerTrainingCourse.server.ts
var import_mongo_connection54 = require("mongo-connection");
var import_mongoose54 = require("mongoose");
var taskerTrainingCourseName = "my_trainingTaskerCourse";
var CourseSchema = new import_mongoose54.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName }
);
var CourseModel = import_mongo_connection54.mongoClientApp.model(
  taskerTrainingCourseName,
  CourseSchema
);
var taskerTrainingCourse = CourseModel;

// schemas/isoCode/my/taskerTrainingCourseStartDate.ts
var import_mongo_connection55 = require("mongo-connection");
var import_mongoose55 = require("mongoose");
var taskerTrainingCourseStartDateName = "my_trainingTaskerCourseStartDate";
var CourseStartDateSchema = new import_mongoose55.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName }
);
var CourseStartDateModel = import_mongo_connection55.mongoClientApp.model(
  taskerTrainingCourseStartDateName,
  CourseStartDateSchema
);
var taskerTrainingCourseStartDate = CourseStartDateModel;

// schemas/isoCode/my/taskerTrainingQuiz.server.ts
var import_mongoose56 = require("mongoose");
var import_mongo_connection56 = require("mongo-connection");
var taskerTrainingQuizName = "my_trainingTaskerQuiz";
var QuizSchema = new import_mongoose56.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName }
);
var QuizModel = import_mongo_connection56.mongoClientApp.model(taskerTrainingQuizName, QuizSchema);
var taskerTrainingQuiz = QuizModel;

// schemas/isoCode/my/taskerTrainingQuizCollection.server.ts
var import_mongoose57 = require("mongoose");
var import_mongo_connection57 = require("mongo-connection");
var taskerTrainingQuizCollectionName = "my_trainingTaskerQuizCollection";
var QuizCollectionSchema = new import_mongoose57.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName }
);
var QuizCollectionModel = import_mongo_connection57.mongoClientApp.model(
  taskerTrainingQuizCollectionName,
  QuizCollectionSchema
);
var taskerTrainingQuizCollection = QuizCollectionModel;

// schemas/isoCode/my/taskerTrainingSubmission.server.ts
var import_mongoose58 = require("mongoose");
var import_mongo_connection58 = require("mongo-connection");
var taskerTrainingSubmissionName = "my_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema = new import_mongoose58.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName }
);
var TaskerTrainingSubmissionModel = import_mongo_connection58.mongoClientApp.model(
  taskerTrainingSubmissionName,
  TaskerTrainingSubmissionSchema
);
var taskerTrainingSubmission = TaskerTrainingSubmissionModel;

// schemas/isoCode/my/thingsToKnow.server.ts
var import_mongoose59 = require("mongoose");
var import_mongo_connection59 = require("mongo-connection");
var thingsToKnowName = "my_thingsToKnow";
var ThingToKnowSchema = new import_mongoose59.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName }
);
var ThingsToKnowModel = import_mongo_connection59.mongoClientApp.model(
  thingsToKnowName,
  ThingToKnowSchema
);
var thingsToKnow = ThingsToKnowModel;

// schemas/isoCode/my/toolKitItems.server.ts
var import_mongoose60 = require("mongoose");
var import_mongo_connection60 = require("mongo-connection");
var toolKitItemsName = "my_toolKitItems";
var ToolKitItemsSchema = new import_mongoose60.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName }
);
var ToolKitItemsModel = import_mongo_connection60.mongoClientApp.model(
  toolKitItemsName,
  ToolKitItemsSchema
);
var toolKitItems = ToolKitItemsModel;

// schemas/isoCode/my/toolKitSetting.server.ts
var import_mongoose61 = require("mongoose");
var import_mongo_connection61 = require("mongo-connection");
var toolKitSettingName = "my_toolKitSetting";
var ToolKitSettingSchema = new import_mongoose61.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName }
);
var ToolKitSettingModel = import_mongo_connection61.mongoClientApp.model(
  toolKitSettingName,
  ToolKitSettingSchema
);
var toolKitSetting = ToolKitSettingModel;

// schemas/isoCode/my/trainingJourney.server.ts
var import_mongoose62 = require("mongoose");
var import_mongo_connection62 = require("mongo-connection");
var trainingJourneyName = "my_trainingJourney";
var TrainingJourneySchema = new import_mongoose62.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName }
);
var TrainingJourneyModel = import_mongo_connection62.mongoClientApp.model(
  trainingJourneyName,
  TrainingJourneySchema
);
var trainingJourney = TrainingJourneyModel;

// schemas/isoCode/my/trainingTasker.server.ts
var import_mongoose63 = require("mongoose");
var import_mongo_connection63 = require("mongo-connection");
var trainingTaskerName = "my_trainingTasker";
var TrainingTaskerSchema = new import_mongoose63.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName }
);
var TrainingTaskerModel = import_mongo_connection63.mongoClientApp.model(
  trainingTaskerName,
  TrainingTaskerSchema
);
var trainingTasker = TrainingTaskerModel;

// schemas/isoCode/my/userActivation.server.ts
var import_mongo_connection64 = require("mongo-connection");
var import_mongoose64 = require("mongoose");
var userActivationName = "my_userActivation";
var UserActivationSchema = new import_mongoose64.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName }
);
var UserActivationModel = import_mongo_connection64.mongoClientApp.model(
  userActivationName,
  UserActivationSchema
);
var userActivation = UserActivationModel;

// schemas/isoCode/my/userApp.server.ts
var import_mongo_connection65 = require("mongo-connection");
var import_mongoose65 = require("mongoose");
var usersName = "users";
var UsersAppSchema = new import_mongoose65.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName }
);
var UsersAppModel = import_mongo_connection65.mongoClientApp.model(usersName, UsersAppSchema);
var users = UsersAppModel;

// schemas/isoCode/my/userComboVoucher.server.ts
var import_mongo_connection66 = require("mongo-connection");
var import_mongoose66 = require("mongoose");
var userComboVoucherName = "my_userComboVoucher";
var UserComboVoucherSchema = new import_mongoose66.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: import_mongoose66.Schema.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: import_mongoose66.Schema.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName }
);
var UserComboVoucherModel = import_mongo_connection66.mongoClientApp.model(
  userComboVoucherName,
  UserComboVoucherSchema
);
var userComboVoucher = UserComboVoucherModel;

// schemas/isoCode/my/userLocationHistory.server.ts
var import_mongoose67 = require("mongoose");
var import_mongo_connection67 = require("mongo-connection");
var userLocationHistoryName = "my_userLocationHistory";
var UserLocationHistorySchema = new import_mongoose67.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName }
);
var UserLocationHistoryModel = import_mongo_connection67.mongoClientApp.model(
  userLocationHistoryName,
  UserLocationHistorySchema
);
var userLocationHistory = UserLocationHistoryModel;

// schemas/isoCode/my/userProfile.server.ts
var import_mongo_connection68 = require("mongo-connection");
var import_mongoose68 = require("mongoose");
var userProfileName = "my_userProfile";
var UserProfileSchema = new import_mongoose68.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: import_mongoose68.Schema.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName }
);
var UserProfileModel = import_mongo_connection68.mongoClientApp.model(userProfileName, UserProfileSchema);
var userProfile = UserProfileModel;

// schemas/isoCode/my/workingPlaces.server.ts
var import_mongoose69 = require("mongoose");
var import_mongo_connection69 = require("mongo-connection");
var workingPlacesName = "my_workingPlaces";
var WorkingPlacesSchema = new import_mongoose69.Schema(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName }
);
var WorkingPlacesModel = import_mongo_connection69.mongoClientApp.model(
  workingPlacesName,
  WorkingPlacesSchema
);
var workingPlaces = WorkingPlacesModel;

// schemas/isoCode/my/index.server.ts
var MY = {
  task,
  historyTasks,
  userLocationHistory,
  trainingTasker,
  trainingJourney,
  toolKitSetting,
  toolKitItems,
  thingsToKnow,
  taskerTrainingSubmission,
  taskerTrainingQuizCollection,
  taskerTrainingQuiz,
  taskerTrainingCourse,
  taskerToolkitLadingDetails,
  financialAccount,
  taskerIncentive,
  taskerPointTransaction,
  taskerGift,
  notification,
  employeeProfile,
  workingPlaces,
  taskerProfile,
  flashSale,
  incentive,
  promotionCode,
  promotionSource,
  service,
  promotionHistory,
  settingCountry,
  comboVoucher,
  referralCampaign,
  marketingCampaign,
  settingSystem,
  partnerRequest,
  partnerDirectory,
  communityComment,
  communityMedal,
  communityNotification,
  communityPost,
  communitySetting,
  communityTag,
  communityUser,
  communityUserReport,
  subscription,
  taskerOnboardingSetting,
  users,
  serviceChannel,
  bEmployee,
  bEmployeeSetting,
  FATransaction,
  taskerBNPLTransaction,
  taskerBNPLProcess,
  paymentToolKitTransaction,
  rating,
  userActivation,
  taskerTrainingCourseStartDate,
  business,
  businessLevel,
  businessMember,
  businessMemberTransaction,
  businessTransaction,
  businessSetupAllocationAndReallocation,
  taskerSpecialCampaign,
  taskerSpecialCampaignTransaction,
  marketingCampaignPaymentMethod,
  userProfile,
  communityTagOrder,
  journeySetting,
  userComboVoucher,
  bundleVoucher
};
var MYName = {
  trainingTasker: trainingTaskerName,
  historyTasks: historyTasksName,
  userLocationHistory: userLocationHistoryName,
  trainingJourney: trainingJourneyName,
  toolKitSetting: toolKitSettingName,
  toolKitItems: toolKitItemsName,
  thingsToKnow: thingsToKnowName,
  taskerTrainingSubmission: taskerTrainingSubmissionName,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName,
  taskerTrainingQuiz: taskerTrainingQuizName,
  taskerTrainingCourse: taskerTrainingCourseName,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName,
  taskerPointTransaction: taskerPointTransactionName,
  taskerIncentive: taskerIncentiveName,
  taskerGift: taskerGiftName,
  notification: notificationName,
  financialAccount: financialAccountName,
  taskerProfile: taskerProfileName,
  employeeProfile: employeeProfileName,
  task: taskName,
  workingPlaces: workingPlacesName,
  service: serviceName,
  settingCountry: settingCountryName,
  settingSystem: settingSystemName,
  incentive: incentiveName,
  communityComment: communityCommentName,
  communityMedal: communityMedalName,
  communityNotification: communityNotificationName,
  communityPost: communityPostName,
  communitySetting: communitySettingName,
  communityTag: communityTagName,
  communityUser: communityUserName,
  communityUserReport: communityUserReportName,
  subscription: subscriptionName,
  taskerOnboardingSetting: taskerOnboardingSettingName,
  users: usersName,
  serviceChannel: serviceChannelName,
  bEmployee: bEmployeeName,
  bEmployeeSetting: bEmployeeSettingName,
  FATransaction: FATransactionName,
  taskerBNPLTransaction: taskerBNPLTransactionName,
  taskerBNPLProcess: taskerBNPLProcessName,
  paymentToolKitTransaction: paymentToolKitTransactionName,
  promotionCode: promotionCodeName,
  promotionHistory: promotionHistoryName,
  marketingCampaign: marketingCampaignName,
  rating: ratingName,
  userActivation: userActivationName,
  comboVoucher: comboVoucherName,
  flashSale: flashSaleName,
  partnerDirectory: partnerDirectoryName,
  partnerRequest: partnerRequestName,
  promotionSource: promotionSourceName,
  referralCampaign: referralCampaignName,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName,
  business: businessName,
  businessLevel: businessLevelName,
  businessMember: businessMemberName,
  businessMemberTransaction: businessMemberTransactionName,
  businessTransaction: businessTransactionName,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName,
  taskerSpecialCampaign: taskerSpecialCampaignName,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName,
  userProfile: userProfileName,
  communityTagOrder: communityTagOrderName,
  journeySetting: journeySettingName,
  userComboVoucher: userComboVoucherName,
  bundleVoucher: bundleVoucherName
};
var index_server_default = MY;

// schemas/isoCode/id/bEmployee.server.ts
var import_mongo_connection70 = require("mongo-connection");
var import_mongoose70 = require("mongoose");
var bEmployeeName2 = "id_bEmployee";
var BEmployeeSchema2 = new import_mongoose70.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName2 }
);
var BEmployeeModel2 = import_mongo_connection70.mongoClientApp.model(bEmployeeName2, BEmployeeSchema2);
var bEmployee2 = BEmployeeModel2;

// schemas/isoCode/id/bEmployeeSetting.server.ts
var import_mongo_connection71 = require("mongo-connection");
var import_mongoose71 = require("mongoose");
var bEmployeeSettingName2 = "id_bEmployeeSetting";
var BEmployeeSettingSchema2 = new import_mongoose71.Schema(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName2 }
);
var BEmployeeSettingModel2 = import_mongo_connection71.mongoClientApp.model(
  bEmployeeSettingName2,
  BEmployeeSettingSchema2
);
var bEmployeeSetting2 = BEmployeeSettingModel2;

// schemas/isoCode/id/bundleVoucher.ts
var import_mongo_connection72 = require("mongo-connection");
var import_mongoose72 = require("mongoose");
var bundleVoucherName2 = "id_bundleVoucher";
var BundleVoucherSchema2 = new import_mongoose72.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose72.Schema.Types.Mixed },
            nextData: { $type: import_mongoose72.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose72.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName2 }
);
var BundleVoucherModel2 = import_mongo_connection72.mongoClientApp.model(
  bundleVoucherName2,
  BundleVoucherSchema2
);
var bundleVoucher2 = BundleVoucherModel2;

// schemas/isoCode/id/campaign-payment.server.ts
var import_mongo_connection73 = require("mongo-connection");
var import_mongoose73 = require("mongoose");
var marketingCampaignPaymentMethodName2 = "id_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema2 = new import_mongoose73.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: import_mongoose73.Schema.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName2 }
);
var MarketingCampaignPaymentMethodModel2 = import_mongo_connection73.mongoClientApp.model(
  marketingCampaignPaymentMethodName2,
  MarketingCampaignPaymentMethodSchema2
);
var marketingCampaignPaymentMethod2 = MarketingCampaignPaymentMethodModel2;

// schemas/isoCode/id/campaign.server.ts
var import_mongo_connection74 = require("mongo-connection");
var import_mongoose74 = require("mongoose");
var marketingCampaignName2 = "id_marketingCampaign";
var MarketingCampaignSchema2 = new import_mongoose74.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: import_mongoose74.Schema.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: import_mongoose74.Schema.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose74.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose74.Schema.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose74.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose74.Schema.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName2 }
);
var MarketingCampaignModel2 = import_mongo_connection74.mongoClientApp.model(
  marketingCampaignName2,
  MarketingCampaignSchema2
);
var marketingCampaign2 = MarketingCampaignModel2;

// schemas/isoCode/id/comboVoucher.server.ts
var import_mongo_connection75 = require("mongo-connection");
var import_mongoose75 = require("mongoose");
var comboVoucherName2 = "id_comboVoucher";
var ComboVoucherSchema2 = new import_mongoose75.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName2 }
);
var ComboVoucherModel2 = import_mongo_connection75.mongoClientApp.model(
  "id_comboVoucher",
  ComboVoucherSchema2
);
var comboVoucher2 = ComboVoucherModel2;

// schemas/isoCode/id/communityComment.server.ts
var import_mongo_connection76 = require("mongo-connection");
var import_mongoose76 = require("mongoose");
var communityCommentName2 = "id_communityComment";
var CommunityCommentSchema2 = new import_mongoose76.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName2 }
);
var CommunityCommentModel2 = import_mongo_connection76.mongoClientApp.model(
  communityCommentName2,
  CommunityCommentSchema2
);
var communityComment2 = CommunityCommentModel2;

// schemas/isoCode/id/communityMedal.server.ts
var import_mongo_connection77 = require("mongo-connection");
var import_mongoose77 = require("mongoose");
var communityMedalName2 = "id_communityMedal";
var CommunityMedalSchema2 = new import_mongoose77.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: import_mongoose77.Schema.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: import_mongoose77.Schema.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: import_mongoose77.Schema.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName2 }
);
var CommunityMedalModel2 = import_mongo_connection77.mongoClientApp.model(
  communityMedalName2,
  CommunityMedalSchema2
);
var communityMedal2 = CommunityMedalModel2;

// schemas/isoCode/id/communityNotification.server.ts
var import_mongo_connection78 = require("mongo-connection");
var import_mongoose78 = require("mongoose");
var communityNotificationName2 = "id_communityNotification";
var CommunityNotificationSchema2 = new import_mongoose78.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: import_mongoose78.Schema.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName2 }
);
var CommunityNotificationModel2 = import_mongo_connection78.mongoClientApp.model(
  communityNotificationName2,
  CommunityNotificationSchema2
);
var communityNotification2 = CommunityNotificationModel2;

// schemas/isoCode/id/communityOrderTag.server.ts
var import_mongo_connection79 = require("mongo-connection");
var import_mongoose79 = require("mongoose");
var communityTagOrderName2 = "id_communityTagOrder";
var CommunityTagOrderSchema2 = new import_mongoose79.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: import_mongoose79.Schema.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: import_mongoose79.Schema.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName2 }
);
var CommunityTagOrderModel2 = import_mongo_connection79.mongoClientApp.model(
  communityTagOrderName2,
  CommunityTagOrderSchema2
);
var communityTagOrder2 = CommunityTagOrderModel2;

// schemas/isoCode/id/communityPost.server.ts
var import_mongo_connection80 = require("mongo-connection");
var import_mongoose80 = require("mongoose");
var communityPostName2 = "id_communityPost";
var CommunityPostSchema2 = new import_mongoose80.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: import_mongoose80.Schema.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityPostName2 }
);
var CommunityPostModel2 = import_mongo_connection80.mongoClientApp.model(
  communityPostName2,
  CommunityPostSchema2
);
var communityPost2 = CommunityPostModel2;

// schemas/isoCode/id/communitySetting.server.ts
var import_mongo_connection81 = require("mongo-connection");
var import_mongoose81 = require("mongoose");
var communitySettingName2 = "id_communitySetting";
var CommunitySettingSchema2 = new import_mongoose81.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: import_mongoose81.Schema.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName2
  }
);
var CommunitySettingModel2 = import_mongo_connection81.mongoClientApp.model(
  communitySettingName2,
  CommunitySettingSchema2
);
var communitySetting2 = CommunitySettingModel2;

// schemas/isoCode/id/communityTag.server.ts
var import_mongo_connection82 = require("mongo-connection");
var import_mongoose82 = require("mongoose");
var communityTagName2 = "id_communityTag";
var CommunityTagSchema2 = new import_mongoose82.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: import_mongoose82.Schema.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: import_mongoose82.Schema.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName2 }
);
var CommunityTagModel2 = import_mongo_connection82.mongoClientApp.model(
  communityTagName2,
  CommunityTagSchema2
);
var communityTag2 = CommunityTagModel2;

// schemas/isoCode/id/communityUser.server.ts
var import_mongo_connection83 = require("mongo-connection");
var import_mongoose83 = require("mongoose");
var communityUserName2 = "id_communityUser";
var CommunityUserSchema2 = new import_mongoose83.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName2 }
);
var CommunityUserModel2 = import_mongo_connection83.mongoClientApp.model(
  communityUserName2,
  CommunityUserSchema2
);
var communityUser2 = CommunityUserModel2;

// schemas/isoCode/id/communityUserReport.server.ts
var import_mongo_connection84 = require("mongo-connection");
var import_mongoose84 = require("mongoose");
var communityUserReportName2 = "id_communityUserReport";
var CommunityUserReportSchema2 = new import_mongoose84.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName2 }
);
var CommunityUserReportModel2 = import_mongo_connection84.mongoClientApp.model(
  communityUserReportName2,
  CommunityUserReportSchema2
);
var communityUserReport2 = CommunityUserReportModel2;

// schemas/isoCode/id/employeeProfile.server.ts
var import_mongo_connection85 = require("mongo-connection");
var import_mongoose85 = require("mongoose");
var employeeProfileName2 = "id_employeeProfile";
var EmployeeProfileSchema2 = new import_mongoose85.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName2 }
);
var EmployeeProfileModel2 = import_mongo_connection85.mongoClientApp.model(
  employeeProfileName2,
  EmployeeProfileSchema2
);
var employeeProfile2 = EmployeeProfileModel2;

// schemas/isoCode/id/FATransaction.server.ts
var import_mongo_connection86 = require("mongo-connection");
var import_mongoose86 = require("mongoose");
var FATransactionName2 = "id_FATransaction";
var FATransactionSchema2 = new import_mongoose86.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName2 }
);
var FATransactionModel2 = import_mongo_connection86.mongoClientApp.model(
  "id_FATransaction",
  FATransactionSchema2
);
var FATransaction2 = FATransactionModel2;

// schemas/isoCode/id/financialAccount.server.ts
var import_mongoose87 = require("mongoose");
var import_mongo_connection87 = require("mongo-connection");
var financialAccountName2 = "id_financialAccount";
var FinancialAccountSchema2 = new import_mongoose87.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName2 }
);
var FinancialAccountModel2 = import_mongo_connection87.mongoClientApp.model(
  "id_financialAccount",
  FinancialAccountSchema2
);
var financialAccount2 = FinancialAccountModel2;

// schemas/isoCode/id/flashSale.server.ts
var import_mongo_connection88 = require("mongo-connection");
var import_mongoose88 = require("mongoose");
var flashSaleName2 = "id_askerFlashSaleIncentive";
var FlashSaleSchema2 = new import_mongoose88.Schema(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName2 }
);
var FlashSaleModel2 = import_mongo_connection88.mongoClientApp.model(
  "id_askerFlashSaleIncentive",
  FlashSaleSchema2
);
var flashSale2 = FlashSaleModel2;

// schemas/isoCode/id/historyTasks.server.ts
var import_mongo_connection89 = require("mongo-connection");
var import_mongoose89 = require("mongoose");
var historyTasksName2 = "id_history_tasks";
var HistoryTasksSchema2 = new import_mongoose89.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose89.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose89.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose89.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose89.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName2 }
);
var HistoryTasksModel2 = import_mongo_connection89.mongoClientApp.model("id_history_tasks", HistoryTasksSchema2);
var historyTasks2 = HistoryTasksModel2;

// schemas/isoCode/id/incentive.server.ts
var import_mongo_connection90 = require("mongo-connection");
var import_mongoose90 = require("mongoose");
var incentiveName2 = "id_incentive";
var IncentiveSchema2 = new import_mongoose90.Schema(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName2 }
);
var IncentiveModel2 = import_mongo_connection90.mongoClientApp.model(incentiveName2, IncentiveSchema2);
var incentive2 = IncentiveModel2;

// schemas/isoCode/id/journeySetting.server.ts
var import_mongo_connection91 = require("mongo-connection");
var import_mongoose91 = require("mongoose");
var journeySettingName2 = "id_journeySetting";
var JourneySettingSchema2 = new import_mongoose91.Schema(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName2 }
);
var JourneySettingModel2 = import_mongo_connection91.mongoClientApp.model(
  journeySettingName2,
  JourneySettingSchema2
);
var journeySetting2 = JourneySettingModel2;

// schemas/isoCode/id/notification.server.ts
var import_mongo_connection92 = require("mongo-connection");
var import_mongoose92 = require("mongoose");
var notificationName2 = "id_notification";
var NotificationSchema2 = new import_mongoose92.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName2 }
);
var NotificationModel2 = import_mongo_connection92.mongoClientApp.model(
  notificationName2,
  NotificationSchema2
);
var notification2 = NotificationModel2;

// schemas/isoCode/id/partnerBusiness.server.ts
var import_mongo_connection93 = require("mongo-connection");
var import_mongoose93 = require("mongoose");
var businessName2 = "id_business";
var BusinessSchema2 = new import_mongoose93.Schema(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [
      {
        name: { $type: String },
        url: { $type: String }
      }
    ],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose93.Schema.Types.Mixed },
            nextData: { $type: import_mongoose93.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose93.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName2 }
);
var BusinessModel2 = import_mongo_connection93.mongoClientApp.model(businessName2, BusinessSchema2);
var business2 = BusinessModel2;

// schemas/isoCode/id/partnerBusinessLevel.server.ts
var import_mongoose94 = require("mongoose");
var import_mongo_connection94 = require("mongo-connection");
var businessLevelName2 = "id_businessLevel";
var BusinessLevelSchema2 = new import_mongoose94.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName2 }
);
var BusinessLevelModel2 = import_mongo_connection94.mongoClientApp.model(
  businessLevelName2,
  BusinessLevelSchema2
);
var businessLevel2 = BusinessLevelModel2;

// schemas/isoCode/id/partnerBusinessMember.server.ts
var import_mongoose95 = require("mongoose");
var import_mongo_connection95 = require("mongo-connection");
var businessMemberName2 = "id_businessMember";
var BusinessMemberSchema2 = new import_mongoose95.Schema(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName2 }
);
var BusinessMemberModel2 = import_mongo_connection95.mongoClientApp.model(businessMemberName2, BusinessMemberSchema2);
var businessMember2 = BusinessMemberModel2;

// schemas/isoCode/id/partnerBusinessMemberTransaction.server.ts
var import_mongo_connection96 = require("mongo-connection");
var import_mongoose96 = require("mongoose");
var businessMemberTransactionName2 = "id_businessMemberTransaction";
var BusinessMemberTransactionSchema2 = new import_mongoose96.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    userId: { $type: String, required: true },
    taskId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName2 }
);
var BusinessMemberTransactionModel2 = import_mongo_connection96.mongoClientApp.model(businessMemberTransactionName2, BusinessMemberTransactionSchema2);
var businessMemberTransaction2 = BusinessMemberTransactionModel2;

// schemas/isoCode/id/partnerBusinessSetupAllocateAndReallocate.server.ts
var import_mongo_connection97 = require("mongo-connection");
var import_mongoose97 = require("mongoose");
var businessSetupAllocationAndReallocationName2 = "id_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema2 = new import_mongoose97.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName2 }
);
var BusinessSetupAllocationAndReallocationModel2 = import_mongo_connection97.mongoClientApp.model(businessSetupAllocationAndReallocationName2, BusinessSetupAllocationAndReallocationSchema2);
var businessSetupAllocationAndReallocation2 = BusinessSetupAllocationAndReallocationModel2;

// schemas/isoCode/id/partnerBusinessTransaction.server.ts
var import_mongo_connection98 = require("mongo-connection");
var import_mongoose98 = require("mongoose");
var businessTransactionName2 = "id_businessTransaction";
var BusinessTransactionSchema2 = new import_mongoose98.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName2 }
);
var BusinessTransactionModel2 = import_mongo_connection98.mongoClientApp.model(
  businessTransactionName2,
  BusinessTransactionSchema2
);
var businessTransaction2 = BusinessTransactionModel2;

// schemas/isoCode/id/partnerDirectory.server.ts
var import_mongoose99 = require("mongoose");
var import_mongo_connection99 = require("mongo-connection");
var partnerDirectoryName2 = "id_partnerDirectory";
var PartnerDirectorySchema2 = new import_mongoose99.Schema(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName2 }
);
var PartnerDirectoryModel2 = import_mongo_connection99.mongoClientApp.model(
  partnerDirectoryName2,
  PartnerDirectorySchema2
);
var partnerDirectory2 = PartnerDirectoryModel2;

// schemas/isoCode/id/partnerRequest.server.ts
var import_mongoose100 = require("mongoose");
var import_mongo_connection100 = require("mongo-connection");
var partnerRequestName2 = "id_partnerRequest";
var PartnerRequestSchema2 = new import_mongoose100.Schema(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName2 }
);
var PartnerRequestModel2 = import_mongo_connection100.mongoClientApp.model(
  partnerRequestName2,
  PartnerRequestSchema2
);
var partnerRequest2 = PartnerRequestModel2;

// schemas/isoCode/id/paymentToolKitTransaction.server.ts
var import_mongo_connection101 = require("mongo-connection");
var import_mongoose101 = require("mongoose");
var paymentToolKitTransactionName2 = "id_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema2 = new import_mongoose101.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName2 }
);
var PaymentToolKitTransactionModel2 = import_mongo_connection101.mongoClientApp.model(
  paymentToolKitTransactionName2,
  PaymentToolKitTransactionSchema2
);
var paymentToolKitTransaction2 = PaymentToolKitTransactionModel2;

// schemas/isoCode/id/promotionCode.server.ts
var import_mongo_connection102 = require("mongo-connection");
var import_mongoose102 = require("mongoose");
var promotionCodeName2 = "id_promotionCode";
var PromotionCodeSchema2 = new import_mongoose102.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: import_mongoose102.Schema.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName2 }
);
var PromotionCodeModel2 = import_mongo_connection102.mongoClientApp.model(
  promotionCodeName2,
  PromotionCodeSchema2
);
var promotionCode2 = PromotionCodeModel2;

// schemas/isoCode/id/promotionHistory.server.ts
var import_mongo_connection103 = require("mongo-connection");
var import_mongoose103 = require("mongoose");
var promotionHistoryName2 = "id_promotionHistory";
var PromotionHistorySchema2 = new import_mongoose103.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName2 }
);
var PromotionHistoryModel2 = import_mongo_connection103.mongoClientApp.model(
  promotionHistoryName2,
  PromotionHistorySchema2
);
var promotionHistory2 = PromotionHistoryModel2;

// schemas/isoCode/id/promotionSource.server.ts
var import_mongoose104 = require("mongoose");
var import_mongo_connection104 = require("mongo-connection");
var promotionSourceName2 = "id_promotionSource";
var PromotionSourceSchema2 = new import_mongoose104.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName2 }
);
var PromotionSourceModel2 = import_mongo_connection104.mongoClientApp.model(
  promotionSourceName2,
  PromotionSourceSchema2
);
var promotionSource2 = PromotionSourceModel2;

// schemas/isoCode/id/rating.server.ts
var import_mongo_connection105 = require("mongo-connection");
var import_mongoose105 = require("mongoose");
var ratingName2 = "id_rating";
var RatingSchema2 = new import_mongoose105.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName2 }
);
var RatingModel2 = import_mongo_connection105.mongoClientApp.model(ratingName2, RatingSchema2);
var rating2 = RatingModel2;

// schemas/isoCode/id/referralCampaign.server.ts
var import_mongo_connection106 = require("mongo-connection");
var import_mongoose106 = require("mongoose");
var referralCampaignName2 = "id_askerReferralCampaign";
var ReferralCampaignSchema2 = new import_mongoose106.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: import_mongoose106.Schema.Types.Mixed, required: true },
    inviter: { $type: import_mongoose106.Schema.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName2 }
);
var ReferralCampaignModel2 = import_mongo_connection106.mongoClientApp.model(
  referralCampaignName2,
  ReferralCampaignSchema2
);
var referralCampaign2 = ReferralCampaignModel2;

// schemas/isoCode/id/service.server.ts
var import_mongo_connection107 = require("mongo-connection");
var import_mongoose107 = require("mongoose");
var serviceName2 = "id_service";
var ServiceSchema2 = new import_mongoose107.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: import_mongoose107.Schema.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: import_mongoose107.Schema.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: import_mongoose107.Schema.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName2 }
);
var ServiceModel2 = import_mongo_connection107.mongoClientApp.model("id_service", ServiceSchema2);
var service2 = ServiceModel2;

// schemas/isoCode/id/serviceChannel.server.ts
var import_mongo_connection108 = require("mongo-connection");
var import_mongoose108 = require("mongoose");
var serviceChannelName2 = "id_serviceChannel";
var ServiceChannelSchema2 = new import_mongoose108.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName2 }
);
var ServiceChannelModel2 = import_mongo_connection108.mongoClientApp.model(
  serviceChannelName2,
  ServiceChannelSchema2
);
var serviceChannel2 = ServiceChannelModel2;

// schemas/isoCode/id/settingCountry.server.ts
var import_mongo_connection109 = require("mongo-connection");
var import_mongoose109 = require("mongoose");
var settingCountryName2 = "id_settingCountry";
var SettingCountrySchema2 = new import_mongoose109.Schema(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName2 }
);
var SettingCountryModel2 = import_mongo_connection109.mongoClientApp.model(
  settingCountryName2,
  SettingCountrySchema2
);
var settingCountry2 = SettingCountryModel2;

// schemas/isoCode/id/settingSystem.server.ts
var import_mongo_connection110 = require("mongo-connection");
var import_mongoose110 = require("mongoose");
var settingSystemName2 = "id_settingSystem";
var SettingSystemSchema2 = new import_mongoose110.Schema(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: import_mongoose110.Schema.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName2 }
);
var SettingSystemModel2 = import_mongo_connection110.mongoClientApp.model(
  "id_settingSystem",
  SettingSystemSchema2
);
var settingSystem2 = SettingSystemModel2;

// schemas/isoCode/id/subscription.server.ts
var import_mongo_connection111 = require("mongo-connection");
var import_mongoose111 = require("mongoose");
var subscriptionName2 = "id_subscription";
var SubscriptionSchema2 = new import_mongoose111.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: import_mongoose111.Schema.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: import_mongoose111.Schema.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName2 }
);
var SubscriptionModel2 = import_mongo_connection111.mongoClientApp.model(subscriptionName2, SubscriptionSchema2);
var subscription2 = SubscriptionModel2;

// schemas/isoCode/id/task.server.ts
var import_mongo_connection112 = require("mongo-connection");
var import_mongoose112 = require("mongoose");
var taskName2 = "id_task";
var TasksSchema2 = new import_mongoose112.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose112.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose112.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose112.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose112.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: taskName2 }
);
var TasksModel2 = import_mongo_connection112.mongoClientApp.model("id_tasks", TasksSchema2);
var task2 = TasksModel2;

// schemas/isoCode/id/taskerBNPLProcess.server.ts
var import_mongo_connection113 = require("mongo-connection");
var import_mongoose113 = require("mongoose");
var taskerBNPLProcessName2 = "id_taskerBNPLProcess";
var TaskerBNPLProcess2 = new import_mongoose113.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName2 }
);
var TaskerBNPLProcessModel2 = import_mongo_connection113.mongoClientApp.model(
  "id_taskerBNPLProcess",
  TaskerBNPLProcess2
);
var taskerBNPLProcess2 = TaskerBNPLProcessModel2;

// schemas/isoCode/id/taskerBNPLTransaction.server.ts
var import_mongo_connection114 = require("mongo-connection");
var import_mongoose114 = require("mongoose");
var taskerBNPLTransactionName2 = "id_taskerBNPLTransaction";
var TaskerBNPLTransaction2 = new import_mongoose114.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName2 }
);
var TaskerBNPLTransactionModel2 = import_mongo_connection114.mongoClientApp.model(
  taskerBNPLTransactionName2,
  TaskerBNPLTransaction2
);
var taskerBNPLTransaction2 = TaskerBNPLTransactionModel2;

// schemas/isoCode/id/taskerGift.server.ts
var import_mongoose115 = require("mongoose");
var import_mongo_connection115 = require("mongo-connection");
var taskerGiftName2 = "id_taskerGift";
var TaskerGiftSchema2 = new import_mongoose115.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName2 }
);
var TaskerGiftModel2 = import_mongo_connection115.mongoClientApp.model("id_taskerGift", TaskerGiftSchema2);
var taskerGift2 = TaskerGiftModel2;

// schemas/isoCode/id/taskerIncentive.server.ts
var import_mongoose116 = require("mongoose");
var import_mongo_connection116 = require("mongo-connection");
var taskerIncentiveName2 = "id_taskerIncentive";
var TaskerIncentiveSchema2 = new import_mongoose116.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: import_mongoose116.Schema.Types.Mixed
    },
    status: {
      $type: String
    },
    codeFromPartner: {
      $type: Number
    },
    giftInfo: import_mongoose116.Schema.Types.Mixed,
    social: import_mongoose116.Schema.Types.Mixed,
    codeList: import_mongoose116.Schema.Types.Mixed,
    office: import_mongoose116.Schema.Types.Mixed,
    applyFor: import_mongoose116.Schema.Types.Mixed,
    brandInfo: import_mongoose116.Schema.Types.Mixed,
    redeemLink: import_mongoose116.Schema.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName2 }
);
var TaskerIncentiveModel2 = import_mongo_connection116.mongoClientApp.model(
  "id_taskerIncentive",
  TaskerIncentiveSchema2
);
var taskerIncentive2 = TaskerIncentiveModel2;

// schemas/isoCode/id/taskerOnboardingSetting.server.ts
var import_mongo_connection117 = require("mongo-connection");
var import_mongoose117 = require("mongoose");
var taskerOnboardingSettingName2 = "id_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema2 = new import_mongoose117.Schema(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName2 }
);
var TaskerOnboardingSettingModel2 = import_mongo_connection117.mongoClientApp.model(
  taskerOnboardingSettingName2,
  TaskerOnboardingSettingSchema2
);
var taskerOnboardingSetting2 = TaskerOnboardingSettingModel2;

// schemas/isoCode/id/taskerPointTransaction.server.ts
var import_mongoose118 = require("mongoose");
var import_mongo_connection118 = require("mongo-connection");
var taskerPointTransactionName2 = "id_taskerPointTransaction";
var TaskerPointTransactionSchema2 = new import_mongoose118.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName2 }
);
var TaskerPointTransactionModel2 = import_mongo_connection118.mongoClientApp.model(
  "id_taskerPointTransaction",
  TaskerPointTransactionSchema2
);
var taskerPointTransaction2 = TaskerPointTransactionModel2;

// schemas/isoCode/id/taskerProfile.server.ts
var import_mongoose119 = require("mongoose");
var import_mongo_connection119 = require("mongo-connection");
var taskerProfileName2 = "id_taskerProfile";
var TaskerProfileSchema2 = new import_mongoose119.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName2
  }
);
var TaskerProfileModel2 = import_mongo_connection119.mongoClientApp.model(
  "id_taskerProfile",
  TaskerProfileSchema2
);
var taskerProfile2 = TaskerProfileModel2;

// schemas/isoCode/id/taskerSpecialCampaign.server.ts
var import_mongo_connection120 = require("mongo-connection");
var import_mongoose120 = require("mongoose");
var taskerSpecialCampaignName2 = "id_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema2 = new import_mongoose120.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: import_mongoose120.Schema.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName2, _id: false }
);
var TaskerSpecialCampaignModel2 = import_mongo_connection120.mongoClientApp.model(
  taskerSpecialCampaignName2,
  TaskerSpecialCampaignSchema2
);
var taskerSpecialCampaign2 = TaskerSpecialCampaignModel2;

// schemas/isoCode/id/taskerSpecialCampaignTransaction.server.ts
var import_mongo_connection121 = require("mongo-connection");
var import_mongoose121 = require("mongoose");
var taskerSpecialCampaignTransactionName2 = "id_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema2 = new import_mongoose121.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName2 }
);
var TaskerSpecialCampaignTransactionModel2 = import_mongo_connection121.mongoClientApp.model(
  taskerSpecialCampaignTransactionName2,
  TaskerSpecialCampaignTransactionSchema2
);
var taskerSpecialCampaignTransaction2 = TaskerSpecialCampaignTransactionModel2;

// schemas/isoCode/id/taskerToolkitLadingDetails.server.ts
var import_mongoose122 = require("mongoose");
var import_mongo_connection122 = require("mongo-connection");
var taskerToolkitLadingDetailsName2 = "id_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema2 = new import_mongoose122.Schema(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName2 }
);
var TaskerToolkitLadingDetailsModel2 = import_mongo_connection122.mongoClientApp.model(
  "id_taskerToolkitLadingDetails",
  TaskerToolkitLadingDetailsSchema2
);
var taskerToolkitLadingDetails2 = TaskerToolkitLadingDetailsModel2;

// schemas/isoCode/id/taskerTrainingCourse.server.ts
var import_mongo_connection123 = require("mongo-connection");
var import_mongoose123 = require("mongoose");
var taskerTrainingCourseName2 = "id_trainingTaskerCourse";
var CourseSchema2 = new import_mongoose123.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName2 }
);
var CourseModel2 = import_mongo_connection123.mongoClientApp.model(
  taskerTrainingCourseName2,
  CourseSchema2
);
var taskerTrainingCourse2 = CourseModel2;

// schemas/isoCode/id/taskerTrainingCourseStartDate.ts
var import_mongo_connection124 = require("mongo-connection");
var import_mongoose124 = require("mongoose");
var taskerTrainingCourseStartDateName2 = "id_trainingTaskerCourseStartDate";
var CourseStartDateSchema2 = new import_mongoose124.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName2 }
);
var CourseStartDateModel2 = import_mongo_connection124.mongoClientApp.model(
  taskerTrainingCourseStartDateName2,
  CourseStartDateSchema2
);
var taskerTrainingCourseStartDate2 = CourseStartDateModel2;

// schemas/isoCode/id/taskerTrainingQuiz.server.ts
var import_mongoose125 = require("mongoose");
var import_mongo_connection125 = require("mongo-connection");
var taskerTrainingQuizName2 = "id_trainingTaskerQuiz";
var QuizSchema2 = new import_mongoose125.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName2 }
);
var QuizModel2 = import_mongo_connection125.mongoClientApp.model(taskerTrainingQuizName2, QuizSchema2);
var taskerTrainingQuiz2 = QuizModel2;

// schemas/isoCode/id/taskerTrainingQuizCollection.server.ts
var import_mongoose126 = require("mongoose");
var import_mongo_connection126 = require("mongo-connection");
var taskerTrainingQuizCollectionName2 = "id_trainingTaskerQuizCollection";
var QuizCollectionSchema2 = new import_mongoose126.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName2 }
);
var QuizCollectionModel2 = import_mongo_connection126.mongoClientApp.model(
  taskerTrainingQuizCollectionName2,
  QuizCollectionSchema2
);
var taskerTrainingQuizCollection2 = QuizCollectionModel2;

// schemas/isoCode/id/taskerTrainingSubmission.server.ts
var import_mongoose127 = require("mongoose");
var import_mongo_connection127 = require("mongo-connection");
var taskerTrainingSubmissionName2 = "id_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema2 = new import_mongoose127.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName2 }
);
var TaskerTrainingSubmissionModel2 = import_mongo_connection127.mongoClientApp.model(
  taskerTrainingSubmissionName2,
  TaskerTrainingSubmissionSchema2
);
var taskerTrainingSubmission2 = TaskerTrainingSubmissionModel2;

// schemas/isoCode/id/thingsToKnow.server.ts
var import_mongoose128 = require("mongoose");
var import_mongo_connection128 = require("mongo-connection");
var thingsToKnowName2 = "id_thingsToKnow";
var ThingToKnowSchema2 = new import_mongoose128.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName2 }
);
var ThingsToKnowModel2 = import_mongo_connection128.mongoClientApp.model(
  "id_thingsToKnow",
  ThingToKnowSchema2
);
var thingsToKnow2 = ThingsToKnowModel2;

// schemas/isoCode/id/toolKitItems.server.ts
var import_mongoose129 = require("mongoose");
var import_mongo_connection129 = require("mongo-connection");
var toolKitItemsName2 = "id_toolKitItems";
var ToolKitItemsSchema2 = new import_mongoose129.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName2 }
);
var ToolKitItemsModel2 = import_mongo_connection129.mongoClientApp.model(
  "id_toolKitItems",
  ToolKitItemsSchema2
);
var toolKitItems2 = ToolKitItemsModel2;

// schemas/isoCode/id/toolKitSetting.server.ts
var import_mongoose130 = require("mongoose");
var import_mongo_connection130 = require("mongo-connection");
var toolKitSettingName2 = "id_toolKitSetting";
var ToolKitSettingSchema2 = new import_mongoose130.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName2 }
);
var ToolKitSettingModel2 = import_mongo_connection130.mongoClientApp.model(
  "id_toolKitSetting",
  ToolKitSettingSchema2
);
var toolKitSetting2 = ToolKitSettingModel2;

// schemas/isoCode/id/trainingJourney.server.ts
var import_mongoose131 = require("mongoose");
var import_mongo_connection131 = require("mongo-connection");
var trainingJourneyName2 = "id_trainingJourney";
var TrainingJourneySchema2 = new import_mongoose131.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName2 }
);
var TrainingJourneyModel2 = import_mongo_connection131.mongoClientApp.model(
  "id_trainingJourney",
  TrainingJourneySchema2
);
var trainingJourney2 = TrainingJourneyModel2;

// schemas/isoCode/id/trainingTasker.server.ts
var import_mongoose132 = require("mongoose");
var import_mongo_connection132 = require("mongo-connection");
var trainingTaskerName2 = "id_trainingTasker";
var TrainingTaskerSchema2 = new import_mongoose132.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName2 }
);
var TrainingTaskerModel2 = import_mongo_connection132.mongoClientApp.model(
  "id_trainingTasker",
  TrainingTaskerSchema2
);
var trainingTasker2 = TrainingTaskerModel2;

// schemas/isoCode/id/userActivation.server.ts
var import_mongo_connection133 = require("mongo-connection");
var import_mongoose133 = require("mongoose");
var userActivationName2 = "id_userActivation";
var UserActivationSchema2 = new import_mongoose133.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName2 }
);
var UserActivationModel2 = import_mongo_connection133.mongoClientApp.model(
  userActivationName2,
  UserActivationSchema2
);
var userActivation2 = UserActivationModel2;

// schemas/isoCode/id/userApp.server.ts
var import_mongo_connection134 = require("mongo-connection");
var import_mongoose134 = require("mongoose");
var usersName2 = "users";
var UsersAppSchema2 = new import_mongoose134.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName2 }
);
var UsersAppModel2 = import_mongo_connection134.mongoClientApp.model("id_users", UsersAppSchema2);
var users2 = UsersAppModel2;

// schemas/isoCode/id/userComboVoucher.server.ts
var import_mongo_connection135 = require("mongo-connection");
var import_mongoose135 = require("mongoose");
var userComboVoucherName2 = "id_userComboVoucher";
var UserComboVoucherSchema2 = new import_mongoose135.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: import_mongoose135.Schema.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: import_mongoose135.Schema.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName2 }
);
var UserComboVoucherModel2 = import_mongo_connection135.mongoClientApp.model(
  userComboVoucherName2,
  UserComboVoucherSchema2
);
var userComboVoucher2 = UserComboVoucherModel2;

// schemas/isoCode/id/userLocationHistory.server.ts
var import_mongoose136 = require("mongoose");
var import_mongo_connection136 = require("mongo-connection");
var userLocationHistoryName2 = "id_userLocationHistory";
var UserLocationHistorySchema2 = new import_mongoose136.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName2 }
);
var UserLocationHistoryModel2 = import_mongo_connection136.mongoClientApp.model(
  userLocationHistoryName2,
  UserLocationHistorySchema2
);
var userLocationHistory2 = UserLocationHistoryModel2;

// schemas/isoCode/id/userProfile.server.ts
var import_mongo_connection137 = require("mongo-connection");
var import_mongoose137 = require("mongoose");
var userProfileName2 = "id_userProfile";
var UserProfileSchema2 = new import_mongoose137.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: import_mongoose137.Schema.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName2 }
);
var UserProfileModel2 = import_mongo_connection137.mongoClientApp.model(userProfileName2, UserProfileSchema2);
var userProfile2 = UserProfileModel2;

// schemas/isoCode/id/workingPlaces.server.ts
var import_mongoose138 = require("mongoose");
var import_mongo_connection138 = require("mongo-connection");
var workingPlacesName2 = "id_workingPlaces";
var WorkingPlacesSchema2 = new import_mongoose138.Schema(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName2 }
);
var WorkingPlacesModel2 = import_mongo_connection138.mongoClientApp.model(
  "id_workingPlaces",
  WorkingPlacesSchema2
);
var workingPlaces2 = WorkingPlacesModel2;

// schemas/isoCode/id/index.server.ts
var ID = {
  task: task2,
  historyTasks: historyTasks2,
  userLocationHistory: userLocationHistory2,
  trainingTasker: trainingTasker2,
  trainingJourney: trainingJourney2,
  toolKitSetting: toolKitSetting2,
  toolKitItems: toolKitItems2,
  thingsToKnow: thingsToKnow2,
  taskerTrainingSubmission: taskerTrainingSubmission2,
  taskerTrainingQuizCollection: taskerTrainingQuizCollection2,
  taskerTrainingQuiz: taskerTrainingQuiz2,
  taskerTrainingCourse: taskerTrainingCourse2,
  taskerToolkitLadingDetails: taskerToolkitLadingDetails2,
  financialAccount: financialAccount2,
  taskerIncentive: taskerIncentive2,
  taskerPointTransaction: taskerPointTransaction2,
  taskerGift: taskerGift2,
  notification: notification2,
  employeeProfile: employeeProfile2,
  workingPlaces: workingPlaces2,
  taskerProfile: taskerProfile2,
  flashSale: flashSale2,
  incentive: incentive2,
  promotionCode: promotionCode2,
  promotionSource: promotionSource2,
  service: service2,
  promotionHistory: promotionHistory2,
  settingCountry: settingCountry2,
  comboVoucher: comboVoucher2,
  referralCampaign: referralCampaign2,
  marketingCampaign: marketingCampaign2,
  settingSystem: settingSystem2,
  partnerRequest: partnerRequest2,
  partnerDirectory: partnerDirectory2,
  communityComment: communityComment2,
  communityMedal: communityMedal2,
  communityNotification: communityNotification2,
  communityPost: communityPost2,
  communitySetting: communitySetting2,
  communityTag: communityTag2,
  communityUser: communityUser2,
  communityUserReport: communityUserReport2,
  subscription: subscription2,
  taskerOnboardingSetting: taskerOnboardingSetting2,
  users: users2,
  serviceChannel: serviceChannel2,
  bEmployee: bEmployee2,
  bEmployeeSetting: bEmployeeSetting2,
  FATransaction: FATransaction2,
  taskerBNPLTransaction: taskerBNPLTransaction2,
  taskerBNPLProcess: taskerBNPLProcess2,
  paymentToolKitTransaction: paymentToolKitTransaction2,
  rating: rating2,
  userActivation: userActivation2,
  business: business2,
  businessLevel: businessLevel2,
  businessMember: businessMember2,
  businessMemberTransaction: businessMemberTransaction2,
  businessTransaction: businessTransaction2,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocation2,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDate2,
  taskerSpecialCampaign: taskerSpecialCampaign2,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransaction2,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethod2,
  userProfile: userProfile2,
  communityTagOrder: communityTagOrder2,
  journeySetting: journeySetting2,
  userComboVoucher: userComboVoucher2,
  bundleVoucher: bundleVoucher2
};
var IDName = {
  trainingTasker: trainingTaskerName2,
  historyTasks: historyTasksName2,
  userLocationHistory: userLocationHistoryName2,
  trainingJourney: trainingJourneyName2,
  toolKitSetting: toolKitSettingName2,
  toolKitItems: toolKitItemsName2,
  thingsToKnow: thingsToKnowName2,
  taskerTrainingSubmission: taskerTrainingSubmissionName2,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName2,
  taskerTrainingQuiz: taskerTrainingQuizName2,
  taskerTrainingCourse: taskerTrainingCourseName2,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName2,
  taskerPointTransaction: taskerPointTransactionName2,
  taskerIncentive: taskerIncentiveName2,
  taskerGift: taskerGiftName2,
  notification: notificationName2,
  financialAccount: financialAccountName2,
  taskerProfile: taskerProfileName2,
  employeeProfile: employeeProfileName2,
  task: taskName2,
  workingPlaces: workingPlacesName2,
  service: serviceName2,
  settingCountry: settingCountryName2,
  settingSystem: settingSystemName2,
  incentive: incentiveName2,
  communityComment: communityCommentName2,
  communityMedal: communityMedalName2,
  communityNotification: communityNotificationName2,
  communityPost: communityPostName2,
  communitySetting: communitySettingName2,
  communityTag: communityTagName2,
  communityUser: communityUserName2,
  communityUserReport: communityUserReportName2,
  subscription: subscriptionName2,
  taskerOnboardingSetting: taskerOnboardingSettingName2,
  users: usersName2,
  serviceChannel: serviceChannelName2,
  bEmployee: bEmployeeName2,
  bEmployeeSetting: bEmployeeSettingName2,
  FATransaction: FATransactionName2,
  taskerBNPLTransaction: taskerBNPLTransactionName2,
  taskerBNPLProcess: taskerBNPLProcessName2,
  paymentToolKitTransaction: paymentToolKitTransactionName2,
  promotionCode: promotionCodeName2,
  promotionHistory: promotionHistoryName2,
  marketingCampaign: marketingCampaignName2,
  rating: ratingName2,
  userActivation: userActivationName2,
  business: businessName2,
  businessLevel: businessLevelName2,
  businessMember: businessMemberName2,
  businessMemberTransaction: businessMemberTransactionName2,
  businessTransaction: businessTransactionName2,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName2,
  comboVoucher: comboVoucherName2,
  flashSale: flashSaleName2,
  partnerDirectory: partnerDirectoryName2,
  partnerRequest: partnerRequestName2,
  promotionSource: promotionSourceName2,
  referralCampaign: referralCampaignName2,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName2,
  taskerSpecialCampaign: taskerSpecialCampaignName2,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName2,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName2,
  userProfile: userProfileName2,
  communityTagOrder: communityTagOrderName2,
  journeySetting: journeySettingName2,
  userComboVoucher: userComboVoucherName2,
  bundleVoucher: bundleVoucherName2
};
var index_server_default2 = ID;

// schemas/isoCode/th/bEmployee.server.ts
var import_mongo_connection139 = require("mongo-connection");
var import_mongoose139 = require("mongoose");
var bEmployeeName3 = "th_bEmployee";
var BEmployeeSchema3 = new import_mongoose139.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName3 }
);
var BEmployeeModel3 = import_mongo_connection139.mongoClientApp.model("th_bEmployee", BEmployeeSchema3);
var bEmployee3 = BEmployeeModel3;

// schemas/isoCode/th/bEmployeeSetting.server.ts
var import_mongo_connection140 = require("mongo-connection");
var import_mongoose140 = require("mongoose");
var bEmployeeSettingName3 = "th_bEmployeeSetting";
var BEmployeeSettingSchema3 = new import_mongoose140.Schema(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName3 }
);
var BEmployeeSettingModel3 = import_mongo_connection140.mongoClientApp.model(
  "th_bEmployeeSetting",
  BEmployeeSettingSchema3
);
var bEmployeeSetting3 = BEmployeeSettingModel3;

// schemas/isoCode/th/bundleVoucher.ts
var import_mongo_connection141 = require("mongo-connection");
var import_mongoose141 = require("mongoose");
var bundleVoucherName3 = "th_bundleVoucher";
var BundleVoucherSchema3 = new import_mongoose141.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose141.Schema.Types.Mixed },
            nextData: { $type: import_mongoose141.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose141.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName3 }
);
var BundleVoucherModel3 = import_mongo_connection141.mongoClientApp.model(
  bundleVoucherName3,
  BundleVoucherSchema3
);
var bundleVoucher3 = BundleVoucherModel3;

// schemas/isoCode/th/campaign-payment.server.ts
var import_mongo_connection142 = require("mongo-connection");
var import_mongoose142 = require("mongoose");
var marketingCampaignPaymentMethodName3 = "th_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema3 = new import_mongoose142.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: import_mongoose142.Schema.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName3 }
);
var MarketingCampaignPaymentMethodModel3 = import_mongo_connection142.mongoClientApp.model(
  marketingCampaignPaymentMethodName3,
  MarketingCampaignPaymentMethodSchema3
);
var marketingCampaignPaymentMethod3 = MarketingCampaignPaymentMethodModel3;

// schemas/isoCode/th/campaign.server.ts
var import_mongo_connection143 = require("mongo-connection");
var import_mongoose143 = require("mongoose");
var marketingCampaignName3 = "th_marketingCampaign";
var MarketingCampaignSchema3 = new import_mongoose143.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: import_mongoose143.Schema.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: import_mongoose143.Schema.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose143.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose143.Schema.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose143.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose143.Schema.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName3 }
);
var MarketingCampaignModel3 = import_mongo_connection143.mongoClientApp.model(
  marketingCampaignName3,
  MarketingCampaignSchema3
);
var marketingCampaign3 = MarketingCampaignModel3;

// schemas/isoCode/th/comboVoucher.server.ts
var import_mongo_connection144 = require("mongo-connection");
var import_mongoose144 = require("mongoose");
var comboVoucherName3 = "th_comboVoucher";
var ComboVoucherSchema3 = new import_mongoose144.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName3 }
);
var ComboVoucherModel3 = import_mongo_connection144.mongoClientApp.model(
  "th_comboVoucher",
  ComboVoucherSchema3
);
var comboVoucher3 = ComboVoucherModel3;

// schemas/isoCode/th/communityComment.server.ts
var import_mongo_connection145 = require("mongo-connection");
var import_mongoose145 = require("mongoose");
var communityCommentName3 = "th_communityComment";
var CommunityCommentSchema3 = new import_mongoose145.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName3 }
);
var CommunityCommentModel3 = import_mongo_connection145.mongoClientApp.model(
  communityCommentName3,
  CommunityCommentSchema3
);
var communityComment3 = CommunityCommentModel3;

// schemas/isoCode/th/communityMedal.server.ts
var import_mongo_connection146 = require("mongo-connection");
var import_mongoose146 = require("mongoose");
var communityMedalName3 = "th_communityMedal";
var CommunityMedalSchema3 = new import_mongoose146.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: import_mongoose146.Schema.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: import_mongoose146.Schema.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: import_mongoose146.Schema.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName3 }
);
var CommunityMedalModel3 = import_mongo_connection146.mongoClientApp.model(
  communityMedalName3,
  CommunityMedalSchema3
);
var communityMedal3 = CommunityMedalModel3;

// schemas/isoCode/th/communityNotification.server.ts
var import_mongo_connection147 = require("mongo-connection");
var import_mongoose147 = require("mongoose");
var communityNotificationName3 = "th_communityNotification";
var CommunityNotificationSchema3 = new import_mongoose147.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: import_mongoose147.Schema.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName3 }
);
var CommunityNotificationModel3 = import_mongo_connection147.mongoClientApp.model(
  communityNotificationName3,
  CommunityNotificationSchema3
);
var communityNotification3 = CommunityNotificationModel3;

// schemas/isoCode/th/communityOrderTag.server.ts
var import_mongo_connection148 = require("mongo-connection");
var import_mongoose148 = require("mongoose");
var communityTagOrderName3 = "th_communityTagOrder";
var CommunityTagOrderSchema3 = new import_mongoose148.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: import_mongoose148.Schema.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: import_mongoose148.Schema.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName3 }
);
var CommunityTagOrderModel3 = import_mongo_connection148.mongoClientApp.model(
  communityTagOrderName3,
  CommunityTagOrderSchema3
);
var communityTagOrder3 = CommunityTagOrderModel3;

// schemas/isoCode/th/communityPost.server.ts
var import_mongo_connection149 = require("mongo-connection");
var import_mongoose149 = require("mongoose");
var communityPostName3 = "th_communityPost";
var CommunityPostSchema3 = new import_mongoose149.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: import_mongoose149.Schema.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityPostName3 }
);
var CommunityPostModel3 = import_mongo_connection149.mongoClientApp.model(
  communityPostName3,
  CommunityPostSchema3
);
var communityPost3 = CommunityPostModel3;

// schemas/isoCode/th/communitySetting.server.ts
var import_mongo_connection150 = require("mongo-connection");
var import_mongoose150 = require("mongoose");
var communitySettingName3 = "th_communitySetting";
var CommunitySettingSchema3 = new import_mongoose150.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: import_mongoose150.Schema.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName3
  }
);
var CommunitySettingModel3 = import_mongo_connection150.mongoClientApp.model(
  communitySettingName3,
  CommunitySettingSchema3
);
var communitySetting3 = CommunitySettingModel3;

// schemas/isoCode/th/communityTag.server.ts
var import_mongo_connection151 = require("mongo-connection");
var import_mongoose151 = require("mongoose");
var communityTagName3 = "th_communityTag";
var CommunityTagSchema3 = new import_mongoose151.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: import_mongoose151.Schema.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: import_mongoose151.Schema.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName3 }
);
var CommunityTagModel3 = import_mongo_connection151.mongoClientApp.model(
  communityTagName3,
  CommunityTagSchema3
);
var communityTag3 = CommunityTagModel3;

// schemas/isoCode/th/communityUser.server.ts
var import_mongo_connection152 = require("mongo-connection");
var import_mongoose152 = require("mongoose");
var communityUserName3 = "th_communityUser";
var CommunityUserSchema3 = new import_mongoose152.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName3 }
);
var CommunityUserModel3 = import_mongo_connection152.mongoClientApp.model(
  communityUserName3,
  CommunityUserSchema3
);
var communityUser3 = CommunityUserModel3;

// schemas/isoCode/th/communityUserReport.server.ts
var import_mongo_connection153 = require("mongo-connection");
var import_mongoose153 = require("mongoose");
var communityUserReportName3 = "th_communityUserReport";
var CommunityUserReportSchema3 = new import_mongoose153.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName3 }
);
var CommunityUserReportModel3 = import_mongo_connection153.mongoClientApp.model(
  communityUserReportName3,
  CommunityUserReportSchema3
);
var communityUserReport3 = CommunityUserReportModel3;

// schemas/isoCode/th/employeeProfile.server.ts
var import_mongoose154 = require("mongoose");
var import_mongo_connection154 = require("mongo-connection");
var employeeProfileName3 = "th_employeeProfile";
var EmployeeProfileSchema3 = new import_mongoose154.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName3 }
);
var EmployeeProfileModel3 = import_mongo_connection154.mongoClientApp.model(
  "th_employeeProfile",
  EmployeeProfileSchema3
);
var employeeProfile3 = EmployeeProfileModel3;

// schemas/isoCode/th/FATransaction.server.ts
var import_mongo_connection155 = require("mongo-connection");
var import_mongoose155 = require("mongoose");
var FATransactionName3 = "th_FATransaction";
var FATransactionSchema3 = new import_mongoose155.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName3 }
);
var FATransactionModel3 = import_mongo_connection155.mongoClientApp.model(
  "th_FATransaction",
  FATransactionSchema3
);
var FATransaction3 = FATransactionModel3;

// schemas/isoCode/th/financialAccount.server.ts
var import_mongoose156 = require("mongoose");
var import_mongo_connection156 = require("mongo-connection");
var financialAccountName3 = "th_financialAccount";
var FinancialAccountSchema3 = new import_mongoose156.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName3 }
);
var FinancialAccountModel3 = import_mongo_connection156.mongoClientApp.model(
  "th_financialAccount",
  FinancialAccountSchema3
);
var financialAccount3 = FinancialAccountModel3;

// schemas/isoCode/th/flashSale.server.ts
var import_mongo_connection157 = require("mongo-connection");
var import_mongoose157 = require("mongoose");
var flashSaleName3 = "th_askerFlashSaleIncentive";
var FlashSaleSchema3 = new import_mongoose157.Schema(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName3 }
);
var FlashSaleModel3 = import_mongo_connection157.mongoClientApp.model(
  "th_askerFlashSaleIncentive",
  FlashSaleSchema3
);
var flashSale3 = FlashSaleModel3;

// schemas/isoCode/th/historyTasks.server.ts
var import_mongo_connection158 = require("mongo-connection");
var import_mongoose158 = require("mongoose");
var historyTasksName3 = "history_tasks";
var HistoryTasksSchema3 = new import_mongoose158.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose158.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose158.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose158.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose158.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName3 }
);
var HistoryTasksModel3 = import_mongo_connection158.mongoClientApp.model("th_history_tasks", HistoryTasksSchema3);
var historyTasks3 = HistoryTasksModel3;

// schemas/isoCode/th/incentive.server.ts
var import_mongo_connection159 = require("mongo-connection");
var import_mongoose159 = require("mongoose");
var incentiveName3 = "th_incentive";
var IncentiveSchema3 = new import_mongoose159.Schema(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName3 }
);
var IncentiveModel3 = import_mongo_connection159.mongoClientApp.model("th_incentive", IncentiveSchema3);
var incentive3 = IncentiveModel3;

// schemas/isoCode/th/journeySetting.server.ts
var import_mongo_connection160 = require("mongo-connection");
var import_mongoose160 = require("mongoose");
var journeySettingName3 = "th_journeySetting";
var JourneySettingSchema3 = new import_mongoose160.Schema(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName3 }
);
var JourneySettingModel3 = import_mongo_connection160.mongoClientApp.model(
  journeySettingName3,
  JourneySettingSchema3
);
var journeySetting3 = JourneySettingModel3;

// schemas/isoCode/th/notification.server.ts
var import_mongoose161 = require("mongoose");
var import_mongo_connection161 = require("mongo-connection");
var notificationName3 = "th_notification";
var NotificationSchema3 = new import_mongoose161.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName3 }
);
var NotificationModel3 = import_mongo_connection161.mongoClientApp.model(
  "th_notification",
  NotificationSchema3
);
var notification3 = NotificationModel3;

// schemas/isoCode/th/partnerBusiness.server.ts
var import_mongo_connection162 = require("mongo-connection");
var import_mongoose162 = require("mongoose");
var businessName3 = "th_business";
var BusinessSchema3 = new import_mongoose162.Schema(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [{ name: { $type: String }, url: { $type: String } }],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose162.Schema.Types.Mixed },
            nextData: { $type: import_mongoose162.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose162.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName3 }
);
var BusinessModel3 = import_mongo_connection162.mongoClientApp.model(businessName3, BusinessSchema3);
var business3 = BusinessModel3;

// schemas/isoCode/th/partnerBusinessLevel.server.ts
var import_mongoose163 = require("mongoose");
var import_mongo_connection163 = require("mongo-connection");
var businessLevelName3 = "th_businessLevel";
var BusinessLevelSchema3 = new import_mongoose163.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName3 }
);
var BusinessLevelModel3 = import_mongo_connection163.mongoClientApp.model(
  businessLevelName3,
  BusinessLevelSchema3
);
var businessLevel3 = BusinessLevelModel3;

// schemas/isoCode/th/partnerBusinessMember.server.ts
var import_mongoose164 = require("mongoose");
var import_mongo_connection164 = require("mongo-connection");
var businessMemberName3 = "th_businessMember";
var BusinessMemberSchema3 = new import_mongoose164.Schema(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName3 }
);
var BusinessMemberModel3 = import_mongo_connection164.mongoClientApp.model(businessMemberName3, BusinessMemberSchema3);
var businessMember3 = BusinessMemberModel3;

// schemas/isoCode/th/partnerBusinessMemberTransaction.server.ts
var import_mongo_connection165 = require("mongo-connection");
var import_mongoose165 = require("mongoose");
var businessMemberTransactionName3 = "th_businessMemberTransaction";
var BusinessMemberTransactionSchema3 = new import_mongoose165.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    taskId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    userId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName3 }
);
var BusinessMemberTransactionModel3 = import_mongo_connection165.mongoClientApp.model(businessMemberTransactionName3, BusinessMemberTransactionSchema3);
var businessMemberTransaction3 = BusinessMemberTransactionModel3;

// schemas/isoCode/th/partnerBusinessSetupAllocateAndReallocate.server.ts
var import_mongo_connection166 = require("mongo-connection");
var import_mongoose166 = require("mongoose");
var businessSetupAllocationAndReallocationName3 = "th_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema3 = new import_mongoose166.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName3 }
);
var BusinessSetupAllocationAndReallocationModel3 = import_mongo_connection166.mongoClientApp.model(businessSetupAllocationAndReallocationName3, BusinessSetupAllocationAndReallocationSchema3);
var businessSetupAllocationAndReallocation3 = BusinessSetupAllocationAndReallocationModel3;

// schemas/isoCode/th/partnerBusinessTransaction.server.ts
var import_mongo_connection167 = require("mongo-connection");
var import_mongoose167 = require("mongoose");
var businessTransactionName3 = "th_businessTransaction";
var BusinessTransactionSchema3 = new import_mongoose167.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName3 }
);
var BusinessTransactionModel3 = import_mongo_connection167.mongoClientApp.model(
  businessTransactionName3,
  BusinessTransactionSchema3
);
var businessTransaction3 = BusinessTransactionModel3;

// schemas/isoCode/th/partnerDirectory.server.ts
var import_mongoose168 = require("mongoose");
var import_mongo_connection168 = require("mongo-connection");
var partnerDirectoryName3 = "th_partnerDirectory";
var PartnerDirectorySchema3 = new import_mongoose168.Schema(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName3 }
);
var PartnerDirectoryModel3 = import_mongo_connection168.mongoClientApp.model(
  partnerDirectoryName3,
  PartnerDirectorySchema3
);
var partnerDirectory3 = PartnerDirectoryModel3;

// schemas/isoCode/th/partnerRequest.server.ts
var import_mongoose169 = require("mongoose");
var import_mongo_connection169 = require("mongo-connection");
var partnerRequestName3 = "th_partnerRequest";
var PartnerRequestSchema3 = new import_mongoose169.Schema(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName3 }
);
var PartnerRequestModel3 = import_mongo_connection169.mongoClientApp.model(
  partnerRequestName3,
  PartnerRequestSchema3
);
var partnerRequest3 = PartnerRequestModel3;

// schemas/isoCode/th/paymentToolKitTransaction.server.ts
var import_mongo_connection170 = require("mongo-connection");
var import_mongoose170 = require("mongoose");
var paymentToolKitTransactionName3 = "th_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema3 = new import_mongoose170.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName3 }
);
var PaymentToolKitTransactionModel3 = import_mongo_connection170.mongoClientApp.model(
  paymentToolKitTransactionName3,
  PaymentToolKitTransactionSchema3
);
var paymentToolKitTransaction3 = PaymentToolKitTransactionModel3;

// schemas/isoCode/th/promotionCode.server.ts
var import_mongo_connection171 = require("mongo-connection");
var import_mongoose171 = require("mongoose");
var promotionCodeName3 = "th_promotionCode";
var PromotionCodeSchema3 = new import_mongoose171.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: import_mongoose171.Schema.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName3 }
);
var PromotionCodeModel3 = import_mongo_connection171.mongoClientApp.model(
  promotionCodeName3,
  PromotionCodeSchema3
);
var promotionCode3 = PromotionCodeModel3;

// schemas/isoCode/th/promotionHistory.server.ts
var import_mongo_connection172 = require("mongo-connection");
var import_mongoose172 = require("mongoose");
var promotionHistoryName3 = "th_promotionHistory";
var PromotionHistorySchema3 = new import_mongoose172.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName3 }
);
var PromotionHistoryModel3 = import_mongo_connection172.mongoClientApp.model(
  promotionHistoryName3,
  PromotionHistorySchema3
);
var promotionHistory3 = PromotionHistoryModel3;

// schemas/isoCode/th/promotionSource.server.ts
var import_mongoose173 = require("mongoose");
var import_mongo_connection173 = require("mongo-connection");
var promotionSourceName3 = "th_promotionSource";
var PromotionSourceSchema3 = new import_mongoose173.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName3 }
);
var PromotionSourceModel3 = import_mongo_connection173.mongoClientApp.model(
  promotionSourceName3,
  PromotionSourceSchema3
);
var promotionSource3 = PromotionSourceModel3;

// schemas/isoCode/th/rating.server.ts
var import_mongo_connection174 = require("mongo-connection");
var import_mongoose174 = require("mongoose");
var ratingName3 = "th_rating";
var RatingSchema3 = new import_mongoose174.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName3 }
);
var RatingModel3 = import_mongo_connection174.mongoClientApp.model(ratingName3, RatingSchema3);
var rating3 = RatingModel3;

// schemas/isoCode/th/referralCampaign.server.ts
var import_mongo_connection175 = require("mongo-connection");
var import_mongoose175 = require("mongoose");
var referralCampaignName3 = "th_askerReferralCampaign";
var ReferralCampaignSchema3 = new import_mongoose175.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: import_mongoose175.Schema.Types.Mixed, required: true },
    inviter: { $type: import_mongoose175.Schema.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName3 }
);
var ReferralCampaignModel3 = import_mongo_connection175.mongoClientApp.model(
  referralCampaignName3,
  ReferralCampaignSchema3
);
var referralCampaign3 = ReferralCampaignModel3;

// schemas/isoCode/th/service.server.ts
var import_mongo_connection176 = require("mongo-connection");
var import_mongoose176 = require("mongoose");
var serviceName3 = "th_service";
var ServiceSchema3 = new import_mongoose176.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: import_mongoose176.Schema.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: import_mongoose176.Schema.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: import_mongoose176.Schema.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName3 }
);
var ServiceModel3 = import_mongo_connection176.mongoClientApp.model("th_service", ServiceSchema3);
var service3 = ServiceModel3;

// schemas/isoCode/th/serviceChannel.server.ts
var import_mongo_connection177 = require("mongo-connection");
var import_mongoose177 = require("mongoose");
var serviceChannelName3 = "th_serviceChannel";
var ServiceChannelSchema3 = new import_mongoose177.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName3 }
);
var ServiceChannelModel3 = import_mongo_connection177.mongoClientApp.model(
  "th_serviceChannel",
  ServiceChannelSchema3
);
var serviceChannel3 = ServiceChannelModel3;

// schemas/isoCode/th/settingCountry.server.ts
var import_mongo_connection178 = require("mongo-connection");
var import_mongoose178 = require("mongoose");
var settingCountryName3 = "th_settingCountry";
var SettingCountrySchema3 = new import_mongoose178.Schema(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName3 }
);
var SettingCountryModel3 = import_mongo_connection178.mongoClientApp.model(
  settingCountryName3,
  SettingCountrySchema3
);
var settingCountry3 = SettingCountryModel3;

// schemas/isoCode/th/settingSystem.server.ts
var import_mongo_connection179 = require("mongo-connection");
var import_mongoose179 = require("mongoose");
var settingSystemName3 = "th_settingSystem";
var SettingSystemSchema3 = new import_mongoose179.Schema(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: import_mongoose179.Schema.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName3 }
);
var SettingSystemModel3 = import_mongo_connection179.mongoClientApp.model(
  "th_settingSystem",
  SettingSystemSchema3
);
var settingSystem3 = SettingSystemModel3;

// schemas/isoCode/th/subscription.server.ts
var import_mongo_connection180 = require("mongo-connection");
var import_mongoose180 = require("mongoose");
var subscriptionName3 = "th_subscription";
var SubscriptionSchema3 = new import_mongoose180.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: import_mongoose180.Schema.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: import_mongoose180.Schema.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName3 }
);
var SubscriptionModel3 = import_mongo_connection180.mongoClientApp.model(subscriptionName3, SubscriptionSchema3);
var subscription3 = SubscriptionModel3;

// schemas/isoCode/th/task.server.ts
var import_mongo_connection181 = require("mongo-connection");
var import_mongoose181 = require("mongoose");
var taskName3 = "th_task";
var TasksSchema3 = new import_mongoose181.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose181.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose181.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose181.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose181.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: taskName3 }
);
var TasksModel3 = import_mongo_connection181.mongoClientApp.model("th_tasks", TasksSchema3);
var task3 = TasksModel3;

// schemas/isoCode/th/taskerBNPLProcess.server.ts
var import_mongo_connection182 = require("mongo-connection");
var import_mongoose182 = require("mongoose");
var taskerBNPLProcessName3 = "th_taskerBNPLProcess";
var TaskerBNPLProcess3 = new import_mongoose182.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName3 }
);
var TaskerBNPLProcessModel3 = import_mongo_connection182.mongoClientApp.model(
  "th_taskerBNPLProcess",
  TaskerBNPLProcess3
);
var taskerBNPLProcess3 = TaskerBNPLProcessModel3;

// schemas/isoCode/th/taskerBNPLTransaction.server.ts
var import_mongo_connection183 = require("mongo-connection");
var import_mongoose183 = require("mongoose");
var taskerBNPLTransactionName3 = "th_taskerBNPLTransaction";
var TaskerBNPLTransaction3 = new import_mongoose183.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName3 }
);
var TaskerBNPLTransactionModel3 = import_mongo_connection183.mongoClientApp.model(
  taskerBNPLTransactionName3,
  TaskerBNPLTransaction3
);
var taskerBNPLTransaction3 = TaskerBNPLTransactionModel3;

// schemas/isoCode/th/taskerGift.server.ts
var import_mongoose184 = require("mongoose");
var import_mongo_connection184 = require("mongo-connection");
var taskerGiftName3 = "th_taskerGift";
var TaskerGiftSchema3 = new import_mongoose184.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName3 }
);
var TaskerGiftModel3 = import_mongo_connection184.mongoClientApp.model("th_taskerGift", TaskerGiftSchema3);
var taskerGift3 = TaskerGiftModel3;

// schemas/isoCode/th/taskerIncentive.server.ts
var import_mongoose185 = require("mongoose");
var import_mongo_connection185 = require("mongo-connection");
var taskerIncentiveName3 = "th_taskerIncentive";
var TaskerIncentiveSchema3 = new import_mongoose185.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    giftInfo: {},
    social: {},
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: import_mongoose185.Schema.Types.Mixed
    },
    status: {
      $type: String
    },
    codeList: import_mongoose185.Schema.Types.Mixed,
    codeFromPartner: {
      $type: Number
    },
    office: import_mongoose185.Schema.Types.Mixed,
    applyFor: import_mongoose185.Schema.Types.Mixed,
    brandInfo: import_mongoose185.Schema.Types.Mixed,
    redeemLink: import_mongoose185.Schema.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName3 }
);
var TaskerIncentiveModel3 = import_mongo_connection185.mongoClientApp.model(
  "th_taskerIncentive",
  TaskerIncentiveSchema3
);
var taskerIncentive3 = TaskerIncentiveModel3;

// schemas/isoCode/th/taskerOnboardingSetting.server.ts
var import_mongo_connection186 = require("mongo-connection");
var import_mongoose186 = require("mongoose");
var taskerOnboardingSettingName3 = "th_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema3 = new import_mongoose186.Schema(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName3 }
);
var TaskerOnboardingSettingModel3 = import_mongo_connection186.mongoClientApp.model(
  taskerOnboardingSettingName3,
  TaskerOnboardingSettingSchema3
);
var taskerOnboardingSetting3 = TaskerOnboardingSettingModel3;

// schemas/isoCode/th/taskerPointTransaction.server.ts
var import_mongoose187 = require("mongoose");
var import_mongo_connection187 = require("mongo-connection");
var taskerPointTransactionName3 = "th_taskerPointTransaction";
var TaskerPointTransactionSchema3 = new import_mongoose187.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName3 }
);
var TaskerPointTransactionModel3 = import_mongo_connection187.mongoClientApp.model(
  "th_taskerPointTransaction",
  TaskerPointTransactionSchema3
);
var taskerPointTransaction3 = TaskerPointTransactionModel3;

// schemas/isoCode/th/taskerProfile.server.ts
var import_mongoose188 = require("mongoose");
var import_mongo_connection188 = require("mongo-connection");
var taskerProfileName3 = "th_taskerProfile";
var TaskerProfileSchema3 = new import_mongoose188.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName3
  }
);
var TaskerProfileModel3 = import_mongo_connection188.mongoClientApp.model(
  "th_taskerProfile",
  TaskerProfileSchema3
);
var taskerProfile3 = TaskerProfileModel3;

// schemas/isoCode/th/taskerSpecialCampaign.server.ts
var import_mongo_connection189 = require("mongo-connection");
var import_mongoose189 = require("mongoose");
var taskerSpecialCampaignName3 = "th_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema3 = new import_mongoose189.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: import_mongoose189.Schema.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName3, _id: false }
);
var TaskerSpecialCampaignModel3 = import_mongo_connection189.mongoClientApp.model(
  taskerSpecialCampaignName3,
  TaskerSpecialCampaignSchema3
);
var taskerSpecialCampaign3 = TaskerSpecialCampaignModel3;

// schemas/isoCode/th/taskerSpecialCampaignTransaction.server.ts
var import_mongo_connection190 = require("mongo-connection");
var import_mongoose190 = require("mongoose");
var taskerSpecialCampaignTransactionName3 = "th_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema3 = new import_mongoose190.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName3 }
);
var TaskerSpecialCampaignTransactionModel3 = import_mongo_connection190.mongoClientApp.model(
  taskerSpecialCampaignTransactionName3,
  TaskerSpecialCampaignTransactionSchema3
);
var taskerSpecialCampaignTransaction3 = TaskerSpecialCampaignTransactionModel3;

// schemas/isoCode/th/taskerToolkitLadingDetails.server.ts
var import_mongoose191 = require("mongoose");
var import_mongo_connection191 = require("mongo-connection");
var taskerToolkitLadingDetailsName3 = "th_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema3 = new import_mongoose191.Schema(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName3 }
);
var TaskerToolkitLadingDetailsModel3 = import_mongo_connection191.mongoClientApp.model(
  "th_taskerToolkitLadingDetails",
  TaskerToolkitLadingDetailsSchema3
);
var taskerToolkitLadingDetails3 = TaskerToolkitLadingDetailsModel3;

// schemas/isoCode/th/taskerTrainingCourse.server.ts
var import_mongo_connection192 = require("mongo-connection");
var import_mongoose192 = require("mongoose");
var taskerTrainingCourseName3 = "th_trainingTaskerCourse";
var CourseSchema3 = new import_mongoose192.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName3 }
);
var CourseModel3 = import_mongo_connection192.mongoClientApp.model(
  taskerTrainingCourseName3,
  CourseSchema3
);
var taskerTrainingCourse3 = CourseModel3;

// schemas/isoCode/th/taskerTrainingCourseStartDate.ts
var import_mongo_connection193 = require("mongo-connection");
var import_mongoose193 = require("mongoose");
var taskerTrainingCourseStartDateName3 = "th_trainingTaskerCourseStartDate";
var CourseStartDateSchema3 = new import_mongoose193.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName3 }
);
var CourseStartDateModel3 = import_mongo_connection193.mongoClientApp.model(
  taskerTrainingCourseStartDateName3,
  CourseStartDateSchema3
);
var taskerTrainingCourseStartDate3 = CourseStartDateModel3;

// schemas/isoCode/th/taskerTrainingQuiz.server.ts
var import_mongoose194 = require("mongoose");
var import_mongo_connection194 = require("mongo-connection");
var taskerTrainingQuizName3 = "th_trainingTaskerQuiz";
var QuizSchema3 = new import_mongoose194.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName3 }
);
var QuizModel3 = import_mongo_connection194.mongoClientApp.model(taskerTrainingQuizName3, QuizSchema3);
var taskerTrainingQuiz3 = QuizModel3;

// schemas/isoCode/th/taskerTrainingQuizCollection.server.ts
var import_mongoose195 = require("mongoose");
var import_mongo_connection195 = require("mongo-connection");
var taskerTrainingQuizCollectionName3 = "th_trainingTaskerQuizCollection";
var QuizCollectionSchema3 = new import_mongoose195.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName3 }
);
var QuizCollectionModel3 = import_mongo_connection195.mongoClientApp.model(
  taskerTrainingQuizCollectionName3,
  QuizCollectionSchema3
);
var taskerTrainingQuizCollection3 = QuizCollectionModel3;

// schemas/isoCode/th/taskerTrainingSubmission.server.ts
var import_mongoose196 = require("mongoose");
var import_mongo_connection196 = require("mongo-connection");
var taskerTrainingSubmissionName3 = "th_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema3 = new import_mongoose196.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName3 }
);
var TaskerTrainingSubmissionModel3 = import_mongo_connection196.mongoClientApp.model(
  taskerTrainingSubmissionName3,
  TaskerTrainingSubmissionSchema3
);
var taskerTrainingSubmission3 = TaskerTrainingSubmissionModel3;

// schemas/isoCode/th/thingsToKnow.server.ts
var import_mongoose197 = require("mongoose");
var import_mongo_connection197 = require("mongo-connection");
var thingsToKnowName3 = "th_thingsToKnow";
var ThingToKnowSchema3 = new import_mongoose197.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName3 }
);
var ThingsToKnowModel3 = import_mongo_connection197.mongoClientApp.model(
  "th_thingsToKnow",
  ThingToKnowSchema3
);
var thingsToKnow3 = ThingsToKnowModel3;

// schemas/isoCode/th/toolKitItems.server.ts
var import_mongoose198 = require("mongoose");
var import_mongo_connection198 = require("mongo-connection");
var toolKitItemsName3 = "th_toolKitItems";
var ToolKitItemsSchema3 = new import_mongoose198.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName3 }
);
var ToolKitItemsModel3 = import_mongo_connection198.mongoClientApp.model(
  "th_toolKitItems",
  ToolKitItemsSchema3
);
var toolKitItems3 = ToolKitItemsModel3;

// schemas/isoCode/th/toolKitSetting.server.ts
var import_mongoose199 = require("mongoose");
var import_mongo_connection199 = require("mongo-connection");
var toolKitSettingName3 = "th_toolKitSetting";
var ToolKitSettingSchema3 = new import_mongoose199.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName3 }
);
var ToolKitSettingModel3 = import_mongo_connection199.mongoClientApp.model(
  "th_toolKitSetting",
  ToolKitSettingSchema3
);
var toolKitSetting3 = ToolKitSettingModel3;

// schemas/isoCode/th/trainingJourney.server.ts
var import_mongoose200 = require("mongoose");
var import_mongo_connection200 = require("mongo-connection");
var trainingJourneyName3 = "th_trainingJourney";
var TrainingJourneySchema3 = new import_mongoose200.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName3 }
);
var TrainingJourneyModel3 = import_mongo_connection200.mongoClientApp.model(
  "th_trainingJourney",
  TrainingJourneySchema3
);
var trainingJourney3 = TrainingJourneyModel3;

// schemas/isoCode/th/trainingTasker.server.ts
var import_mongoose201 = require("mongoose");
var import_mongo_connection201 = require("mongo-connection");
var trainingTaskerName3 = "th_trainingTasker";
var TrainingTaskerSchema3 = new import_mongoose201.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName3 }
);
var TrainingTaskerModel3 = import_mongo_connection201.mongoClientApp.model(
  "th_trainingTasker",
  TrainingTaskerSchema3
);
var trainingTasker3 = TrainingTaskerModel3;

// schemas/isoCode/th/userActivation.server.ts
var import_mongo_connection202 = require("mongo-connection");
var import_mongoose202 = require("mongoose");
var userActivationName3 = "th_userActivation";
var UserActivationSchema3 = new import_mongoose202.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName3 }
);
var UserActivationModel3 = import_mongo_connection202.mongoClientApp.model(
  userActivationName3,
  UserActivationSchema3
);
var userActivation3 = UserActivationModel3;

// schemas/isoCode/th/userApp.server.ts
var import_mongo_connection203 = require("mongo-connection");
var import_mongoose203 = require("mongoose");
var usersName3 = "users";
var UsersAppSchema3 = new import_mongoose203.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName3 }
);
var UsersAppModel3 = import_mongo_connection203.mongoClientApp.model("th_users", UsersAppSchema3);
var users3 = UsersAppModel3;

// schemas/isoCode/th/userComboVoucher.server.ts
var import_mongo_connection204 = require("mongo-connection");
var import_mongoose204 = require("mongoose");
var userComboVoucherName3 = "th_userComboVoucher";
var UserComboVoucherSchema3 = new import_mongoose204.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: import_mongoose204.Schema.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: import_mongoose204.Schema.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName3 }
);
var UserComboVoucherModel3 = import_mongo_connection204.mongoClientApp.model(
  userComboVoucherName3,
  UserComboVoucherSchema3
);
var userComboVoucher3 = UserComboVoucherModel3;

// schemas/isoCode/th/userLocationHistory.server.ts
var import_mongoose205 = require("mongoose");
var import_mongo_connection205 = require("mongo-connection");
var userLocationHistoryName3 = "th_userLocationHistory";
var UserLocationHistorySchema3 = new import_mongoose205.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName3 }
);
var UserLocationHistoryModel3 = import_mongo_connection205.mongoClientApp.model(
  userLocationHistoryName3,
  UserLocationHistorySchema3
);
var userLocationHistory3 = UserLocationHistoryModel3;

// schemas/isoCode/th/userProfile.server.ts
var import_mongo_connection206 = require("mongo-connection");
var import_mongoose206 = require("mongoose");
var userProfileName3 = "th_userProfile";
var UserProfileSchema3 = new import_mongoose206.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: import_mongoose206.Schema.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName3 }
);
var UserProfileModel3 = import_mongo_connection206.mongoClientApp.model(userProfileName3, UserProfileSchema3);
var userProfile3 = UserProfileModel3;

// schemas/isoCode/th/workingPlaces.server.ts
var import_mongoose207 = require("mongoose");
var import_mongo_connection207 = require("mongo-connection");
var workingPlacesName3 = "th_workingPlaces";
var WorkingPlacesSchema3 = new import_mongoose207.Schema(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName3 }
);
var WorkingPlacesModel3 = import_mongo_connection207.mongoClientApp.model(
  "th_workingPlaces",
  WorkingPlacesSchema3
);
var workingPlaces3 = WorkingPlacesModel3;

// schemas/isoCode/th/index.server.ts
var TH = {
  task: task3,
  historyTasks: historyTasks3,
  userLocationHistory: userLocationHistory3,
  trainingTasker: trainingTasker3,
  trainingJourney: trainingJourney3,
  toolKitSetting: toolKitSetting3,
  toolKitItems: toolKitItems3,
  thingsToKnow: thingsToKnow3,
  taskerTrainingSubmission: taskerTrainingSubmission3,
  taskerTrainingQuizCollection: taskerTrainingQuizCollection3,
  taskerTrainingQuiz: taskerTrainingQuiz3,
  taskerTrainingCourse: taskerTrainingCourse3,
  taskerToolkitLadingDetails: taskerToolkitLadingDetails3,
  financialAccount: financialAccount3,
  taskerIncentive: taskerIncentive3,
  taskerPointTransaction: taskerPointTransaction3,
  taskerGift: taskerGift3,
  notification: notification3,
  taskerProfile: taskerProfile3,
  employeeProfile: employeeProfile3,
  workingPlaces: workingPlaces3,
  flashSale: flashSale3,
  incentive: incentive3,
  promotionCode: promotionCode3,
  promotionSource: promotionSource3,
  service: service3,
  promotionHistory: promotionHistory3,
  settingCountry: settingCountry3,
  comboVoucher: comboVoucher3,
  referralCampaign: referralCampaign3,
  marketingCampaign: marketingCampaign3,
  settingSystem: settingSystem3,
  partnerDirectory: partnerDirectory3,
  partnerRequest: partnerRequest3,
  communityComment: communityComment3,
  communityMedal: communityMedal3,
  communityNotification: communityNotification3,
  communityPost: communityPost3,
  communitySetting: communitySetting3,
  communityTag: communityTag3,
  communityUser: communityUser3,
  communityUserReport: communityUserReport3,
  subscription: subscription3,
  taskerOnboardingSetting: taskerOnboardingSetting3,
  serviceChannel: serviceChannel3,
  users: users3,
  bEmployee: bEmployee3,
  bEmployeeSetting: bEmployeeSetting3,
  FATransaction: FATransaction3,
  taskerBNPLTransaction: taskerBNPLTransaction3,
  taskerBNPLProcess: taskerBNPLProcess3,
  paymentToolKitTransaction: paymentToolKitTransaction3,
  rating: rating3,
  userActivation: userActivation3,
  business: business3,
  businessLevel: businessLevel3,
  businessMember: businessMember3,
  businessMemberTransaction: businessMemberTransaction3,
  businessTransaction: businessTransaction3,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocation3,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDate3,
  taskerSpecialCampaign: taskerSpecialCampaign3,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransaction3,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethod3,
  userProfile: userProfile3,
  communityTagOrder: communityTagOrder3,
  journeySetting: journeySetting3,
  userComboVoucher: userComboVoucher3,
  bundleVoucher: bundleVoucher3
};
var THName = {
  task: taskName3,
  historyTasks: historyTasksName3,
  userLocationHistory: userLocationHistoryName3,
  trainingTasker: trainingTaskerName3,
  trainingJourney: trainingJourneyName3,
  toolKitSetting: toolKitSettingName3,
  toolKitItems: toolKitItemsName3,
  thingsToKnow: thingsToKnowName3,
  taskerTrainingSubmission: taskerTrainingSubmissionName3,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName3,
  taskerTrainingQuiz: taskerTrainingQuizName3,
  taskerTrainingCourse: taskerTrainingCourseName3,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName3,
  taskerPointTransaction: taskerPointTransactionName3,
  taskerIncentive: taskerIncentiveName3,
  taskerGift: taskerGiftName3,
  notification: notificationName3,
  financialAccount: financialAccountName3,
  taskerProfile: taskerProfileName3,
  employeeProfile: employeeProfileName3,
  workingPlaces: workingPlacesName3,
  service: serviceName3,
  settingCountry: settingCountryName3,
  settingSystem: settingSystemName3,
  incentive: incentiveName3,
  communityComment: communityCommentName3,
  communityMedal: communityMedalName3,
  communityNotification: communityNotificationName3,
  communityPost: communityPostName3,
  communitySetting: communitySettingName3,
  communityTag: communityTagName3,
  communityUser: communityUserName3,
  communityUserReport: communityUserReportName3,
  subscription: subscriptionName3,
  taskerOnboardingSetting: taskerOnboardingSettingName3,
  users: usersName3,
  serviceChannel: serviceChannelName3,
  bEmployee: bEmployeeName3,
  bEmployeeSetting: bEmployeeSettingName3,
  FATransaction: FATransactionName3,
  taskerBNPLTransaction: taskerBNPLTransactionName3,
  taskerBNPLProcess: taskerBNPLProcessName3,
  paymentToolKitTransaction: paymentToolKitTransactionName3,
  promotionCode: promotionCodeName3,
  promotionHistory: promotionHistoryName3,
  marketingCampaign: marketingCampaignName3,
  rating: ratingName3,
  userActivation: userActivationName3,
  business: businessName3,
  businessMember: businessMemberName3,
  businessLevel: businessLevelName3,
  businessMemberTransaction: businessMemberTransactionName3,
  businessTransaction: businessTransactionName3,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName3,
  comboVoucher: comboVoucherName3,
  flashSale: flashSaleName3,
  partnerDirectory: partnerDirectoryName3,
  partnerRequest: partnerRequestName3,
  promotionSource: promotionSourceName3,
  referralCampaign: referralCampaignName3,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName3,
  taskerSpecialCampaign: taskerSpecialCampaignName3,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName3,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName3,
  userProfile: userProfileName3,
  communityTagOrder: communityTagOrderName3,
  journeySetting: journeySettingName3,
  userComboVoucher: userComboVoucherName3,
  bundleVoucher: bundleVoucherName3
};
var index_server_default3 = TH;

// schemas/isoCode/vn/bEmployee.server.ts
var import_mongo_connection208 = require("mongo-connection");
var import_mongoose208 = require("mongoose");
var bEmployeeName4 = "bEmployee";
var BEmployeeSchema4 = new import_mongoose208.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName4 }
);
var BEmployeeModel4 = import_mongo_connection208.mongoClientApp.model("vn_bEmployee", BEmployeeSchema4);
var bEmployee4 = BEmployeeModel4;

// schemas/isoCode/vn/bEmployeeSetting.server.ts
var import_mongo_connection209 = require("mongo-connection");
var import_mongoose209 = require("mongoose");
var bEmployeeSettingName4 = "vn_bEmployeeSetting";
var BEmployeeSettingSchema4 = new import_mongoose209.Schema(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName4 }
);
var BEmployeeSettingModel4 = import_mongo_connection209.mongoClientApp.model(
  "vn_bEmployeeSetting",
  BEmployeeSettingSchema4
);
var bEmployeeSetting4 = BEmployeeSettingModel4;

// schemas/isoCode/vn/bundleVoucher.ts
var import_mongo_connection210 = require("mongo-connection");
var import_mongoose210 = require("mongoose");
var bundleVoucherName4 = "vn_bundleVoucher";
var BundleVoucherSchema4 = new import_mongoose210.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose210.Schema.Types.Mixed },
            nextData: { $type: import_mongoose210.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose210.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName4 }
);
var BundleVoucherModel4 = import_mongo_connection210.mongoClientApp.model(
  bundleVoucherName4,
  BundleVoucherSchema4
);
var bundleVoucher4 = BundleVoucherModel4;

// schemas/isoCode/vn/campaign-payment.server.ts
var import_mongo_connection211 = require("mongo-connection");
var import_mongoose211 = require("mongoose");
var marketingCampaignPaymentMethodName4 = "vn_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema4 = new import_mongoose211.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: import_mongoose211.Schema.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName4 }
);
var MarketingCampaignPaymentMethodModel4 = import_mongo_connection211.mongoClientApp.model(
  marketingCampaignPaymentMethodName4,
  MarketingCampaignPaymentMethodSchema4
);
var marketingCampaignPaymentMethod4 = MarketingCampaignPaymentMethodModel4;

// schemas/isoCode/vn/campaign.server.ts
var import_mongo_connection212 = require("mongo-connection");
var import_mongoose212 = require("mongoose");
var marketingCampaignName4 = "vn_marketingCampaign";
var MarketingCampaignSchema4 = new import_mongoose212.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: import_mongoose212.Schema.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: import_mongoose212.Schema.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose212.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose212.Schema.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: import_mongoose212.Schema.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: import_mongoose212.Schema.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName4 }
);
var MarketingCampaignModel4 = import_mongo_connection212.mongoClientApp.model(
  marketingCampaignName4,
  MarketingCampaignSchema4
);
var marketingCampaign4 = MarketingCampaignModel4;

// schemas/isoCode/vn/comboVoucher.server.ts
var import_mongo_connection213 = require("mongo-connection");
var import_mongoose213 = require("mongoose");
var comboVoucherName4 = "vn_comboVoucher";
var ComboVoucherSchema4 = new import_mongoose213.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName4 }
);
var ComboVoucherModel4 = import_mongo_connection213.mongoClientApp.model(
  comboVoucherName4,
  ComboVoucherSchema4
);
var comboVoucher4 = ComboVoucherModel4;

// schemas/isoCode/vn/communityComment.server.ts
var import_mongo_connection214 = require("mongo-connection");
var import_mongoose214 = require("mongoose");
var communityCommentName4 = "vn_communityComment";
var CommunityCommentSchema4 = new import_mongoose214.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName4 }
);
var CommunityCommentModel4 = import_mongo_connection214.mongoClientApp.model(
  communityCommentName4,
  CommunityCommentSchema4
);
var communityComment4 = CommunityCommentModel4;

// schemas/isoCode/vn/communityMedal.server.ts
var import_mongo_connection215 = require("mongo-connection");
var import_mongoose215 = require("mongoose");
var communityMedalName4 = "vn_communityMedal";
var CommunityMedalSchema4 = new import_mongoose215.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: import_mongoose215.Schema.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: import_mongoose215.Schema.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: import_mongoose215.Schema.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName4 }
);
var CommunityMedalModel4 = import_mongo_connection215.mongoClientApp.model(
  communityMedalName4,
  CommunityMedalSchema4
);
var communityMedal4 = CommunityMedalModel4;

// schemas/isoCode/vn/communityNotification.server.ts
var import_mongo_connection216 = require("mongo-connection");
var import_mongoose216 = require("mongoose");
var communityNotificationName4 = "vn_communityNotification";
var CommunityNotificationSchema4 = new import_mongoose216.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: import_mongoose216.Schema.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName4 }
);
var CommunityNotificationModel4 = import_mongo_connection216.mongoClientApp.model(
  communityNotificationName4,
  CommunityNotificationSchema4
);
var communityNotification4 = CommunityNotificationModel4;

// schemas/isoCode/vn/communityOrderTag.server.ts
var import_mongo_connection217 = require("mongo-connection");
var import_mongoose217 = require("mongoose");
var communityTagOrderName4 = "vn_communityTagOrder";
var CommunityTagOrderSchema4 = new import_mongoose217.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: import_mongoose217.Schema.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: import_mongoose217.Schema.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName4 }
);
var CommunityTagOrderModel4 = import_mongo_connection217.mongoClientApp.model(
  communityTagOrderName4,
  CommunityTagOrderSchema4
);
var communityTagOrder4 = CommunityTagOrderModel4;

// schemas/isoCode/vn/communityPost.server.ts
var import_mongo_connection218 = require("mongo-connection");
var import_mongoose218 = require("mongoose");
var communityPostName4 = "vn_communityPost";
var CommunityPostSchema4 = new import_mongoose218.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: import_mongoose218.Schema.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean },
    isPinned: { $type: Boolean, default: false }
  },
  { typeKey: "$type", collection: communityPostName4 }
);
var CommunityPostModel4 = import_mongo_connection218.mongoClientApp.model(
  communityPostName4,
  CommunityPostSchema4
);
var communityPost4 = CommunityPostModel4;

// schemas/isoCode/vn/communityTag.server.ts
var import_mongo_connection219 = require("mongo-connection");
var import_mongoose219 = require("mongoose");
var communityTagName4 = "vn_communityTag";
var CommunityTagSchema4 = new import_mongoose219.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: import_mongoose219.Schema.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: import_mongoose219.Schema.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName4 }
);
var CommunityTagModel4 = import_mongo_connection219.mongoClientApp.model(
  communityTagName4,
  CommunityTagSchema4
);
var communityTag4 = CommunityTagModel4;

// schemas/isoCode/vn/communityUser.server.ts
var import_mongo_connection220 = require("mongo-connection");
var import_mongoose220 = require("mongoose");
var communityUserName4 = "vn_communityUser";
var CommunityUserSchema4 = new import_mongoose220.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName4 }
);
var CommunityUserModel4 = import_mongo_connection220.mongoClientApp.model(
  communityUserName4,
  CommunityUserSchema4
);
var communityUser4 = CommunityUserModel4;

// schemas/isoCode/vn/communitySetting.server.ts
var import_mongo_connection221 = require("mongo-connection");
var import_mongoose221 = require("mongoose");
var communitySettingName4 = "vn_communitySetting";
var CommunitySettingSchema4 = new import_mongoose221.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: import_mongoose221.Schema.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName4
  }
);
var CommunitySettingModel4 = import_mongo_connection221.mongoClientApp.model(
  communitySettingName4,
  CommunitySettingSchema4
);
var communitySetting4 = CommunitySettingModel4;

// schemas/isoCode/vn/communityUserReport.server.ts
var import_mongo_connection222 = require("mongo-connection");
var import_mongoose222 = require("mongoose");
var communityUserReportName4 = "vn_communityUserReport";
var CommunityUserReportSchema4 = new import_mongoose222.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName4 }
);
var CommunityUserReportModel4 = import_mongo_connection222.mongoClientApp.model(
  communityUserReportName4,
  CommunityUserReportSchema4
);
var communityUserReport4 = CommunityUserReportModel4;

// schemas/isoCode/vn/employeeProfile.server.ts
var import_mongoose223 = require("mongoose");
var import_mongo_connection223 = require("mongo-connection");
var employeeProfileName4 = "vn_employeeProfile";
var EmployeeProfileSchema4 = new import_mongoose223.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName4 }
);
var EmployeeProfileModel4 = import_mongo_connection223.mongoClientApp.model(
  "vn_employeeProfile",
  EmployeeProfileSchema4
);
var employeeProfile4 = EmployeeProfileModel4;

// schemas/isoCode/vn/FATransaction.server.ts
var import_mongo_connection224 = require("mongo-connection");
var import_mongoose224 = require("mongoose");
var FATransactionName4 = "FATransaction";
var FATransactionSchema4 = new import_mongoose224.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName4 }
);
var FATransactionModel4 = import_mongo_connection224.mongoClientApp.model(
  "vn_FATransaction",
  FATransactionSchema4
);
var FATransaction4 = FATransactionModel4;

// schemas/isoCode/vn/financialAccount.server.ts
var import_mongoose225 = require("mongoose");
var import_mongo_connection225 = require("mongo-connection");
var financialAccountName4 = "vn_financialAccount";
var FinancialAccountSchema4 = new import_mongoose225.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName4 }
);
var FinancialAccountModel4 = import_mongo_connection225.mongoClientApp.model(
  "vn_financialAccount",
  FinancialAccountSchema4
);
var financialAccount4 = FinancialAccountModel4;

// schemas/isoCode/vn/flashSale.server.ts
var import_mongo_connection226 = require("mongo-connection");
var import_mongoose226 = require("mongoose");
var flashSaleName4 = "vn_askerFlashSaleIncentive";
var FlashSaleSchema4 = new import_mongoose226.Schema(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName4 }
);
var FlashSaleModel4 = import_mongo_connection226.mongoClientApp.model(
  flashSaleName4,
  FlashSaleSchema4
);
var flashSale4 = FlashSaleModel4;

// schemas/isoCode/vn/historyTasks.server.ts
var import_mongo_connection227 = require("mongo-connection");
var import_mongoose227 = require("mongoose");
var historyTasksName4 = "history_tasks";
var HistoryTasksSchema4 = new import_mongoose227.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose227.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose227.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose227.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose227.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName4 }
);
var HistoryTasksModel4 = import_mongo_connection227.mongoClientApp.model("vn_history_tasks", HistoryTasksSchema4);
var historyTasks4 = HistoryTasksModel4;

// schemas/isoCode/vn/incentive.server.ts
var import_mongo_connection228 = require("mongo-connection");
var import_mongoose228 = require("mongoose");
var incentiveName4 = "vn_incentive";
var IncentiveSchema4 = new import_mongoose228.Schema(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName4 }
);
var IncentiveModel4 = import_mongo_connection228.mongoClientApp.model(incentiveName4, IncentiveSchema4);
var incentive4 = IncentiveModel4;

// schemas/isoCode/vn/journeySetting.server.ts
var import_mongo_connection229 = require("mongo-connection");
var import_mongoose229 = require("mongoose");
var journeySettingName4 = "vn_journeySetting";
var JourneySettingSchema4 = new import_mongoose229.Schema(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName4 }
);
var JourneySettingModel4 = import_mongo_connection229.mongoClientApp.model(
  journeySettingName4,
  JourneySettingSchema4
);
var journeySetting4 = JourneySettingModel4;

// schemas/isoCode/vn/notification.server.ts
var import_mongoose230 = require("mongoose");
var import_mongo_connection230 = require("mongo-connection");
var notificationName4 = "vn_notification";
var NotificationSchema4 = new import_mongoose230.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName4 }
);
var NotificationModel4 = import_mongo_connection230.mongoClientApp.model(
  "vn_notification",
  NotificationSchema4
);
var notification4 = NotificationModel4;

// schemas/isoCode/vn/partnerBusiness.server.ts
var import_mongo_connection231 = require("mongo-connection");
var import_mongoose231 = require("mongoose");
var businessName4 = "vn_business";
var BusinessSchema4 = new import_mongoose231.Schema(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [{ name: { $type: String }, url: { $type: String } }],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: import_mongoose231.Schema.Types.Mixed },
            nextData: { $type: import_mongoose231.Schema.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: import_mongoose231.Schema.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      //Added a getter function that ensures the values are stored as floating-point numbers with 2 decimal places
      period: { $type: Number, get: (v) => parseFloat(v.toFixed(2)) },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number, get: (v) => parseFloat(v.toFixed(2)) },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName4 }
);
var BusinessModel4 = import_mongo_connection231.mongoClientApp.model(businessName4, BusinessSchema4);
var business4 = BusinessModel4;

// schemas/isoCode/vn/partnerBusinessLevel.server.ts
var import_mongoose232 = require("mongoose");
var import_mongo_connection232 = require("mongo-connection");
var businessLevelName4 = "vn_businessLevel";
var BusinessLevelSchema4 = new import_mongoose232.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName4 }
);
var BusinessLevelModel4 = import_mongo_connection232.mongoClientApp.model(
  businessLevelName4,
  BusinessLevelSchema4
);
var businessLevel4 = BusinessLevelModel4;

// schemas/isoCode/vn/partnerBusinessMember.server.ts
var import_mongoose233 = require("mongoose");
var import_mongo_connection233 = require("mongo-connection");
var businessMemberName4 = "vn_businessMember";
var BusinessMemberSchema4 = new import_mongoose233.Schema(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName4 }
);
var BusinessMemberModel4 = import_mongo_connection233.mongoClientApp.model(
  businessMemberName4,
  BusinessMemberSchema4
);
var businessMember4 = BusinessMemberModel4;

// schemas/isoCode/vn/partnerBusinessMemberTransaction.server.ts
var import_mongo_connection234 = require("mongo-connection");
var import_mongoose234 = require("mongoose");
var businessMemberTransactionName4 = "vn_businessMemberTransaction";
var BusinessMemberTransactionSchema4 = new import_mongoose234.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    taskId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    userId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName4 }
);
var BusinessMemberTransactionModel4 = import_mongo_connection234.mongoClientApp.model(businessMemberTransactionName4, BusinessMemberTransactionSchema4);
var businessMemberTransaction4 = BusinessMemberTransactionModel4;

// schemas/isoCode/vn/partnerBusinessSetupAllocateAndReallocate.server.ts
var import_mongo_connection235 = require("mongo-connection");
var import_mongoose235 = require("mongoose");
var businessSetupAllocationAndReallocationName4 = "vn_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema4 = new import_mongoose235.Schema(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName4 }
);
var BusinessSetupAllocationAndReallocationModel4 = import_mongo_connection235.mongoClientApp.model(businessSetupAllocationAndReallocationName4, BusinessSetupAllocationAndReallocationSchema4);
var businessSetupAllocationAndReallocation4 = BusinessSetupAllocationAndReallocationModel4;

// schemas/isoCode/vn/partnerBusinessTransaction.server.ts
var import_mongo_connection236 = require("mongo-connection");
var import_mongoose236 = require("mongoose");
var businessTransactionName4 = "vn_businessTransaction";
var BusinessTransactionSchema4 = new import_mongoose236.Schema(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName4 }
);
var BusinessTransactionModel4 = import_mongo_connection236.mongoClientApp.model(
  businessTransactionName4,
  BusinessTransactionSchema4
);
var businessTransaction4 = BusinessTransactionModel4;

// schemas/isoCode/vn/partnerDirectory.server.ts
var import_mongoose237 = require("mongoose");
var import_mongo_connection237 = require("mongo-connection");
var partnerDirectoryName4 = "vn_partnerDirectory";
var PartnerDirectorySchema4 = new import_mongoose237.Schema(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName4 }
);
var PartnerDirectoryModel4 = import_mongo_connection237.mongoClientApp.model(
  partnerDirectoryName4,
  PartnerDirectorySchema4
);
var partnerDirectory4 = PartnerDirectoryModel4;

// schemas/isoCode/vn/partnerRequest.server.ts
var import_mongoose238 = require("mongoose");
var import_mongo_connection238 = require("mongo-connection");
var partnerRequestName4 = "vn_partnerRequest";
var PartnerRequestSchema4 = new import_mongoose238.Schema(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName4 }
);
var PartnerRequestModel4 = import_mongo_connection238.mongoClientApp.model(
  partnerRequestName4,
  PartnerRequestSchema4
);
var partnerRequest4 = PartnerRequestModel4;

// schemas/isoCode/vn/paymentToolKitTransaction.server.ts
var import_mongo_connection239 = require("mongo-connection");
var import_mongoose239 = require("mongoose");
var paymentToolKitTransactionName4 = "vn_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema4 = new import_mongoose239.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName4 }
);
var PaymentToolKitTransactionModel4 = import_mongo_connection239.mongoClientApp.model(
  paymentToolKitTransactionName4,
  PaymentToolKitTransactionSchema4
);
var paymentToolKitTransaction4 = PaymentToolKitTransactionModel4;

// schemas/isoCode/vn/promotionCode.server.ts
var import_mongo_connection240 = require("mongo-connection");
var import_mongoose240 = require("mongoose");
var promotionCodeName4 = "vn_promotionCode";
var PromotionCodeSchema4 = new import_mongoose240.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: import_mongoose240.Schema.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName4 }
);
var PromotionCodeModel4 = import_mongo_connection240.mongoClientApp.model(
  promotionCodeName4,
  PromotionCodeSchema4
);
var promotionCode4 = PromotionCodeModel4;

// schemas/isoCode/vn/promotionHistory.server.ts
var import_mongo_connection241 = require("mongo-connection");
var import_mongoose241 = require("mongoose");
var promotionHistoryName4 = "vn_promotionHistory";
var PromotionHistorySchema4 = new import_mongoose241.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName4 }
);
var PromotionHistoryModel4 = import_mongo_connection241.mongoClientApp.model(
  promotionHistoryName4,
  PromotionHistorySchema4
);
var promotionHistory4 = PromotionHistoryModel4;

// schemas/isoCode/vn/promotionSource.server.ts
var import_mongoose242 = require("mongoose");
var import_mongo_connection242 = require("mongo-connection");
var promotionSourceName4 = "vn_promotionSource";
var PromotionSourceSchema4 = new import_mongoose242.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName4 }
);
var PromotionSourceModel4 = import_mongo_connection242.mongoClientApp.model(
  promotionSourceName4,
  PromotionSourceSchema4
);
var promotionSource4 = PromotionSourceModel4;

// schemas/isoCode/vn/rating.server.ts
var import_mongo_connection243 = require("mongo-connection");
var import_mongoose243 = require("mongoose");
var ratingName4 = "vn_rating";
var RatingSchema4 = new import_mongoose243.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName4 }
);
var RatingModel4 = import_mongo_connection243.mongoClientApp.model(ratingName4, RatingSchema4);
var rating4 = RatingModel4;

// schemas/isoCode/vn/referralCampaign.server.ts
var import_mongo_connection244 = require("mongo-connection");
var import_mongoose244 = require("mongoose");
var referralCampaignName4 = "vn_askerReferralCampaign";
var ReferralCampaignSchema4 = new import_mongoose244.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: import_mongoose244.Schema.Types.Mixed, required: true },
    inviter: { $type: import_mongoose244.Schema.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName4 }
);
var ReferralCampaignModel4 = import_mongo_connection244.mongoClientApp.model(
  referralCampaignName4,
  ReferralCampaignSchema4
);
var referralCampaign4 = ReferralCampaignModel4;

// schemas/isoCode/vn/service.server.ts
var import_mongo_connection245 = require("mongo-connection");
var import_mongoose245 = require("mongoose");
var serviceName4 = "service";
var ServiceSchema4 = new import_mongoose245.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: import_mongoose245.Schema.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: import_mongoose245.Schema.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: import_mongoose245.Schema.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName4 }
);
var ServiceModel4 = import_mongo_connection245.mongoClientApp.model("vn_service", ServiceSchema4);
var service4 = ServiceModel4;

// schemas/isoCode/vn/serviceChannel.server.ts
var import_mongo_connection246 = require("mongo-connection");
var import_mongoose246 = require("mongoose");
var serviceChannelName4 = "vn_serviceChannel";
var ServiceChannelSchema4 = new import_mongoose246.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName4 }
);
var ServiceChannelModel4 = import_mongo_connection246.mongoClientApp.model(
  "vn_serviceChannel",
  ServiceChannelSchema4
);
var serviceChannel4 = ServiceChannelModel4;

// schemas/isoCode/vn/settingCountry.server.ts
var import_mongo_connection247 = require("mongo-connection");
var import_mongoose247 = require("mongoose");
var settingCountryName4 = "vn_settingCountry";
var SettingCountrySchema4 = new import_mongoose247.Schema(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName4 }
);
var SettingCountryModel4 = import_mongo_connection247.mongoClientApp.model(
  settingCountryName4,
  SettingCountrySchema4
);
var settingCountry4 = SettingCountryModel4;

// schemas/isoCode/vn/settingSystem.server.ts
var import_mongo_connection248 = require("mongo-connection");
var import_mongoose248 = require("mongoose");
var settingSystemName4 = "settingSystem";
var SettingSystemSchema4 = new import_mongoose248.Schema(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: import_mongoose248.Schema.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName4 }
);
var SettingSystemModel4 = import_mongo_connection248.mongoClientApp.model(
  "vn_settingSystem",
  SettingSystemSchema4
);
var settingSystem4 = SettingSystemModel4;

// schemas/isoCode/vn/subscription.server.ts
var import_mongo_connection249 = require("mongo-connection");
var import_mongoose249 = require("mongoose");
var subscriptionName4 = "vn_subscription";
var SubscriptionSchema4 = new import_mongoose249.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: import_mongoose249.Schema.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: import_mongoose249.Schema.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName4 }
);
var SubscriptionModel4 = import_mongo_connection249.mongoClientApp.model(subscriptionName4, SubscriptionSchema4);
var subscription4 = SubscriptionModel4;

// schemas/isoCode/vn/task.server.ts
var import_mongo_connection250 = require("mongo-connection");
var import_mongoose250 = require("mongoose");
var taskName4 = "vn_task";
var TasksSchema4 = new import_mongoose250.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: import_mongoose250.Schema.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: import_mongoose250.Schema.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: import_mongoose250.Schema.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: import_mongoose250.Schema.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: taskName4 }
);
var TasksModel4 = import_mongo_connection250.mongoClientApp.model("vn_tasks", TasksSchema4);
var task4 = TasksModel4;

// schemas/isoCode/vn/taskerBNPLProcess.server.ts
var import_mongo_connection251 = require("mongo-connection");
var import_mongoose251 = require("mongoose");
var taskerBNPLProcessName4 = "vn_taskerBNPLProcess";
var TaskerBNPLProcess4 = new import_mongoose251.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName4 }
);
var TaskerBNPLProcessModel4 = import_mongo_connection251.mongoClientApp.model(
  "vn_taskerBNPLProcess",
  TaskerBNPLProcess4
);
var taskerBNPLProcess4 = TaskerBNPLProcessModel4;

// schemas/isoCode/vn/taskerBNPLTransaction.server.ts
var import_mongo_connection252 = require("mongo-connection");
var import_mongoose252 = require("mongoose");
var taskerBNPLTransactionName4 = "vn_taskerBNPLTransaction";
var TaskerBNPLTransaction4 = new import_mongoose252.Schema(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName4 }
);
var TaskerBNPLTransactionModel4 = import_mongo_connection252.mongoClientApp.model(
  taskerBNPLTransactionName4,
  TaskerBNPLTransaction4
);
var taskerBNPLTransaction4 = TaskerBNPLTransactionModel4;

// schemas/isoCode/vn/taskerGift.server.ts
var import_mongoose253 = require("mongoose");
var import_mongo_connection253 = require("mongo-connection");
var taskerGiftName4 = "vn_taskerGift";
var TaskerGiftSchema4 = new import_mongoose253.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName4 }
);
var TaskerGiftModel4 = import_mongo_connection253.mongoClientApp.model("vn_taskerGift", TaskerGiftSchema4);
var taskerGift4 = TaskerGiftModel4;

// schemas/isoCode/vn/taskerIncentive.server.ts
var import_mongoose254 = require("mongoose");
var import_mongo_connection254 = require("mongo-connection");
var taskerIncentiveName4 = "vn_taskerIncentive";
var TaskerIncentiveSchema4 = new import_mongoose254.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    giftInfo: {},
    social: {},
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: import_mongoose254.Schema.Types.Mixed
    },
    status: {
      $type: String
    },
    codeList: import_mongoose254.Schema.Types.Mixed,
    codeFromPartner: {
      $type: Number
    },
    office: import_mongoose254.Schema.Types.Mixed,
    applyFor: import_mongoose254.Schema.Types.Mixed,
    brandInfo: import_mongoose254.Schema.Types.Mixed,
    redeemLink: import_mongoose254.Schema.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName4 }
);
var TaskerIncentiveModel4 = import_mongo_connection254.mongoClientApp.model(
  "vn_taskerIncentive",
  TaskerIncentiveSchema4
);
var taskerIncentive4 = TaskerIncentiveModel4;

// schemas/isoCode/vn/taskerOnboardingSetting.server.ts
var import_mongo_connection255 = require("mongo-connection");
var import_mongoose255 = require("mongoose");
var taskerOnboardingSettingName4 = "vn_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema4 = new import_mongoose255.Schema(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName4 }
);
var TaskerOnboardingSettingModel4 = import_mongo_connection255.mongoClientApp.model(
  taskerOnboardingSettingName4,
  TaskerOnboardingSettingSchema4
);
var taskerOnboardingSetting4 = TaskerOnboardingSettingModel4;

// schemas/isoCode/vn/taskerPointTransaction.server.ts
var import_mongoose256 = require("mongoose");
var import_mongo_connection256 = require("mongo-connection");
var taskerPointTransactionName4 = "vn_taskerPointTransaction";
var TaskerPointTransactionSchema4 = new import_mongoose256.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName4 }
);
var TaskerPointTransactionModel4 = import_mongo_connection256.mongoClientApp.model(
  "vn_taskerPointTransaction",
  TaskerPointTransactionSchema4
);
var taskerPointTransaction4 = TaskerPointTransactionModel4;

// schemas/isoCode/vn/taskerProfile.server.ts
var import_mongoose257 = require("mongoose");
var import_mongo_connection257 = require("mongo-connection");
var taskerProfileName4 = "taskerProfile";
var TaskerProfileSchema4 = new import_mongoose257.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName4
  }
);
var TaskerProfileModel4 = import_mongo_connection257.mongoClientApp.model(
  "vn_taskerProfile",
  TaskerProfileSchema4
);
var taskerProfile4 = TaskerProfileModel4;

// schemas/isoCode/vn/taskerSpecialCampaign.server.ts
var import_mongo_connection258 = require("mongo-connection");
var import_mongoose258 = require("mongoose");
var taskerSpecialCampaignName4 = "vn_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema4 = new import_mongoose258.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: import_mongoose258.Schema.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName4, _id: false }
);
var TaskerSpecialCampaignModel4 = import_mongo_connection258.mongoClientApp.model(
  taskerSpecialCampaignName4,
  TaskerSpecialCampaignSchema4
);
var taskerSpecialCampaign4 = TaskerSpecialCampaignModel4;

// schemas/isoCode/vn/taskerSpecialCampaignTransaction.server.ts
var import_mongo_connection259 = require("mongo-connection");
var import_mongoose259 = require("mongoose");
var taskerSpecialCampaignTransactionName4 = "vn_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema4 = new import_mongoose259.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName4 }
);
var TaskerSpecialCampaignTransactionModel4 = import_mongo_connection259.mongoClientApp.model(
  taskerSpecialCampaignTransactionName4,
  TaskerSpecialCampaignTransactionSchema4
);
var taskerSpecialCampaignTransaction4 = TaskerSpecialCampaignTransactionModel4;

// schemas/isoCode/vn/taskerToolkitLadingDetails.server.ts
var import_mongoose260 = require("mongoose");
var import_mongo_connection260 = require("mongo-connection");
var taskerToolkitLadingDetailsName4 = "vn_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema4 = new import_mongoose260.Schema(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number },
    toolKitTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName4 }
);
var TaskerToolkitLadingDetailsModel4 = import_mongo_connection260.mongoClientApp.model(
  "vn_taskerToolkitLadingDetails",
  TaskerToolkitLadingDetailsSchema4
);
var taskerToolkitLadingDetails4 = TaskerToolkitLadingDetailsModel4;

// schemas/isoCode/vn/taskerTrainingCourse.server.ts
var import_mongo_connection261 = require("mongo-connection");
var import_mongoose261 = require("mongoose");
var taskerTrainingCourseName4 = "vn_trainingTaskerCourse";
var CourseSchema4 = new import_mongoose261.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName4 }
);
var CourseModel4 = import_mongo_connection261.mongoClientApp.model(
  taskerTrainingCourseName4,
  CourseSchema4
);
var taskerTrainingCourse4 = CourseModel4;

// schemas/isoCode/vn/taskerTrainingCourseStartDate.ts
var import_mongo_connection262 = require("mongo-connection");
var import_mongoose262 = require("mongoose");
var taskerTrainingCourseStartDateName4 = "vn_trainingTaskerCourseStartDate";
var CourseStartDateSchema4 = new import_mongoose262.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName4 }
);
var CourseStartDateModel4 = import_mongo_connection262.mongoClientApp.model(
  taskerTrainingCourseStartDateName4,
  CourseStartDateSchema4
);
var taskerTrainingCourseStartDate4 = CourseStartDateModel4;

// schemas/isoCode/vn/taskerTrainingQuiz.server.ts
var import_mongoose263 = require("mongoose");
var import_mongo_connection263 = require("mongo-connection");
var taskerTrainingQuizName4 = "vn_trainingTaskerQuiz";
var QuizSchema4 = new import_mongoose263.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName4 }
);
var QuizModel4 = import_mongo_connection263.mongoClientApp.model(taskerTrainingQuizName4, QuizSchema4);
var taskerTrainingQuiz4 = QuizModel4;

// schemas/isoCode/vn/taskerTrainingQuizCollection.server.ts
var import_mongoose264 = require("mongoose");
var import_mongo_connection264 = require("mongo-connection");
var taskerTrainingQuizCollectionName4 = "vn_trainingTaskerQuizCollection";
var QuizCollectionSchema4 = new import_mongoose264.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName4 }
);
var QuizCollectionModel4 = import_mongo_connection264.mongoClientApp.model(
  taskerTrainingQuizCollectionName4,
  QuizCollectionSchema4
);
var taskerTrainingQuizCollection4 = QuizCollectionModel4;

// schemas/isoCode/vn/taskerTrainingSubmission.server.ts
var import_mongoose265 = require("mongoose");
var import_mongo_connection265 = require("mongo-connection");
var taskerTrainingSubmissionName4 = "vn_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema4 = new import_mongoose265.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName4 }
);
var TaskerTrainingSubmissionModel4 = import_mongo_connection265.mongoClientApp.model(
  taskerTrainingSubmissionName4,
  TaskerTrainingSubmissionSchema4
);
var taskerTrainingSubmission4 = TaskerTrainingSubmissionModel4;

// schemas/isoCode/vn/thingsToKnow.server.ts
var import_mongoose266 = require("mongoose");
var import_mongo_connection266 = require("mongo-connection");
var thingsToKnowName4 = "vn_thingsToKnow";
var ThingToKnowSchema4 = new import_mongoose266.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName4 }
);
var ThingsToKnowModel4 = import_mongo_connection266.mongoClientApp.model(
  "vn_thingsToKnow",
  ThingToKnowSchema4
);
var thingsToKnow4 = ThingsToKnowModel4;

// schemas/isoCode/vn/toolKitItems.server.ts
var import_mongoose267 = require("mongoose");
var import_mongo_connection267 = require("mongo-connection");
var toolKitItemsName4 = "vn_toolKitItems";
var ToolKitItemsSchema4 = new import_mongoose267.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName4 }
);
var ToolKitItemsModel4 = import_mongo_connection267.mongoClientApp.model(
  "vn_toolKitItems",
  ToolKitItemsSchema4
);
var toolKitItems4 = ToolKitItemsModel4;

// schemas/isoCode/vn/toolKitSetting.server.ts
var import_mongoose268 = require("mongoose");
var import_mongo_connection268 = require("mongo-connection");
var toolKitSettingName4 = "vn_toolKitSetting";
var ToolKitSettingSchema4 = new import_mongoose268.Schema(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName4 }
);
var ToolKitSettingModel4 = import_mongo_connection268.mongoClientApp.model(
  "vn_toolKitSetting",
  ToolKitSettingSchema4
);
var toolKitSetting4 = ToolKitSettingModel4;

// schemas/isoCode/vn/trainingJourney.server.ts
var import_mongoose269 = require("mongoose");
var import_mongo_connection269 = require("mongo-connection");
var trainingJourneyName4 = "vn_trainingJourney";
var TrainingJourneySchema4 = new import_mongoose269.Schema(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName4 }
);
var TrainingJourneyModel4 = import_mongo_connection269.mongoClientApp.model(
  "vn_trainingJourney",
  TrainingJourneySchema4
);
var trainingJourney4 = TrainingJourneyModel4;

// schemas/isoCode/vn/trainingTasker.server.ts
var import_mongoose270 = require("mongoose");
var import_mongo_connection270 = require("mongo-connection");
var trainingTaskerName4 = "trainingTasker";
var TrainingTaskerSchema4 = new import_mongoose270.Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName4 }
);
var TrainingTaskerModel4 = import_mongo_connection270.mongoClientApp.model(
  "vn_trainingTasker",
  TrainingTaskerSchema4
);
var trainingTasker4 = TrainingTaskerModel4;

// schemas/isoCode/vn/userActivation.server.ts
var import_mongo_connection271 = require("mongo-connection");
var import_mongoose271 = require("mongoose");
var userActivationName4 = "vn_userActivation";
var UserActivationSchema4 = new import_mongoose271.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName4 }
);
var UserActivationModel4 = import_mongo_connection271.mongoClientApp.model(
  userActivationName4,
  UserActivationSchema4
);
var userActivation4 = UserActivationModel4;

// schemas/isoCode/vn/userApp.server.ts
var import_mongo_connection272 = require("mongo-connection");
var import_mongoose272 = require("mongoose");
var usersName4 = "users";
var UsersAppSchema4 = new import_mongoose272.Schema(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName4 }
);
var UsersAppModel4 = import_mongo_connection272.mongoClientApp.model("vn_users", UsersAppSchema4);
var users4 = UsersAppModel4;

// schemas/isoCode/vn/userComboVoucher.server.ts
var import_mongo_connection273 = require("mongo-connection");
var import_mongoose273 = require("mongoose");
var userComboVoucherName4 = "vn_userComboVoucher";
var UserComboVoucherSchema4 = new import_mongoose273.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: import_mongoose273.Schema.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: import_mongoose273.Schema.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName4 }
);
var UserComboVoucherModel4 = import_mongo_connection273.mongoClientApp.model(
  userComboVoucherName4,
  UserComboVoucherSchema4
);
var userComboVoucher4 = UserComboVoucherModel4;

// schemas/isoCode/vn/userLocationHistory.server.ts
var import_mongoose274 = require("mongoose");
var import_mongo_connection274 = require("mongo-connection");
var userLocationHistoryName4 = "vn_userLocationHistory";
var UserLocationHistorySchema4 = new import_mongoose274.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName4 }
);
var UserLocationHistoryModel4 = import_mongo_connection274.mongoClientApp.model(
  userLocationHistoryName4,
  UserLocationHistorySchema4
);
var userLocationHistory4 = UserLocationHistoryModel4;

// schemas/isoCode/vn/userProfile.server.ts
var import_mongo_connection275 = require("mongo-connection");
var import_mongoose275 = require("mongoose");
var userProfileName4 = "vn_userProfile";
var UserProfileSchema4 = new import_mongoose275.Schema(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: import_mongoose275.Schema.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName4 }
);
var UserProfileModel4 = import_mongo_connection275.mongoClientApp.model(userProfileName4, UserProfileSchema4);
var userProfile4 = UserProfileModel4;

// schemas/isoCode/vn/workingPlaces.server.ts
var import_mongoose276 = require("mongoose");
var import_mongo_connection276 = require("mongo-connection");
var workingPlacesName4 = "vn_workingPlaces";
var WorkingPlacesSchema4 = new import_mongoose276.Schema(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName4 }
);
var WorkingPlacesModel4 = import_mongo_connection276.mongoClientApp.model(
  "vn_workingPlaces",
  WorkingPlacesSchema4
);
var workingPlaces4 = WorkingPlacesModel4;

// schemas/isoCode/vn/index.server.ts
var VN = {
  task: task4,
  historyTasks: historyTasks4,
  trainingTasker: trainingTasker4,
  userLocationHistory: userLocationHistory4,
  trainingJourney: trainingJourney4,
  toolKitSetting: toolKitSetting4,
  toolKitItems: toolKitItems4,
  thingsToKnow: thingsToKnow4,
  taskerTrainingSubmission: taskerTrainingSubmission4,
  taskerTrainingQuizCollection: taskerTrainingQuizCollection4,
  taskerTrainingQuiz: taskerTrainingQuiz4,
  taskerTrainingCourse: taskerTrainingCourse4,
  taskerToolkitLadingDetails: taskerToolkitLadingDetails4,
  financialAccount: financialAccount4,
  taskerIncentive: taskerIncentive4,
  taskerPointTransaction: taskerPointTransaction4,
  taskerGift: taskerGift4,
  notification: notification4,
  taskerProfile: taskerProfile4,
  employeeProfile: employeeProfile4,
  workingPlaces: workingPlaces4,
  flashSale: flashSale4,
  incentive: incentive4,
  promotionCode: promotionCode4,
  promotionSource: promotionSource4,
  service: service4,
  promotionHistory: promotionHistory4,
  settingCountry: settingCountry4,
  comboVoucher: comboVoucher4,
  referralCampaign: referralCampaign4,
  marketingCampaign: marketingCampaign4,
  settingSystem: settingSystem4,
  partnerDirectory: partnerDirectory4,
  partnerRequest: partnerRequest4,
  communityComment: communityComment4,
  communityMedal: communityMedal4,
  communityNotification: communityNotification4,
  communityPost: communityPost4,
  communitySetting: communitySetting4,
  communityTag: communityTag4,
  communityUser: communityUser4,
  communityUserReport: communityUserReport4,
  subscription: subscription4,
  taskerOnboardingSetting: taskerOnboardingSetting4,
  users: users4,
  serviceChannel: serviceChannel4,
  bEmployee: bEmployee4,
  bEmployeeSetting: bEmployeeSetting4,
  FATransaction: FATransaction4,
  taskerBNPLTransaction: taskerBNPLTransaction4,
  taskerBNPLProcess: taskerBNPLProcess4,
  paymentToolKitTransaction: paymentToolKitTransaction4,
  rating: rating4,
  userActivation: userActivation4,
  business: business4,
  businessLevel: businessLevel4,
  businessMember: businessMember4,
  businessMemberTransaction: businessMemberTransaction4,
  businessTransaction: businessTransaction4,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocation4,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDate4,
  taskerSpecialCampaign: taskerSpecialCampaign4,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransaction4,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethod4,
  userProfile: userProfile4,
  communityTagOrder: communityTagOrder4,
  journeySetting: journeySetting4,
  userComboVoucher: userComboVoucher4,
  bundleVoucher: bundleVoucher4
};
var VNName = {
  task: taskName4,
  historyTasks: historyTasksName4,
  userLocationHistory: userLocationHistoryName4,
  trainingTasker: trainingTaskerName4,
  trainingJourney: trainingJourneyName4,
  toolKitSetting: toolKitSettingName4,
  toolKitItems: toolKitItemsName4,
  thingsToKnow: thingsToKnowName4,
  taskerTrainingSubmission: taskerTrainingSubmissionName4,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName4,
  taskerTrainingQuiz: taskerTrainingQuizName4,
  taskerTrainingCourse: taskerTrainingCourseName4,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName4,
  taskerPointTransaction: taskerPointTransactionName4,
  taskerIncentive: taskerIncentiveName4,
  taskerGift: taskerGiftName4,
  notification: notificationName4,
  financialAccount: financialAccountName4,
  taskerProfile: taskerProfileName4,
  employeeProfile: employeeProfileName4,
  workingPlaces: workingPlacesName4,
  service: serviceName4,
  settingCountry: settingCountryName4,
  settingSystem: settingSystemName4,
  incentive: incentiveName4,
  communityComment: communityCommentName4,
  communityMedal: communityMedalName4,
  communityNotification: communityNotificationName4,
  communityPost: communityPostName4,
  communitySetting: communitySettingName4,
  communityTag: communityTagName4,
  communityUser: communityUserName4,
  communityUserReport: communityUserReportName4,
  subscription: subscriptionName4,
  taskerOnboardingSetting: taskerOnboardingSettingName4,
  users: usersName4,
  serviceChannel: serviceChannelName4,
  bEmployee: bEmployeeName4,
  bEmployeeSetting: bEmployeeSettingName4,
  FATransaction: FATransactionName4,
  taskerBNPLTransaction: taskerBNPLTransactionName4,
  taskerBNPLProcess: taskerBNPLProcessName4,
  paymentToolKitTransaction: paymentToolKitTransactionName4,
  promotionCode: promotionCodeName4,
  promotionHistory: promotionHistoryName4,
  marketingCampaign: marketingCampaignName4,
  rating: ratingName4,
  userActivation: userActivationName4,
  business: businessName4,
  businessLevel: businessLevelName4,
  businessMember: businessMemberName4,
  businessMemberTransaction: businessMemberTransactionName4,
  businessTransaction: businessTransactionName4,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName4,
  comboVoucher: comboVoucherName4,
  flashSale: flashSaleName4,
  partnerDirectory: partnerDirectoryName4,
  partnerRequest: partnerRequestName4,
  promotionSource: promotionSourceName4,
  referralCampaign: referralCampaignName4,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName4,
  taskerSpecialCampaign: taskerSpecialCampaignName4,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName4,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName4,
  userProfile: userProfileName4,
  communityTagOrder: communityTagOrderName4,
  journeySetting: journeySettingName4,
  userComboVoucher: userComboVoucherName4,
  bundleVoucher: bundleVoucherName4
};
var index_server_default4 = VN;

// schemas/isoCode/method.server.ts
var EnumIsoCode = /* @__PURE__ */ ((EnumIsoCode2) => {
  EnumIsoCode2["VN"] = "VN";
  EnumIsoCode2["TH"] = "TH";
  EnumIsoCode2["ID"] = "ID";
  EnumIsoCode2["MY"] = "MY";
  return EnumIsoCode2;
})(EnumIsoCode || {});
function getModels(isoCode) {
  if (isoCode === "VN") {
    return index_server_default4;
  }
  if (isoCode === "TH") {
    return index_server_default3;
  }
  if (isoCode === "ID") {
    return index_server_default2;
  }
  if (isoCode === "MY") {
    return index_server_default;
  }
  throw new Error("Iso code not is VN or TH or ID or MY");
}
function getFieldNameByIsoCode({
  isoCode,
  fieldName
}) {
  if (isoCode === "VN" /* VN */ || isoCode === "MY" /* MY */) return fieldName;
  return `${isoCode}_${fieldName}`;
}
function getCollectionNameByIsoCode(isoCode) {
  if (isoCode === "VN") {
    return VNName;
  }
  if (isoCode === "TH") {
    return THName;
  }
  if (isoCode === "ID") {
    return IDName;
  }
  if (isoCode === "MY") {
    return MYName;
  }
  throw new Error("Iso code not is VN or TH or ID or MY");
}
async function getFinancialAccountByIsoCode({
  fAccountId,
  isoCode
}) {
  if (!isoCode) {
    throw new Error("IsoCode is not valid");
  }
  const fAccount = await getModels(isoCode).financialAccount.findById(fAccountId).lean();
  if (!fAccount) {
    throw new Error("Financial account not found");
  }
  const fMainAccountFieldName = getFieldNameByIsoCode({
    isoCode,
    fieldName: "FMainAccount"
  });
  const promotionFieldName = getFieldNameByIsoCode({
    isoCode,
    fieldName: "Promotion"
  });
  return {
    [fMainAccountFieldName]: fAccount?.[fMainAccountFieldName] || 0,
    [promotionFieldName]: fAccount?.[promotionFieldName] || 0
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  EnumIsoCode,
  getCollectionNameByIsoCode,
  getFieldNameByIsoCode,
  getFinancialAccountByIsoCode,
  getModels
});
//# sourceMappingURL=index.js.map
