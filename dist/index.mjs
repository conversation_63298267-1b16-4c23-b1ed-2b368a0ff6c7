// schemas/isoCode/my/bEmployee.server.ts
import { mongoClientApp } from "mongo-connection";
import { Schema } from "mongoose";
var bEmployeeName = "my_bEmployee";
var BEmployeeSchema = new Schema(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName }
);
var BEmployeeModel = mongoClientApp.model(bEmployeeName, BEmployeeSchema);
var bEmployee = BEmployeeModel;

// schemas/isoCode/my/bEmployeeSetting.server.ts
import { mongoClientApp as mongoClientApp2 } from "mongo-connection";
import { Schema as Schema2 } from "mongoose";
var bEmployeeSettingName = "my_bEmployeeSetting";
var BEmployeeSettingSchema = new Schema2(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName }
);
var BEmployeeSettingModel = mongoClientApp2.model(
  bEmployeeSettingName,
  BEmployeeSettingSchema
);
var bEmployeeSetting = BEmployeeSettingModel;

// schemas/isoCode/my/bundleVoucher.ts
import { mongoClientApp as mongoClientApp3 } from "mongo-connection";
import { Schema as Schema3 } from "mongoose";
var bundleVoucherName = "my_bundleVoucher";
var BundleVoucherSchema = new Schema3(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema3.Types.Mixed },
            nextData: { $type: Schema3.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema3.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName }
);
var BundleVoucherModel = mongoClientApp3.model(
  bundleVoucherName,
  BundleVoucherSchema
);
var bundleVoucher = BundleVoucherModel;

// schemas/isoCode/my/campaign-payment.server.ts
import { mongoClientApp as mongoClientApp4 } from "mongo-connection";
import { Schema as Schema4 } from "mongoose";
var marketingCampaignPaymentMethodName = "my_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema = new Schema4(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: Schema4.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName }
);
var MarketingCampaignPaymentMethodModel = mongoClientApp4.model(
  marketingCampaignPaymentMethodName,
  MarketingCampaignPaymentMethodSchema
);
var marketingCampaignPaymentMethod = MarketingCampaignPaymentMethodModel;

// schemas/isoCode/my/campaign.server.ts
import { mongoClientApp as mongoClientApp5 } from "mongo-connection";
import { Schema as Schema5 } from "mongoose";
var marketingCampaignName = "my_marketingCampaign";
var MarketingCampaignSchema = new Schema5(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: Schema5.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: Schema5.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema5.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema5.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema5.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema5.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName }
);
var MarketingCampaignModel = mongoClientApp5.model(
  marketingCampaignName,
  MarketingCampaignSchema
);
var marketingCampaign = MarketingCampaignModel;

// schemas/isoCode/my/comboVoucher.server.ts
import { mongoClientApp as mongoClientApp6 } from "mongo-connection";
import { Schema as Schema6 } from "mongoose";
var comboVoucherName = "my_comboVoucher";
var ComboVoucherSchema = new Schema6(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName }
);
var ComboVoucherModel = mongoClientApp6.model(
  "my_comboVoucher",
  ComboVoucherSchema
);
var comboVoucher = ComboVoucherModel;

// schemas/isoCode/my/communityComment.server.ts
import { mongoClientApp as mongoClientApp7 } from "mongo-connection";
import { Schema as Schema7 } from "mongoose";
var communityCommentName = "my_communityComment";
var CommunityCommentSchema = new Schema7(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName }
);
var CommunityCommentModel = mongoClientApp7.model(
  communityCommentName,
  CommunityCommentSchema
);
var communityComment = CommunityCommentModel;

// schemas/isoCode/my/communityMedal.server.ts
import { mongoClientApp as mongoClientApp8 } from "mongo-connection";
import { Schema as Schema8 } from "mongoose";
var communityMedalName = "my_communityMedal";
var CommunityMedalSchema = new Schema8(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: Schema8.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: Schema8.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: Schema8.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName }
);
var CommunityMedalModel = mongoClientApp8.model(
  communityMedalName,
  CommunityMedalSchema
);
var communityMedal = CommunityMedalModel;

// schemas/isoCode/my/communityNotification.server.ts
import { mongoClientApp as mongoClientApp9 } from "mongo-connection";
import { Schema as Schema9 } from "mongoose";
var communityNotificationName = "my_communityNotification";
var CommunityNotificationSchema = new Schema9(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: Schema9.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName }
);
var CommunityNotificationModel = mongoClientApp9.model(
  communityNotificationName,
  CommunityNotificationSchema
);
var communityNotification = CommunityNotificationModel;

// schemas/isoCode/my/communityOrderTag.server.ts
import { mongoClientApp as mongoClientApp10 } from "mongo-connection";
import { Schema as Schema10 } from "mongoose";
var communityTagOrderName = "my_communityTagOrder";
var CommunityTagOrderSchema = new Schema10(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: Schema10.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: Schema10.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName }
);
var CommunityTagOrderModel = mongoClientApp10.model(
  communityTagOrderName,
  CommunityTagOrderSchema
);
var communityTagOrder = CommunityTagOrderModel;

// schemas/isoCode/my/communityPost.server.ts
import { mongoClientApp as mongoClientApp11 } from "mongo-connection";
import { Schema as Schema11 } from "mongoose";
var communityPostName = "my_communityPost";
var CommunityPostSchema = new Schema11(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: Schema11.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityPostName }
);
var CommunityPostModel = mongoClientApp11.model(
  communityPostName,
  CommunityPostSchema
);
var communityPost = CommunityPostModel;

// schemas/isoCode/my/communitySetting.server.ts
import { mongoClientApp as mongoClientApp12 } from "mongo-connection";
import { Schema as Schema12 } from "mongoose";
var communitySettingName = "my_communitySetting";
var CommunitySettingSchema = new Schema12(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: Schema12.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName
  }
);
var CommunitySettingModel = mongoClientApp12.model(
  communitySettingName,
  CommunitySettingSchema
);
var communitySetting = CommunitySettingModel;

// schemas/isoCode/my/communityTag.server.ts
import { mongoClientApp as mongoClientApp13 } from "mongo-connection";
import { Schema as Schema13 } from "mongoose";
var communityTagName = "my_communityTag";
var CommunityTagSchema = new Schema13(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: Schema13.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: Schema13.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName }
);
var CommunityTagModel = mongoClientApp13.model(
  communityTagName,
  CommunityTagSchema
);
var communityTag = CommunityTagModel;

// schemas/isoCode/my/communityUser.server.ts
import { mongoClientApp as mongoClientApp14 } from "mongo-connection";
import { Schema as Schema14 } from "mongoose";
var communityUserName = "my_communityUser";
var CommunityUserSchema = new Schema14(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName }
);
var CommunityUserModel = mongoClientApp14.model(
  communityUserName,
  CommunityUserSchema
);
var communityUser = CommunityUserModel;

// schemas/isoCode/my/communityUserReport.server.ts
import { mongoClientApp as mongoClientApp15 } from "mongo-connection";
import { Schema as Schema15 } from "mongoose";
var communityUserReportName = "my_communityUserReport";
var CommunityUserReportSchema = new Schema15(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName }
);
var CommunityUserReportModel = mongoClientApp15.model(
  communityUserReportName,
  CommunityUserReportSchema
);
var communityUserReport = CommunityUserReportModel;

// schemas/isoCode/my/employeeProfile.server.ts
import { mongoClientApp as mongoClientApp16 } from "mongo-connection";
import { Schema as Schema16 } from "mongoose";
var employeeProfileName = "my_employeeProfile";
var EmployeeProfileSchema = new Schema16(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName }
);
var EmployeeProfileModel = mongoClientApp16.model(
  employeeProfileName,
  EmployeeProfileSchema
);
var employeeProfile = EmployeeProfileModel;

// schemas/isoCode/my/FATransaction.server.ts
import { mongoClientApp as mongoClientApp17 } from "mongo-connection";
import { Schema as Schema17 } from "mongoose";
var FATransactionName = "my_FATransaction";
var FATransactionSchema = new Schema17(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName }
);
var FATransactionModel = mongoClientApp17.model(
  "my_FATransaction",
  FATransactionSchema
);
var FATransaction = FATransactionModel;

// schemas/isoCode/my/financialAccount.server.ts
import { Schema as Schema18 } from "mongoose";
import { mongoClientApp as mongoClientApp18 } from "mongo-connection";
var financialAccountName = "my_financialAccount";
var FinancialAccountSchema = new Schema18(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName }
);
var FinancialAccountModel = mongoClientApp18.model(
  "my_financialAccount",
  FinancialAccountSchema
);
var financialAccount = FinancialAccountModel;

// schemas/isoCode/my/flashSale.server.ts
import { mongoClientApp as mongoClientApp19 } from "mongo-connection";
import { Schema as Schema19 } from "mongoose";
var flashSaleName = "my_askerFlashSaleIncentive";
var FlashSaleSchema = new Schema19(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName }
);
var FlashSaleModel = mongoClientApp19.model(
  "my_askerFlashSaleIncentive",
  FlashSaleSchema
);
var flashSale = FlashSaleModel;

// schemas/isoCode/my/historyTasks.server.ts
import { mongoClientApp as mongoClientApp20 } from "mongo-connection";
import { Schema as Schema20 } from "mongoose";
var historyTasksName = "my_history_tasks";
var HistoryTasksSchema = new Schema20(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema20.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema20.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema20.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema20.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName }
);
var HistoryTasksModel = mongoClientApp20.model("my_history_tasks", HistoryTasksSchema);
var historyTasks = HistoryTasksModel;

// schemas/isoCode/my/incentive.server.ts
import { mongoClientApp as mongoClientApp21 } from "mongo-connection";
import { Schema as Schema21 } from "mongoose";
var incentiveName = "my_incentive";
var IncentiveSchema = new Schema21(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName }
);
var IncentiveModel = mongoClientApp21.model(incentiveName, IncentiveSchema);
var incentive = IncentiveModel;

// schemas/isoCode/my/journeySetting.server.ts
import { mongoClientApp as mongoClientApp22 } from "mongo-connection";
import { Schema as Schema22 } from "mongoose";
var journeySettingName = "my_journeySetting";
var JourneySettingSchema = new Schema22(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName }
);
var JourneySettingModel = mongoClientApp22.model(
  journeySettingName,
  JourneySettingSchema
);
var journeySetting = JourneySettingModel;

// schemas/isoCode/my/notification.server.ts
import { mongoClientApp as mongoClientApp23 } from "mongo-connection";
import { Schema as Schema23 } from "mongoose";
var notificationName = "my_notification";
var NotificationSchema = new Schema23(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName }
);
var NotificationModel = mongoClientApp23.model(
  notificationName,
  NotificationSchema
);
var notification = NotificationModel;

// schemas/isoCode/my/partnerBusiness.server.ts
import { mongoClientApp as mongoClientApp24 } from "mongo-connection";
import { Schema as Schema24 } from "mongoose";
var businessName = "my_business";
var BusinessSchema = new Schema24(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [{ name: { $type: String }, url: { $type: String } }],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema24.Types.Mixed },
            nextData: { $type: Schema24.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema24.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName }
);
var BusinessModel = mongoClientApp24.model(businessName, BusinessSchema);
var business = BusinessModel;

// schemas/isoCode/my/partnerBusinessLevel.server.ts
import { Schema as Schema25 } from "mongoose";
import { mongoClientApp as mongoClientApp25 } from "mongo-connection";
var businessLevelName = "my_businessLevel";
var BusinessLevelSchema = new Schema25(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName }
);
var BusinessLevelModel = mongoClientApp25.model(
  businessLevelName,
  BusinessLevelSchema
);
var businessLevel = BusinessLevelModel;

// schemas/isoCode/my/partnerBusinessMember.server.ts
import { Schema as Schema26 } from "mongoose";
import { mongoClientApp as mongoClientApp26 } from "mongo-connection";
var businessMemberName = "my_businessMember";
var BusinessMemberSchema = new Schema26(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName }
);
var BusinessMemberModel = mongoClientApp26.model(businessMemberName, BusinessMemberSchema);
var businessMember = BusinessMemberModel;

// schemas/isoCode/my/partnerBusinessMemberTransaction.server.ts
import { mongoClientApp as mongoClientApp27 } from "mongo-connection";
import { Schema as Schema27 } from "mongoose";
var businessMemberTransactionName = "my_businessMemberTransaction";
var BusinessMemberTransactionSchema = new Schema27(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    taskId: { $type: String, required: true },
    userId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName }
);
var BusinessMemberTransactionModel = mongoClientApp27.model(businessMemberTransactionName, BusinessMemberTransactionSchema);
var businessMemberTransaction = BusinessMemberTransactionModel;

// schemas/isoCode/my/partnerBusinessSetupAllocateAndReallocate.server.ts
import { mongoClientApp as mongoClientApp28 } from "mongo-connection";
import { Schema as Schema28 } from "mongoose";
var businessSetupAllocationAndReallocationName = "my_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema = new Schema28(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName }
);
var BusinessSetupAllocationAndReallocationModel = mongoClientApp28.model(businessSetupAllocationAndReallocationName, BusinessSetupAllocationAndReallocationSchema);
var businessSetupAllocationAndReallocation = BusinessSetupAllocationAndReallocationModel;

// schemas/isoCode/my/partnerBusinessTransaction.server.ts
import { mongoClientApp as mongoClientApp29 } from "mongo-connection";
import { Schema as Schema29 } from "mongoose";
var businessTransactionName = "my_businessTransaction";
var BusinessTransactionSchema = new Schema29(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName }
);
var BusinessTransactionModel = mongoClientApp29.model(
  businessTransactionName,
  BusinessTransactionSchema
);
var businessTransaction = BusinessTransactionModel;

// schemas/isoCode/my/partnerDirectory.server.ts
import { Schema as Schema30 } from "mongoose";
import { mongoClientApp as mongoClientApp30 } from "mongo-connection";
var partnerDirectoryName = "my_partnerDirectory";
var PartnerDirectorySchema = new Schema30(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName }
);
var PartnerDirectoryModel = mongoClientApp30.model(
  partnerDirectoryName,
  PartnerDirectorySchema
);
var partnerDirectory = PartnerDirectoryModel;

// schemas/isoCode/my/partnerRequest.server.ts
import { Schema as Schema31 } from "mongoose";
import { mongoClientApp as mongoClientApp31 } from "mongo-connection";
var partnerRequestName = "my_partnerRequest";
var PartnerRequestSchema = new Schema31(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName }
);
var PartnerRequestModel = mongoClientApp31.model(
  partnerRequestName,
  PartnerRequestSchema
);
var partnerRequest = PartnerRequestModel;

// schemas/isoCode/my/paymentToolKitTransaction.server.ts
import { mongoClientApp as mongoClientApp32 } from "mongo-connection";
import { Schema as Schema32 } from "mongoose";
var paymentToolKitTransactionName = "my_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema = new Schema32(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName }
);
var PaymentToolKitTransactionModel = mongoClientApp32.model(
  paymentToolKitTransactionName,
  PaymentToolKitTransactionSchema
);
var paymentToolKitTransaction = PaymentToolKitTransactionModel;

// schemas/isoCode/my/promotionCode.server.ts
import { mongoClientApp as mongoClientApp33 } from "mongo-connection";
import { Schema as Schema33 } from "mongoose";
var promotionCodeName = "my_promotionCode";
var PromotionCodeSchema = new Schema33(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: Schema33.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName }
);
var PromotionCodeModel = mongoClientApp33.model(
  promotionCodeName,
  PromotionCodeSchema
);
var promotionCode = PromotionCodeModel;

// schemas/isoCode/my/promotionHistory.server.ts
import { mongoClientApp as mongoClientApp34 } from "mongo-connection";
import { Schema as Schema34 } from "mongoose";
var promotionHistoryName = "my_promotionHistory";
var PromotionHistorySchema = new Schema34(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName }
);
var PromotionHistoryModel = mongoClientApp34.model(
  promotionHistoryName,
  PromotionHistorySchema
);
var promotionHistory = PromotionHistoryModel;

// schemas/isoCode/my/promotionSource.server.ts
import { Schema as Schema35 } from "mongoose";
import { mongoClientApp as mongoClientApp35 } from "mongo-connection";
var promotionSourceName = "my_promotionSource";
var PromotionSourceSchema = new Schema35(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName }
);
var PromotionSourceModel = mongoClientApp35.model(
  promotionSourceName,
  PromotionSourceSchema
);
var promotionSource = PromotionSourceModel;

// schemas/isoCode/my/rating.server.ts
import { mongoClientApp as mongoClientApp36 } from "mongo-connection";
import { Schema as Schema36 } from "mongoose";
var ratingName = "my_rating";
var RatingSchema = new Schema36(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName }
);
var RatingModel = mongoClientApp36.model(ratingName, RatingSchema);
var rating = RatingModel;

// schemas/isoCode/my/referralCampaign.server.ts
import { mongoClientApp as mongoClientApp37 } from "mongo-connection";
import { Schema as Schema37 } from "mongoose";
var referralCampaignName = "my_askerReferralCampaign";
var ReferralCampaignSchema = new Schema37(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: Schema37.Types.Mixed, required: true },
    inviter: { $type: Schema37.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName }
);
var ReferralCampaignModel = mongoClientApp37.model(
  referralCampaignName,
  ReferralCampaignSchema
);
var referralCampaign = ReferralCampaignModel;

// schemas/isoCode/my/service.server.ts
import { mongoClientApp as mongoClientApp38 } from "mongo-connection";
import { Schema as Schema38 } from "mongoose";
var serviceName = "my_service";
var ServiceSchema = new Schema38(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: Schema38.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: Schema38.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: Schema38.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName }
);
var ServiceModel = mongoClientApp38.model(serviceName, ServiceSchema);
var service = ServiceModel;

// schemas/isoCode/my/serviceChannel.server.ts
import { mongoClientApp as mongoClientApp39 } from "mongo-connection";
import { Schema as Schema39 } from "mongoose";
var serviceChannelName = "my_serviceChannel";
var ServiceChannelSchema = new Schema39(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName }
);
var ServiceChannelModel = mongoClientApp39.model(
  serviceChannelName,
  ServiceChannelSchema
);
var serviceChannel = ServiceChannelModel;

// schemas/isoCode/my/settingCountry.server.ts
import { mongoClientApp as mongoClientApp40 } from "mongo-connection";
import { Schema as Schema40 } from "mongoose";
var settingCountryName = "my_settingCountry";
var SettingCountrySchema = new Schema40(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName }
);
var SettingCountryModel = mongoClientApp40.model(
  settingCountryName,
  SettingCountrySchema
);
var settingCountry = SettingCountryModel;

// schemas/isoCode/my/settingSystem.server.ts
import { mongoClientApp as mongoClientApp41 } from "mongo-connection";
import { Schema as Schema41 } from "mongoose";
var settingSystemName = "my_settingSystem";
var SettingSystemSchema = new Schema41(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: Schema41.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName }
);
var SettingSystemModel = mongoClientApp41.model(
  "my_settingSystem",
  SettingSystemSchema
);
var settingSystem = SettingSystemModel;

// schemas/isoCode/my/subscription.server.ts
import { mongoClientApp as mongoClientApp42 } from "mongo-connection";
import { Schema as Schema42 } from "mongoose";
var subscriptionName = "my_subscription";
var SubscriptionSchema = new Schema42(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: Schema42.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: Schema42.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName }
);
var SubscriptionModel = mongoClientApp42.model(subscriptionName, SubscriptionSchema);
var subscription = SubscriptionModel;

// schemas/isoCode/my/task.server.ts
import { mongoClientApp as mongoClientApp43 } from "mongo-connection";
import { Schema as Schema43 } from "mongoose";
var taskName = "my_task";
var TasksSchema = new Schema43(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema43.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema43.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema43.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema43.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: taskName }
);
var TasksModel = mongoClientApp43.model("my_tasks", TasksSchema);
var task = TasksModel;

// schemas/isoCode/my/taskerBNPLProcess.server.ts
import { mongoClientApp as mongoClientApp44 } from "mongo-connection";
import { Schema as Schema44 } from "mongoose";
var taskerBNPLProcessName = "my_taskerBNPLProcess";
var TaskerBNPLProcess = new Schema44(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName }
);
var TaskerBNPLProcessModel = mongoClientApp44.model(
  taskerBNPLProcessName,
  TaskerBNPLProcess
);
var taskerBNPLProcess = TaskerBNPLProcessModel;

// schemas/isoCode/my/taskerBNPLTransaction.server.ts
import { mongoClientApp as mongoClientApp45 } from "mongo-connection";
import { Schema as Schema45 } from "mongoose";
var taskerBNPLTransactionName = "my_taskerBNPLTransaction";
var TaskerBNPLTransaction = new Schema45(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName }
);
var TaskerBNPLTransactionModel = mongoClientApp45.model(
  taskerBNPLTransactionName,
  TaskerBNPLTransaction
);
var taskerBNPLTransaction = TaskerBNPLTransactionModel;

// schemas/isoCode/my/taskerGift.server.ts
import { Schema as Schema46 } from "mongoose";
import { mongoClientApp as mongoClientApp46 } from "mongo-connection";
var taskerGiftName = "my_taskerGift";
var TaskerGiftSchema = new Schema46(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName }
);
var TaskerGiftModel = mongoClientApp46.model(taskerGiftName, TaskerGiftSchema);
var taskerGift = TaskerGiftModel;

// schemas/isoCode/my/taskerIncentive.server.ts
import { Schema as Schema47 } from "mongoose";
import { mongoClientApp as mongoClientApp47 } from "mongo-connection";
var taskerIncentiveName = "my_taskerIncentive";
var TaskerIncentiveSchema = new Schema47(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: Schema47.Types.Mixed
    },
    status: {
      $type: String
    },
    codeFromPartner: {
      $type: Number
    },
    giftInfo: Schema47.Types.Mixed,
    social: Schema47.Types.Mixed,
    codeList: Schema47.Types.Mixed,
    office: Schema47.Types.Mixed,
    applyFor: Schema47.Types.Mixed,
    brandInfo: Schema47.Types.Mixed,
    redeemLink: Schema47.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName }
);
var TaskerIncentiveModel = mongoClientApp47.model(
  taskerIncentiveName,
  TaskerIncentiveSchema
);
var taskerIncentive = TaskerIncentiveModel;

// schemas/isoCode/my/taskerOnboardingSetting.server.ts
import { mongoClientApp as mongoClientApp48 } from "mongo-connection";
import { Schema as Schema48 } from "mongoose";
var taskerOnboardingSettingName = "my_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema = new Schema48(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName }
);
var TaskerOnboardingSettingModel = mongoClientApp48.model(
  taskerOnboardingSettingName,
  TaskerOnboardingSettingSchema
);
var taskerOnboardingSetting = TaskerOnboardingSettingModel;

// schemas/isoCode/my/taskerPointTransaction.server.ts
import { Schema as Schema49 } from "mongoose";
import { mongoClientApp as mongoClientApp49 } from "mongo-connection";
var taskerPointTransactionName = "my_taskerPointTransaction";
var TaskerPointTransactionSchema = new Schema49(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName }
);
var TaskerPointTransactionModel = mongoClientApp49.model(
  taskerPointTransactionName,
  TaskerPointTransactionSchema
);
var taskerPointTransaction = TaskerPointTransactionModel;

// schemas/isoCode/my/taskerProfile.server.ts
import { Schema as Schema50 } from "mongoose";
import { mongoClientApp as mongoClientApp50 } from "mongo-connection";
var taskerProfileName = "my_taskerProfile";
var TaskerProfileSchema = new Schema50(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName
  }
);
var TaskerProfileModel = mongoClientApp50.model(
  taskerProfileName,
  TaskerProfileSchema
);
var taskerProfile = TaskerProfileModel;

// schemas/isoCode/my/taskerSpecialCampaign.server.ts
import { mongoClientApp as mongoClientApp51 } from "mongo-connection";
import { Schema as Schema51 } from "mongoose";
var taskerSpecialCampaignName = "my_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema = new Schema51(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: Schema51.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName, _id: false }
);
var TaskerSpecialCampaignModel = mongoClientApp51.model(
  taskerSpecialCampaignName,
  TaskerSpecialCampaignSchema
);
var taskerSpecialCampaign = TaskerSpecialCampaignModel;

// schemas/isoCode/my/taskerSpecialCampaignTransaction.server.ts
import { mongoClientApp as mongoClientApp52 } from "mongo-connection";
import { Schema as Schema52 } from "mongoose";
var taskerSpecialCampaignTransactionName = "my_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema = new Schema52(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName }
);
var TaskerSpecialCampaignTransactionModel = mongoClientApp52.model(
  taskerSpecialCampaignTransactionName,
  TaskerSpecialCampaignTransactionSchema
);
var taskerSpecialCampaignTransaction = TaskerSpecialCampaignTransactionModel;

// schemas/isoCode/my/taskerToolkitLadingDetails.server.ts
import { Schema as Schema53 } from "mongoose";
import { mongoClientApp as mongoClientApp53 } from "mongo-connection";
var taskerToolkitLadingDetailsName = "my_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema = new Schema53(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName }
);
var TaskerToolkitLadingDetailsModel = mongoClientApp53.model(
  taskerToolkitLadingDetailsName,
  TaskerToolkitLadingDetailsSchema
);
var taskerToolkitLadingDetails = TaskerToolkitLadingDetailsModel;

// schemas/isoCode/my/taskerTrainingCourse.server.ts
import { mongoClientApp as mongoClientApp54 } from "mongo-connection";
import { Schema as Schema54 } from "mongoose";
var taskerTrainingCourseName = "my_trainingTaskerCourse";
var CourseSchema = new Schema54(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName }
);
var CourseModel = mongoClientApp54.model(
  taskerTrainingCourseName,
  CourseSchema
);
var taskerTrainingCourse = CourseModel;

// schemas/isoCode/my/taskerTrainingCourseStartDate.ts
import { mongoClientApp as mongoClientApp55 } from "mongo-connection";
import { Schema as Schema55 } from "mongoose";
var taskerTrainingCourseStartDateName = "my_trainingTaskerCourseStartDate";
var CourseStartDateSchema = new Schema55(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName }
);
var CourseStartDateModel = mongoClientApp55.model(
  taskerTrainingCourseStartDateName,
  CourseStartDateSchema
);
var taskerTrainingCourseStartDate = CourseStartDateModel;

// schemas/isoCode/my/taskerTrainingQuiz.server.ts
import { Schema as Schema56 } from "mongoose";
import { mongoClientApp as mongoClientApp56 } from "mongo-connection";
var taskerTrainingQuizName = "my_trainingTaskerQuiz";
var QuizSchema = new Schema56(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName }
);
var QuizModel = mongoClientApp56.model(taskerTrainingQuizName, QuizSchema);
var taskerTrainingQuiz = QuizModel;

// schemas/isoCode/my/taskerTrainingQuizCollection.server.ts
import { Schema as Schema57 } from "mongoose";
import { mongoClientApp as mongoClientApp57 } from "mongo-connection";
var taskerTrainingQuizCollectionName = "my_trainingTaskerQuizCollection";
var QuizCollectionSchema = new Schema57(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName }
);
var QuizCollectionModel = mongoClientApp57.model(
  taskerTrainingQuizCollectionName,
  QuizCollectionSchema
);
var taskerTrainingQuizCollection = QuizCollectionModel;

// schemas/isoCode/my/taskerTrainingSubmission.server.ts
import { Schema as Schema58 } from "mongoose";
import { mongoClientApp as mongoClientApp58 } from "mongo-connection";
var taskerTrainingSubmissionName = "my_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema = new Schema58(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName }
);
var TaskerTrainingSubmissionModel = mongoClientApp58.model(
  taskerTrainingSubmissionName,
  TaskerTrainingSubmissionSchema
);
var taskerTrainingSubmission = TaskerTrainingSubmissionModel;

// schemas/isoCode/my/thingsToKnow.server.ts
import { Schema as Schema59 } from "mongoose";
import { mongoClientApp as mongoClientApp59 } from "mongo-connection";
var thingsToKnowName = "my_thingsToKnow";
var ThingToKnowSchema = new Schema59(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName }
);
var ThingsToKnowModel = mongoClientApp59.model(
  thingsToKnowName,
  ThingToKnowSchema
);
var thingsToKnow = ThingsToKnowModel;

// schemas/isoCode/my/toolKitItems.server.ts
import { Schema as Schema60 } from "mongoose";
import { mongoClientApp as mongoClientApp60 } from "mongo-connection";
var toolKitItemsName = "my_toolKitItems";
var ToolKitItemsSchema = new Schema60(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName }
);
var ToolKitItemsModel = mongoClientApp60.model(
  toolKitItemsName,
  ToolKitItemsSchema
);
var toolKitItems = ToolKitItemsModel;

// schemas/isoCode/my/toolKitSetting.server.ts
import { Schema as Schema61 } from "mongoose";
import { mongoClientApp as mongoClientApp61 } from "mongo-connection";
var toolKitSettingName = "my_toolKitSetting";
var ToolKitSettingSchema = new Schema61(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName }
);
var ToolKitSettingModel = mongoClientApp61.model(
  toolKitSettingName,
  ToolKitSettingSchema
);
var toolKitSetting = ToolKitSettingModel;

// schemas/isoCode/my/trainingJourney.server.ts
import { Schema as Schema62 } from "mongoose";
import { mongoClientApp as mongoClientApp62 } from "mongo-connection";
var trainingJourneyName = "my_trainingJourney";
var TrainingJourneySchema = new Schema62(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName }
);
var TrainingJourneyModel = mongoClientApp62.model(
  trainingJourneyName,
  TrainingJourneySchema
);
var trainingJourney = TrainingJourneyModel;

// schemas/isoCode/my/trainingTasker.server.ts
import { Schema as Schema63 } from "mongoose";
import { mongoClientApp as mongoClientApp63 } from "mongo-connection";
var trainingTaskerName = "my_trainingTasker";
var TrainingTaskerSchema = new Schema63(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName }
);
var TrainingTaskerModel = mongoClientApp63.model(
  trainingTaskerName,
  TrainingTaskerSchema
);
var trainingTasker = TrainingTaskerModel;

// schemas/isoCode/my/userActivation.server.ts
import { mongoClientApp as mongoClientApp64 } from "mongo-connection";
import { Schema as Schema64 } from "mongoose";
var userActivationName = "my_userActivation";
var UserActivationSchema = new Schema64(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName }
);
var UserActivationModel = mongoClientApp64.model(
  userActivationName,
  UserActivationSchema
);
var userActivation = UserActivationModel;

// schemas/isoCode/my/userApp.server.ts
import { mongoClientApp as mongoClientApp65 } from "mongo-connection";
import { Schema as Schema65 } from "mongoose";
var usersName = "users";
var UsersAppSchema = new Schema65(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName }
);
var UsersAppModel = mongoClientApp65.model(usersName, UsersAppSchema);
var users = UsersAppModel;

// schemas/isoCode/my/userComboVoucher.server.ts
import { mongoClientApp as mongoClientApp66 } from "mongo-connection";
import { Schema as Schema66 } from "mongoose";
var userComboVoucherName = "my_userComboVoucher";
var UserComboVoucherSchema = new Schema66(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: Schema66.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: Schema66.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName }
);
var UserComboVoucherModel = mongoClientApp66.model(
  userComboVoucherName,
  UserComboVoucherSchema
);
var userComboVoucher = UserComboVoucherModel;

// schemas/isoCode/my/userLocationHistory.server.ts
import { Schema as Schema67 } from "mongoose";
import { mongoClientApp as mongoClientApp67 } from "mongo-connection";
var userLocationHistoryName = "my_userLocationHistory";
var UserLocationHistorySchema = new Schema67(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName }
);
var UserLocationHistoryModel = mongoClientApp67.model(
  userLocationHistoryName,
  UserLocationHistorySchema
);
var userLocationHistory = UserLocationHistoryModel;

// schemas/isoCode/my/userProfile.server.ts
import { mongoClientApp as mongoClientApp68 } from "mongo-connection";
import { Schema as Schema68 } from "mongoose";
var userProfileName = "my_userProfile";
var UserProfileSchema = new Schema68(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Schema68.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName }
);
var UserProfileModel = mongoClientApp68.model(userProfileName, UserProfileSchema);
var userProfile = UserProfileModel;

// schemas/isoCode/my/workingPlaces.server.ts
import { Schema as Schema69 } from "mongoose";
import { mongoClientApp as mongoClientApp69 } from "mongo-connection";
var workingPlacesName = "my_workingPlaces";
var WorkingPlacesSchema = new Schema69(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName }
);
var WorkingPlacesModel = mongoClientApp69.model(
  workingPlacesName,
  WorkingPlacesSchema
);
var workingPlaces = WorkingPlacesModel;

// schemas/isoCode/my/index.server.ts
var MY = {
  task,
  historyTasks,
  userLocationHistory,
  trainingTasker,
  trainingJourney,
  toolKitSetting,
  toolKitItems,
  thingsToKnow,
  taskerTrainingSubmission,
  taskerTrainingQuizCollection,
  taskerTrainingQuiz,
  taskerTrainingCourse,
  taskerToolkitLadingDetails,
  financialAccount,
  taskerIncentive,
  taskerPointTransaction,
  taskerGift,
  notification,
  employeeProfile,
  workingPlaces,
  taskerProfile,
  flashSale,
  incentive,
  promotionCode,
  promotionSource,
  service,
  promotionHistory,
  settingCountry,
  comboVoucher,
  referralCampaign,
  marketingCampaign,
  settingSystem,
  partnerRequest,
  partnerDirectory,
  communityComment,
  communityMedal,
  communityNotification,
  communityPost,
  communitySetting,
  communityTag,
  communityUser,
  communityUserReport,
  subscription,
  taskerOnboardingSetting,
  users,
  serviceChannel,
  bEmployee,
  bEmployeeSetting,
  FATransaction,
  taskerBNPLTransaction,
  taskerBNPLProcess,
  paymentToolKitTransaction,
  rating,
  userActivation,
  taskerTrainingCourseStartDate,
  business,
  businessLevel,
  businessMember,
  businessMemberTransaction,
  businessTransaction,
  businessSetupAllocationAndReallocation,
  taskerSpecialCampaign,
  taskerSpecialCampaignTransaction,
  marketingCampaignPaymentMethod,
  userProfile,
  communityTagOrder,
  journeySetting,
  userComboVoucher,
  bundleVoucher
};
var MYName = {
  trainingTasker: trainingTaskerName,
  historyTasks: historyTasksName,
  userLocationHistory: userLocationHistoryName,
  trainingJourney: trainingJourneyName,
  toolKitSetting: toolKitSettingName,
  toolKitItems: toolKitItemsName,
  thingsToKnow: thingsToKnowName,
  taskerTrainingSubmission: taskerTrainingSubmissionName,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName,
  taskerTrainingQuiz: taskerTrainingQuizName,
  taskerTrainingCourse: taskerTrainingCourseName,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName,
  taskerPointTransaction: taskerPointTransactionName,
  taskerIncentive: taskerIncentiveName,
  taskerGift: taskerGiftName,
  notification: notificationName,
  financialAccount: financialAccountName,
  taskerProfile: taskerProfileName,
  employeeProfile: employeeProfileName,
  task: taskName,
  workingPlaces: workingPlacesName,
  service: serviceName,
  settingCountry: settingCountryName,
  settingSystem: settingSystemName,
  incentive: incentiveName,
  communityComment: communityCommentName,
  communityMedal: communityMedalName,
  communityNotification: communityNotificationName,
  communityPost: communityPostName,
  communitySetting: communitySettingName,
  communityTag: communityTagName,
  communityUser: communityUserName,
  communityUserReport: communityUserReportName,
  subscription: subscriptionName,
  taskerOnboardingSetting: taskerOnboardingSettingName,
  users: usersName,
  serviceChannel: serviceChannelName,
  bEmployee: bEmployeeName,
  bEmployeeSetting: bEmployeeSettingName,
  FATransaction: FATransactionName,
  taskerBNPLTransaction: taskerBNPLTransactionName,
  taskerBNPLProcess: taskerBNPLProcessName,
  paymentToolKitTransaction: paymentToolKitTransactionName,
  promotionCode: promotionCodeName,
  promotionHistory: promotionHistoryName,
  marketingCampaign: marketingCampaignName,
  rating: ratingName,
  userActivation: userActivationName,
  comboVoucher: comboVoucherName,
  flashSale: flashSaleName,
  partnerDirectory: partnerDirectoryName,
  partnerRequest: partnerRequestName,
  promotionSource: promotionSourceName,
  referralCampaign: referralCampaignName,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName,
  business: businessName,
  businessLevel: businessLevelName,
  businessMember: businessMemberName,
  businessMemberTransaction: businessMemberTransactionName,
  businessTransaction: businessTransactionName,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName,
  taskerSpecialCampaign: taskerSpecialCampaignName,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName,
  userProfile: userProfileName,
  communityTagOrder: communityTagOrderName,
  journeySetting: journeySettingName,
  userComboVoucher: userComboVoucherName,
  bundleVoucher: bundleVoucherName
};
var index_server_default = MY;

// schemas/isoCode/id/bEmployee.server.ts
import { mongoClientApp as mongoClientApp70 } from "mongo-connection";
import { Schema as Schema70 } from "mongoose";
var bEmployeeName2 = "id_bEmployee";
var BEmployeeSchema2 = new Schema70(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName2 }
);
var BEmployeeModel2 = mongoClientApp70.model(bEmployeeName2, BEmployeeSchema2);
var bEmployee2 = BEmployeeModel2;

// schemas/isoCode/id/bEmployeeSetting.server.ts
import { mongoClientApp as mongoClientApp71 } from "mongo-connection";
import { Schema as Schema71 } from "mongoose";
var bEmployeeSettingName2 = "id_bEmployeeSetting";
var BEmployeeSettingSchema2 = new Schema71(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName2 }
);
var BEmployeeSettingModel2 = mongoClientApp71.model(
  bEmployeeSettingName2,
  BEmployeeSettingSchema2
);
var bEmployeeSetting2 = BEmployeeSettingModel2;

// schemas/isoCode/id/bundleVoucher.ts
import { mongoClientApp as mongoClientApp72 } from "mongo-connection";
import { Schema as Schema72 } from "mongoose";
var bundleVoucherName2 = "id_bundleVoucher";
var BundleVoucherSchema2 = new Schema72(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema72.Types.Mixed },
            nextData: { $type: Schema72.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema72.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName2 }
);
var BundleVoucherModel2 = mongoClientApp72.model(
  bundleVoucherName2,
  BundleVoucherSchema2
);
var bundleVoucher2 = BundleVoucherModel2;

// schemas/isoCode/id/campaign-payment.server.ts
import { mongoClientApp as mongoClientApp73 } from "mongo-connection";
import { Schema as Schema73 } from "mongoose";
var marketingCampaignPaymentMethodName2 = "id_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema2 = new Schema73(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: Schema73.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName2 }
);
var MarketingCampaignPaymentMethodModel2 = mongoClientApp73.model(
  marketingCampaignPaymentMethodName2,
  MarketingCampaignPaymentMethodSchema2
);
var marketingCampaignPaymentMethod2 = MarketingCampaignPaymentMethodModel2;

// schemas/isoCode/id/campaign.server.ts
import { mongoClientApp as mongoClientApp74 } from "mongo-connection";
import { Schema as Schema74 } from "mongoose";
var marketingCampaignName2 = "id_marketingCampaign";
var MarketingCampaignSchema2 = new Schema74(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: Schema74.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: Schema74.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema74.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema74.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema74.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema74.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName2 }
);
var MarketingCampaignModel2 = mongoClientApp74.model(
  marketingCampaignName2,
  MarketingCampaignSchema2
);
var marketingCampaign2 = MarketingCampaignModel2;

// schemas/isoCode/id/comboVoucher.server.ts
import { mongoClientApp as mongoClientApp75 } from "mongo-connection";
import { Schema as Schema75 } from "mongoose";
var comboVoucherName2 = "id_comboVoucher";
var ComboVoucherSchema2 = new Schema75(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName2 }
);
var ComboVoucherModel2 = mongoClientApp75.model(
  "id_comboVoucher",
  ComboVoucherSchema2
);
var comboVoucher2 = ComboVoucherModel2;

// schemas/isoCode/id/communityComment.server.ts
import { mongoClientApp as mongoClientApp76 } from "mongo-connection";
import { Schema as Schema76 } from "mongoose";
var communityCommentName2 = "id_communityComment";
var CommunityCommentSchema2 = new Schema76(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName2 }
);
var CommunityCommentModel2 = mongoClientApp76.model(
  communityCommentName2,
  CommunityCommentSchema2
);
var communityComment2 = CommunityCommentModel2;

// schemas/isoCode/id/communityMedal.server.ts
import { mongoClientApp as mongoClientApp77 } from "mongo-connection";
import { Schema as Schema77 } from "mongoose";
var communityMedalName2 = "id_communityMedal";
var CommunityMedalSchema2 = new Schema77(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: Schema77.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: Schema77.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: Schema77.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName2 }
);
var CommunityMedalModel2 = mongoClientApp77.model(
  communityMedalName2,
  CommunityMedalSchema2
);
var communityMedal2 = CommunityMedalModel2;

// schemas/isoCode/id/communityNotification.server.ts
import { mongoClientApp as mongoClientApp78 } from "mongo-connection";
import { Schema as Schema78 } from "mongoose";
var communityNotificationName2 = "id_communityNotification";
var CommunityNotificationSchema2 = new Schema78(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: Schema78.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName2 }
);
var CommunityNotificationModel2 = mongoClientApp78.model(
  communityNotificationName2,
  CommunityNotificationSchema2
);
var communityNotification2 = CommunityNotificationModel2;

// schemas/isoCode/id/communityOrderTag.server.ts
import { mongoClientApp as mongoClientApp79 } from "mongo-connection";
import { Schema as Schema79 } from "mongoose";
var communityTagOrderName2 = "id_communityTagOrder";
var CommunityTagOrderSchema2 = new Schema79(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: Schema79.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: Schema79.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName2 }
);
var CommunityTagOrderModel2 = mongoClientApp79.model(
  communityTagOrderName2,
  CommunityTagOrderSchema2
);
var communityTagOrder2 = CommunityTagOrderModel2;

// schemas/isoCode/id/communityPost.server.ts
import { mongoClientApp as mongoClientApp80 } from "mongo-connection";
import { Schema as Schema80 } from "mongoose";
var communityPostName2 = "id_communityPost";
var CommunityPostSchema2 = new Schema80(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: Schema80.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityPostName2 }
);
var CommunityPostModel2 = mongoClientApp80.model(
  communityPostName2,
  CommunityPostSchema2
);
var communityPost2 = CommunityPostModel2;

// schemas/isoCode/id/communitySetting.server.ts
import { mongoClientApp as mongoClientApp81 } from "mongo-connection";
import { Schema as Schema81 } from "mongoose";
var communitySettingName2 = "id_communitySetting";
var CommunitySettingSchema2 = new Schema81(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: Schema81.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName2
  }
);
var CommunitySettingModel2 = mongoClientApp81.model(
  communitySettingName2,
  CommunitySettingSchema2
);
var communitySetting2 = CommunitySettingModel2;

// schemas/isoCode/id/communityTag.server.ts
import { mongoClientApp as mongoClientApp82 } from "mongo-connection";
import { Schema as Schema82 } from "mongoose";
var communityTagName2 = "id_communityTag";
var CommunityTagSchema2 = new Schema82(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: Schema82.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: Schema82.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName2 }
);
var CommunityTagModel2 = mongoClientApp82.model(
  communityTagName2,
  CommunityTagSchema2
);
var communityTag2 = CommunityTagModel2;

// schemas/isoCode/id/communityUser.server.ts
import { mongoClientApp as mongoClientApp83 } from "mongo-connection";
import { Schema as Schema83 } from "mongoose";
var communityUserName2 = "id_communityUser";
var CommunityUserSchema2 = new Schema83(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName2 }
);
var CommunityUserModel2 = mongoClientApp83.model(
  communityUserName2,
  CommunityUserSchema2
);
var communityUser2 = CommunityUserModel2;

// schemas/isoCode/id/communityUserReport.server.ts
import { mongoClientApp as mongoClientApp84 } from "mongo-connection";
import { Schema as Schema84 } from "mongoose";
var communityUserReportName2 = "id_communityUserReport";
var CommunityUserReportSchema2 = new Schema84(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName2 }
);
var CommunityUserReportModel2 = mongoClientApp84.model(
  communityUserReportName2,
  CommunityUserReportSchema2
);
var communityUserReport2 = CommunityUserReportModel2;

// schemas/isoCode/id/employeeProfile.server.ts
import { mongoClientApp as mongoClientApp85 } from "mongo-connection";
import { Schema as Schema85 } from "mongoose";
var employeeProfileName2 = "id_employeeProfile";
var EmployeeProfileSchema2 = new Schema85(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName2 }
);
var EmployeeProfileModel2 = mongoClientApp85.model(
  employeeProfileName2,
  EmployeeProfileSchema2
);
var employeeProfile2 = EmployeeProfileModel2;

// schemas/isoCode/id/FATransaction.server.ts
import { mongoClientApp as mongoClientApp86 } from "mongo-connection";
import { Schema as Schema86 } from "mongoose";
var FATransactionName2 = "id_FATransaction";
var FATransactionSchema2 = new Schema86(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName2 }
);
var FATransactionModel2 = mongoClientApp86.model(
  "id_FATransaction",
  FATransactionSchema2
);
var FATransaction2 = FATransactionModel2;

// schemas/isoCode/id/financialAccount.server.ts
import { Schema as Schema87 } from "mongoose";
import { mongoClientApp as mongoClientApp87 } from "mongo-connection";
var financialAccountName2 = "id_financialAccount";
var FinancialAccountSchema2 = new Schema87(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName2 }
);
var FinancialAccountModel2 = mongoClientApp87.model(
  "id_financialAccount",
  FinancialAccountSchema2
);
var financialAccount2 = FinancialAccountModel2;

// schemas/isoCode/id/flashSale.server.ts
import { mongoClientApp as mongoClientApp88 } from "mongo-connection";
import { Schema as Schema88 } from "mongoose";
var flashSaleName2 = "id_askerFlashSaleIncentive";
var FlashSaleSchema2 = new Schema88(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName2 }
);
var FlashSaleModel2 = mongoClientApp88.model(
  "id_askerFlashSaleIncentive",
  FlashSaleSchema2
);
var flashSale2 = FlashSaleModel2;

// schemas/isoCode/id/historyTasks.server.ts
import { mongoClientApp as mongoClientApp89 } from "mongo-connection";
import { Schema as Schema89 } from "mongoose";
var historyTasksName2 = "id_history_tasks";
var HistoryTasksSchema2 = new Schema89(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema89.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema89.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema89.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema89.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName2 }
);
var HistoryTasksModel2 = mongoClientApp89.model("id_history_tasks", HistoryTasksSchema2);
var historyTasks2 = HistoryTasksModel2;

// schemas/isoCode/id/incentive.server.ts
import { mongoClientApp as mongoClientApp90 } from "mongo-connection";
import { Schema as Schema90 } from "mongoose";
var incentiveName2 = "id_incentive";
var IncentiveSchema2 = new Schema90(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName2 }
);
var IncentiveModel2 = mongoClientApp90.model(incentiveName2, IncentiveSchema2);
var incentive2 = IncentiveModel2;

// schemas/isoCode/id/journeySetting.server.ts
import { mongoClientApp as mongoClientApp91 } from "mongo-connection";
import { Schema as Schema91 } from "mongoose";
var journeySettingName2 = "id_journeySetting";
var JourneySettingSchema2 = new Schema91(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName2 }
);
var JourneySettingModel2 = mongoClientApp91.model(
  journeySettingName2,
  JourneySettingSchema2
);
var journeySetting2 = JourneySettingModel2;

// schemas/isoCode/id/notification.server.ts
import { mongoClientApp as mongoClientApp92 } from "mongo-connection";
import { Schema as Schema92 } from "mongoose";
var notificationName2 = "id_notification";
var NotificationSchema2 = new Schema92(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName2 }
);
var NotificationModel2 = mongoClientApp92.model(
  notificationName2,
  NotificationSchema2
);
var notification2 = NotificationModel2;

// schemas/isoCode/id/partnerBusiness.server.ts
import { mongoClientApp as mongoClientApp93 } from "mongo-connection";
import { Schema as Schema93 } from "mongoose";
var businessName2 = "id_business";
var BusinessSchema2 = new Schema93(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [
      {
        name: { $type: String },
        url: { $type: String }
      }
    ],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema93.Types.Mixed },
            nextData: { $type: Schema93.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema93.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName2 }
);
var BusinessModel2 = mongoClientApp93.model(businessName2, BusinessSchema2);
var business2 = BusinessModel2;

// schemas/isoCode/id/partnerBusinessLevel.server.ts
import { Schema as Schema94 } from "mongoose";
import { mongoClientApp as mongoClientApp94 } from "mongo-connection";
var businessLevelName2 = "id_businessLevel";
var BusinessLevelSchema2 = new Schema94(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName2 }
);
var BusinessLevelModel2 = mongoClientApp94.model(
  businessLevelName2,
  BusinessLevelSchema2
);
var businessLevel2 = BusinessLevelModel2;

// schemas/isoCode/id/partnerBusinessMember.server.ts
import { Schema as Schema95 } from "mongoose";
import { mongoClientApp as mongoClientApp95 } from "mongo-connection";
var businessMemberName2 = "id_businessMember";
var BusinessMemberSchema2 = new Schema95(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName2 }
);
var BusinessMemberModel2 = mongoClientApp95.model(businessMemberName2, BusinessMemberSchema2);
var businessMember2 = BusinessMemberModel2;

// schemas/isoCode/id/partnerBusinessMemberTransaction.server.ts
import { mongoClientApp as mongoClientApp96 } from "mongo-connection";
import { Schema as Schema96 } from "mongoose";
var businessMemberTransactionName2 = "id_businessMemberTransaction";
var BusinessMemberTransactionSchema2 = new Schema96(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    userId: { $type: String, required: true },
    taskId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName2 }
);
var BusinessMemberTransactionModel2 = mongoClientApp96.model(businessMemberTransactionName2, BusinessMemberTransactionSchema2);
var businessMemberTransaction2 = BusinessMemberTransactionModel2;

// schemas/isoCode/id/partnerBusinessSetupAllocateAndReallocate.server.ts
import { mongoClientApp as mongoClientApp97 } from "mongo-connection";
import { Schema as Schema97 } from "mongoose";
var businessSetupAllocationAndReallocationName2 = "id_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema2 = new Schema97(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName2 }
);
var BusinessSetupAllocationAndReallocationModel2 = mongoClientApp97.model(businessSetupAllocationAndReallocationName2, BusinessSetupAllocationAndReallocationSchema2);
var businessSetupAllocationAndReallocation2 = BusinessSetupAllocationAndReallocationModel2;

// schemas/isoCode/id/partnerBusinessTransaction.server.ts
import { mongoClientApp as mongoClientApp98 } from "mongo-connection";
import { Schema as Schema98 } from "mongoose";
var businessTransactionName2 = "id_businessTransaction";
var BusinessTransactionSchema2 = new Schema98(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName2 }
);
var BusinessTransactionModel2 = mongoClientApp98.model(
  businessTransactionName2,
  BusinessTransactionSchema2
);
var businessTransaction2 = BusinessTransactionModel2;

// schemas/isoCode/id/partnerDirectory.server.ts
import { Schema as Schema99 } from "mongoose";
import { mongoClientApp as mongoClientApp99 } from "mongo-connection";
var partnerDirectoryName2 = "id_partnerDirectory";
var PartnerDirectorySchema2 = new Schema99(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName2 }
);
var PartnerDirectoryModel2 = mongoClientApp99.model(
  partnerDirectoryName2,
  PartnerDirectorySchema2
);
var partnerDirectory2 = PartnerDirectoryModel2;

// schemas/isoCode/id/partnerRequest.server.ts
import { Schema as Schema100 } from "mongoose";
import { mongoClientApp as mongoClientApp100 } from "mongo-connection";
var partnerRequestName2 = "id_partnerRequest";
var PartnerRequestSchema2 = new Schema100(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName2 }
);
var PartnerRequestModel2 = mongoClientApp100.model(
  partnerRequestName2,
  PartnerRequestSchema2
);
var partnerRequest2 = PartnerRequestModel2;

// schemas/isoCode/id/paymentToolKitTransaction.server.ts
import { mongoClientApp as mongoClientApp101 } from "mongo-connection";
import { Schema as Schema101 } from "mongoose";
var paymentToolKitTransactionName2 = "id_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema2 = new Schema101(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName2 }
);
var PaymentToolKitTransactionModel2 = mongoClientApp101.model(
  paymentToolKitTransactionName2,
  PaymentToolKitTransactionSchema2
);
var paymentToolKitTransaction2 = PaymentToolKitTransactionModel2;

// schemas/isoCode/id/promotionCode.server.ts
import { mongoClientApp as mongoClientApp102 } from "mongo-connection";
import { Schema as Schema102 } from "mongoose";
var promotionCodeName2 = "id_promotionCode";
var PromotionCodeSchema2 = new Schema102(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: Schema102.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName2 }
);
var PromotionCodeModel2 = mongoClientApp102.model(
  promotionCodeName2,
  PromotionCodeSchema2
);
var promotionCode2 = PromotionCodeModel2;

// schemas/isoCode/id/promotionHistory.server.ts
import { mongoClientApp as mongoClientApp103 } from "mongo-connection";
import { Schema as Schema103 } from "mongoose";
var promotionHistoryName2 = "id_promotionHistory";
var PromotionHistorySchema2 = new Schema103(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName2 }
);
var PromotionHistoryModel2 = mongoClientApp103.model(
  promotionHistoryName2,
  PromotionHistorySchema2
);
var promotionHistory2 = PromotionHistoryModel2;

// schemas/isoCode/id/promotionSource.server.ts
import { Schema as Schema104 } from "mongoose";
import { mongoClientApp as mongoClientApp104 } from "mongo-connection";
var promotionSourceName2 = "id_promotionSource";
var PromotionSourceSchema2 = new Schema104(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName2 }
);
var PromotionSourceModel2 = mongoClientApp104.model(
  promotionSourceName2,
  PromotionSourceSchema2
);
var promotionSource2 = PromotionSourceModel2;

// schemas/isoCode/id/rating.server.ts
import { mongoClientApp as mongoClientApp105 } from "mongo-connection";
import { Schema as Schema105 } from "mongoose";
var ratingName2 = "id_rating";
var RatingSchema2 = new Schema105(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName2 }
);
var RatingModel2 = mongoClientApp105.model(ratingName2, RatingSchema2);
var rating2 = RatingModel2;

// schemas/isoCode/id/referralCampaign.server.ts
import { mongoClientApp as mongoClientApp106 } from "mongo-connection";
import { Schema as Schema106 } from "mongoose";
var referralCampaignName2 = "id_askerReferralCampaign";
var ReferralCampaignSchema2 = new Schema106(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: Schema106.Types.Mixed, required: true },
    inviter: { $type: Schema106.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName2 }
);
var ReferralCampaignModel2 = mongoClientApp106.model(
  referralCampaignName2,
  ReferralCampaignSchema2
);
var referralCampaign2 = ReferralCampaignModel2;

// schemas/isoCode/id/service.server.ts
import { mongoClientApp as mongoClientApp107 } from "mongo-connection";
import { Schema as Schema107 } from "mongoose";
var serviceName2 = "id_service";
var ServiceSchema2 = new Schema107(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: Schema107.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: Schema107.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: Schema107.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName2 }
);
var ServiceModel2 = mongoClientApp107.model("id_service", ServiceSchema2);
var service2 = ServiceModel2;

// schemas/isoCode/id/serviceChannel.server.ts
import { mongoClientApp as mongoClientApp108 } from "mongo-connection";
import { Schema as Schema108 } from "mongoose";
var serviceChannelName2 = "id_serviceChannel";
var ServiceChannelSchema2 = new Schema108(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName2 }
);
var ServiceChannelModel2 = mongoClientApp108.model(
  serviceChannelName2,
  ServiceChannelSchema2
);
var serviceChannel2 = ServiceChannelModel2;

// schemas/isoCode/id/settingCountry.server.ts
import { mongoClientApp as mongoClientApp109 } from "mongo-connection";
import { Schema as Schema109 } from "mongoose";
var settingCountryName2 = "id_settingCountry";
var SettingCountrySchema2 = new Schema109(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName2 }
);
var SettingCountryModel2 = mongoClientApp109.model(
  settingCountryName2,
  SettingCountrySchema2
);
var settingCountry2 = SettingCountryModel2;

// schemas/isoCode/id/settingSystem.server.ts
import { mongoClientApp as mongoClientApp110 } from "mongo-connection";
import { Schema as Schema110 } from "mongoose";
var settingSystemName2 = "id_settingSystem";
var SettingSystemSchema2 = new Schema110(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: Schema110.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName2 }
);
var SettingSystemModel2 = mongoClientApp110.model(
  "id_settingSystem",
  SettingSystemSchema2
);
var settingSystem2 = SettingSystemModel2;

// schemas/isoCode/id/subscription.server.ts
import { mongoClientApp as mongoClientApp111 } from "mongo-connection";
import { Schema as Schema111 } from "mongoose";
var subscriptionName2 = "id_subscription";
var SubscriptionSchema2 = new Schema111(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: Schema111.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: Schema111.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName2 }
);
var SubscriptionModel2 = mongoClientApp111.model(subscriptionName2, SubscriptionSchema2);
var subscription2 = SubscriptionModel2;

// schemas/isoCode/id/task.server.ts
import { mongoClientApp as mongoClientApp112 } from "mongo-connection";
import { Schema as Schema112 } from "mongoose";
var taskName2 = "id_task";
var TasksSchema2 = new Schema112(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema112.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema112.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema112.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema112.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: taskName2 }
);
var TasksModel2 = mongoClientApp112.model("id_tasks", TasksSchema2);
var task2 = TasksModel2;

// schemas/isoCode/id/taskerBNPLProcess.server.ts
import { mongoClientApp as mongoClientApp113 } from "mongo-connection";
import { Schema as Schema113 } from "mongoose";
var taskerBNPLProcessName2 = "id_taskerBNPLProcess";
var TaskerBNPLProcess2 = new Schema113(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName2 }
);
var TaskerBNPLProcessModel2 = mongoClientApp113.model(
  "id_taskerBNPLProcess",
  TaskerBNPLProcess2
);
var taskerBNPLProcess2 = TaskerBNPLProcessModel2;

// schemas/isoCode/id/taskerBNPLTransaction.server.ts
import { mongoClientApp as mongoClientApp114 } from "mongo-connection";
import { Schema as Schema114 } from "mongoose";
var taskerBNPLTransactionName2 = "id_taskerBNPLTransaction";
var TaskerBNPLTransaction2 = new Schema114(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName2 }
);
var TaskerBNPLTransactionModel2 = mongoClientApp114.model(
  taskerBNPLTransactionName2,
  TaskerBNPLTransaction2
);
var taskerBNPLTransaction2 = TaskerBNPLTransactionModel2;

// schemas/isoCode/id/taskerGift.server.ts
import { Schema as Schema115 } from "mongoose";
import { mongoClientApp as mongoClientApp115 } from "mongo-connection";
var taskerGiftName2 = "id_taskerGift";
var TaskerGiftSchema2 = new Schema115(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName2 }
);
var TaskerGiftModel2 = mongoClientApp115.model("id_taskerGift", TaskerGiftSchema2);
var taskerGift2 = TaskerGiftModel2;

// schemas/isoCode/id/taskerIncentive.server.ts
import { Schema as Schema116 } from "mongoose";
import { mongoClientApp as mongoClientApp116 } from "mongo-connection";
var taskerIncentiveName2 = "id_taskerIncentive";
var TaskerIncentiveSchema2 = new Schema116(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: Schema116.Types.Mixed
    },
    status: {
      $type: String
    },
    codeFromPartner: {
      $type: Number
    },
    giftInfo: Schema116.Types.Mixed,
    social: Schema116.Types.Mixed,
    codeList: Schema116.Types.Mixed,
    office: Schema116.Types.Mixed,
    applyFor: Schema116.Types.Mixed,
    brandInfo: Schema116.Types.Mixed,
    redeemLink: Schema116.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName2 }
);
var TaskerIncentiveModel2 = mongoClientApp116.model(
  "id_taskerIncentive",
  TaskerIncentiveSchema2
);
var taskerIncentive2 = TaskerIncentiveModel2;

// schemas/isoCode/id/taskerOnboardingSetting.server.ts
import { mongoClientApp as mongoClientApp117 } from "mongo-connection";
import { Schema as Schema117 } from "mongoose";
var taskerOnboardingSettingName2 = "id_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema2 = new Schema117(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName2 }
);
var TaskerOnboardingSettingModel2 = mongoClientApp117.model(
  taskerOnboardingSettingName2,
  TaskerOnboardingSettingSchema2
);
var taskerOnboardingSetting2 = TaskerOnboardingSettingModel2;

// schemas/isoCode/id/taskerPointTransaction.server.ts
import { Schema as Schema118 } from "mongoose";
import { mongoClientApp as mongoClientApp118 } from "mongo-connection";
var taskerPointTransactionName2 = "id_taskerPointTransaction";
var TaskerPointTransactionSchema2 = new Schema118(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName2 }
);
var TaskerPointTransactionModel2 = mongoClientApp118.model(
  "id_taskerPointTransaction",
  TaskerPointTransactionSchema2
);
var taskerPointTransaction2 = TaskerPointTransactionModel2;

// schemas/isoCode/id/taskerProfile.server.ts
import { Schema as Schema119 } from "mongoose";
import { mongoClientApp as mongoClientApp119 } from "mongo-connection";
var taskerProfileName2 = "id_taskerProfile";
var TaskerProfileSchema2 = new Schema119(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName2
  }
);
var TaskerProfileModel2 = mongoClientApp119.model(
  "id_taskerProfile",
  TaskerProfileSchema2
);
var taskerProfile2 = TaskerProfileModel2;

// schemas/isoCode/id/taskerSpecialCampaign.server.ts
import { mongoClientApp as mongoClientApp120 } from "mongo-connection";
import { Schema as Schema120 } from "mongoose";
var taskerSpecialCampaignName2 = "id_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema2 = new Schema120(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: Schema120.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName2, _id: false }
);
var TaskerSpecialCampaignModel2 = mongoClientApp120.model(
  taskerSpecialCampaignName2,
  TaskerSpecialCampaignSchema2
);
var taskerSpecialCampaign2 = TaskerSpecialCampaignModel2;

// schemas/isoCode/id/taskerSpecialCampaignTransaction.server.ts
import { mongoClientApp as mongoClientApp121 } from "mongo-connection";
import { Schema as Schema121 } from "mongoose";
var taskerSpecialCampaignTransactionName2 = "id_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema2 = new Schema121(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName2 }
);
var TaskerSpecialCampaignTransactionModel2 = mongoClientApp121.model(
  taskerSpecialCampaignTransactionName2,
  TaskerSpecialCampaignTransactionSchema2
);
var taskerSpecialCampaignTransaction2 = TaskerSpecialCampaignTransactionModel2;

// schemas/isoCode/id/taskerToolkitLadingDetails.server.ts
import { Schema as Schema122 } from "mongoose";
import { mongoClientApp as mongoClientApp122 } from "mongo-connection";
var taskerToolkitLadingDetailsName2 = "id_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema2 = new Schema122(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName2 }
);
var TaskerToolkitLadingDetailsModel2 = mongoClientApp122.model(
  "id_taskerToolkitLadingDetails",
  TaskerToolkitLadingDetailsSchema2
);
var taskerToolkitLadingDetails2 = TaskerToolkitLadingDetailsModel2;

// schemas/isoCode/id/taskerTrainingCourse.server.ts
import { mongoClientApp as mongoClientApp123 } from "mongo-connection";
import { Schema as Schema123 } from "mongoose";
var taskerTrainingCourseName2 = "id_trainingTaskerCourse";
var CourseSchema2 = new Schema123(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName2 }
);
var CourseModel2 = mongoClientApp123.model(
  taskerTrainingCourseName2,
  CourseSchema2
);
var taskerTrainingCourse2 = CourseModel2;

// schemas/isoCode/id/taskerTrainingCourseStartDate.ts
import { mongoClientApp as mongoClientApp124 } from "mongo-connection";
import { Schema as Schema124 } from "mongoose";
var taskerTrainingCourseStartDateName2 = "id_trainingTaskerCourseStartDate";
var CourseStartDateSchema2 = new Schema124(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName2 }
);
var CourseStartDateModel2 = mongoClientApp124.model(
  taskerTrainingCourseStartDateName2,
  CourseStartDateSchema2
);
var taskerTrainingCourseStartDate2 = CourseStartDateModel2;

// schemas/isoCode/id/taskerTrainingQuiz.server.ts
import { Schema as Schema125 } from "mongoose";
import { mongoClientApp as mongoClientApp125 } from "mongo-connection";
var taskerTrainingQuizName2 = "id_trainingTaskerQuiz";
var QuizSchema2 = new Schema125(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName2 }
);
var QuizModel2 = mongoClientApp125.model(taskerTrainingQuizName2, QuizSchema2);
var taskerTrainingQuiz2 = QuizModel2;

// schemas/isoCode/id/taskerTrainingQuizCollection.server.ts
import { Schema as Schema126 } from "mongoose";
import { mongoClientApp as mongoClientApp126 } from "mongo-connection";
var taskerTrainingQuizCollectionName2 = "id_trainingTaskerQuizCollection";
var QuizCollectionSchema2 = new Schema126(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName2 }
);
var QuizCollectionModel2 = mongoClientApp126.model(
  taskerTrainingQuizCollectionName2,
  QuizCollectionSchema2
);
var taskerTrainingQuizCollection2 = QuizCollectionModel2;

// schemas/isoCode/id/taskerTrainingSubmission.server.ts
import { Schema as Schema127 } from "mongoose";
import { mongoClientApp as mongoClientApp127 } from "mongo-connection";
var taskerTrainingSubmissionName2 = "id_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema2 = new Schema127(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName2 }
);
var TaskerTrainingSubmissionModel2 = mongoClientApp127.model(
  taskerTrainingSubmissionName2,
  TaskerTrainingSubmissionSchema2
);
var taskerTrainingSubmission2 = TaskerTrainingSubmissionModel2;

// schemas/isoCode/id/thingsToKnow.server.ts
import { Schema as Schema128 } from "mongoose";
import { mongoClientApp as mongoClientApp128 } from "mongo-connection";
var thingsToKnowName2 = "id_thingsToKnow";
var ThingToKnowSchema2 = new Schema128(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName2 }
);
var ThingsToKnowModel2 = mongoClientApp128.model(
  "id_thingsToKnow",
  ThingToKnowSchema2
);
var thingsToKnow2 = ThingsToKnowModel2;

// schemas/isoCode/id/toolKitItems.server.ts
import { Schema as Schema129 } from "mongoose";
import { mongoClientApp as mongoClientApp129 } from "mongo-connection";
var toolKitItemsName2 = "id_toolKitItems";
var ToolKitItemsSchema2 = new Schema129(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName2 }
);
var ToolKitItemsModel2 = mongoClientApp129.model(
  "id_toolKitItems",
  ToolKitItemsSchema2
);
var toolKitItems2 = ToolKitItemsModel2;

// schemas/isoCode/id/toolKitSetting.server.ts
import { Schema as Schema130 } from "mongoose";
import { mongoClientApp as mongoClientApp130 } from "mongo-connection";
var toolKitSettingName2 = "id_toolKitSetting";
var ToolKitSettingSchema2 = new Schema130(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName2 }
);
var ToolKitSettingModel2 = mongoClientApp130.model(
  "id_toolKitSetting",
  ToolKitSettingSchema2
);
var toolKitSetting2 = ToolKitSettingModel2;

// schemas/isoCode/id/trainingJourney.server.ts
import { Schema as Schema131 } from "mongoose";
import { mongoClientApp as mongoClientApp131 } from "mongo-connection";
var trainingJourneyName2 = "id_trainingJourney";
var TrainingJourneySchema2 = new Schema131(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName2 }
);
var TrainingJourneyModel2 = mongoClientApp131.model(
  "id_trainingJourney",
  TrainingJourneySchema2
);
var trainingJourney2 = TrainingJourneyModel2;

// schemas/isoCode/id/trainingTasker.server.ts
import { Schema as Schema132 } from "mongoose";
import { mongoClientApp as mongoClientApp132 } from "mongo-connection";
var trainingTaskerName2 = "id_trainingTasker";
var TrainingTaskerSchema2 = new Schema132(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName2 }
);
var TrainingTaskerModel2 = mongoClientApp132.model(
  "id_trainingTasker",
  TrainingTaskerSchema2
);
var trainingTasker2 = TrainingTaskerModel2;

// schemas/isoCode/id/userActivation.server.ts
import { mongoClientApp as mongoClientApp133 } from "mongo-connection";
import { Schema as Schema133 } from "mongoose";
var userActivationName2 = "id_userActivation";
var UserActivationSchema2 = new Schema133(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName2 }
);
var UserActivationModel2 = mongoClientApp133.model(
  userActivationName2,
  UserActivationSchema2
);
var userActivation2 = UserActivationModel2;

// schemas/isoCode/id/userApp.server.ts
import { mongoClientApp as mongoClientApp134 } from "mongo-connection";
import { Schema as Schema134 } from "mongoose";
var usersName2 = "users";
var UsersAppSchema2 = new Schema134(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName2 }
);
var UsersAppModel2 = mongoClientApp134.model("id_users", UsersAppSchema2);
var users2 = UsersAppModel2;

// schemas/isoCode/id/userComboVoucher.server.ts
import { mongoClientApp as mongoClientApp135 } from "mongo-connection";
import { Schema as Schema135 } from "mongoose";
var userComboVoucherName2 = "id_userComboVoucher";
var UserComboVoucherSchema2 = new Schema135(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: Schema135.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: Schema135.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName2 }
);
var UserComboVoucherModel2 = mongoClientApp135.model(
  userComboVoucherName2,
  UserComboVoucherSchema2
);
var userComboVoucher2 = UserComboVoucherModel2;

// schemas/isoCode/id/userLocationHistory.server.ts
import { Schema as Schema136 } from "mongoose";
import { mongoClientApp as mongoClientApp136 } from "mongo-connection";
var userLocationHistoryName2 = "id_userLocationHistory";
var UserLocationHistorySchema2 = new Schema136(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName2 }
);
var UserLocationHistoryModel2 = mongoClientApp136.model(
  userLocationHistoryName2,
  UserLocationHistorySchema2
);
var userLocationHistory2 = UserLocationHistoryModel2;

// schemas/isoCode/id/userProfile.server.ts
import { mongoClientApp as mongoClientApp137 } from "mongo-connection";
import { Schema as Schema137 } from "mongoose";
var userProfileName2 = "id_userProfile";
var UserProfileSchema2 = new Schema137(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Schema137.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName2 }
);
var UserProfileModel2 = mongoClientApp137.model(userProfileName2, UserProfileSchema2);
var userProfile2 = UserProfileModel2;

// schemas/isoCode/id/workingPlaces.server.ts
import { Schema as Schema138 } from "mongoose";
import { mongoClientApp as mongoClientApp138 } from "mongo-connection";
var workingPlacesName2 = "id_workingPlaces";
var WorkingPlacesSchema2 = new Schema138(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName2 }
);
var WorkingPlacesModel2 = mongoClientApp138.model(
  "id_workingPlaces",
  WorkingPlacesSchema2
);
var workingPlaces2 = WorkingPlacesModel2;

// schemas/isoCode/id/index.server.ts
var ID = {
  task: task2,
  historyTasks: historyTasks2,
  userLocationHistory: userLocationHistory2,
  trainingTasker: trainingTasker2,
  trainingJourney: trainingJourney2,
  toolKitSetting: toolKitSetting2,
  toolKitItems: toolKitItems2,
  thingsToKnow: thingsToKnow2,
  taskerTrainingSubmission: taskerTrainingSubmission2,
  taskerTrainingQuizCollection: taskerTrainingQuizCollection2,
  taskerTrainingQuiz: taskerTrainingQuiz2,
  taskerTrainingCourse: taskerTrainingCourse2,
  taskerToolkitLadingDetails: taskerToolkitLadingDetails2,
  financialAccount: financialAccount2,
  taskerIncentive: taskerIncentive2,
  taskerPointTransaction: taskerPointTransaction2,
  taskerGift: taskerGift2,
  notification: notification2,
  employeeProfile: employeeProfile2,
  workingPlaces: workingPlaces2,
  taskerProfile: taskerProfile2,
  flashSale: flashSale2,
  incentive: incentive2,
  promotionCode: promotionCode2,
  promotionSource: promotionSource2,
  service: service2,
  promotionHistory: promotionHistory2,
  settingCountry: settingCountry2,
  comboVoucher: comboVoucher2,
  referralCampaign: referralCampaign2,
  marketingCampaign: marketingCampaign2,
  settingSystem: settingSystem2,
  partnerRequest: partnerRequest2,
  partnerDirectory: partnerDirectory2,
  communityComment: communityComment2,
  communityMedal: communityMedal2,
  communityNotification: communityNotification2,
  communityPost: communityPost2,
  communitySetting: communitySetting2,
  communityTag: communityTag2,
  communityUser: communityUser2,
  communityUserReport: communityUserReport2,
  subscription: subscription2,
  taskerOnboardingSetting: taskerOnboardingSetting2,
  users: users2,
  serviceChannel: serviceChannel2,
  bEmployee: bEmployee2,
  bEmployeeSetting: bEmployeeSetting2,
  FATransaction: FATransaction2,
  taskerBNPLTransaction: taskerBNPLTransaction2,
  taskerBNPLProcess: taskerBNPLProcess2,
  paymentToolKitTransaction: paymentToolKitTransaction2,
  rating: rating2,
  userActivation: userActivation2,
  business: business2,
  businessLevel: businessLevel2,
  businessMember: businessMember2,
  businessMemberTransaction: businessMemberTransaction2,
  businessTransaction: businessTransaction2,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocation2,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDate2,
  taskerSpecialCampaign: taskerSpecialCampaign2,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransaction2,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethod2,
  userProfile: userProfile2,
  communityTagOrder: communityTagOrder2,
  journeySetting: journeySetting2,
  userComboVoucher: userComboVoucher2,
  bundleVoucher: bundleVoucher2
};
var IDName = {
  trainingTasker: trainingTaskerName2,
  historyTasks: historyTasksName2,
  userLocationHistory: userLocationHistoryName2,
  trainingJourney: trainingJourneyName2,
  toolKitSetting: toolKitSettingName2,
  toolKitItems: toolKitItemsName2,
  thingsToKnow: thingsToKnowName2,
  taskerTrainingSubmission: taskerTrainingSubmissionName2,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName2,
  taskerTrainingQuiz: taskerTrainingQuizName2,
  taskerTrainingCourse: taskerTrainingCourseName2,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName2,
  taskerPointTransaction: taskerPointTransactionName2,
  taskerIncentive: taskerIncentiveName2,
  taskerGift: taskerGiftName2,
  notification: notificationName2,
  financialAccount: financialAccountName2,
  taskerProfile: taskerProfileName2,
  employeeProfile: employeeProfileName2,
  task: taskName2,
  workingPlaces: workingPlacesName2,
  service: serviceName2,
  settingCountry: settingCountryName2,
  settingSystem: settingSystemName2,
  incentive: incentiveName2,
  communityComment: communityCommentName2,
  communityMedal: communityMedalName2,
  communityNotification: communityNotificationName2,
  communityPost: communityPostName2,
  communitySetting: communitySettingName2,
  communityTag: communityTagName2,
  communityUser: communityUserName2,
  communityUserReport: communityUserReportName2,
  subscription: subscriptionName2,
  taskerOnboardingSetting: taskerOnboardingSettingName2,
  users: usersName2,
  serviceChannel: serviceChannelName2,
  bEmployee: bEmployeeName2,
  bEmployeeSetting: bEmployeeSettingName2,
  FATransaction: FATransactionName2,
  taskerBNPLTransaction: taskerBNPLTransactionName2,
  taskerBNPLProcess: taskerBNPLProcessName2,
  paymentToolKitTransaction: paymentToolKitTransactionName2,
  promotionCode: promotionCodeName2,
  promotionHistory: promotionHistoryName2,
  marketingCampaign: marketingCampaignName2,
  rating: ratingName2,
  userActivation: userActivationName2,
  business: businessName2,
  businessLevel: businessLevelName2,
  businessMember: businessMemberName2,
  businessMemberTransaction: businessMemberTransactionName2,
  businessTransaction: businessTransactionName2,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName2,
  comboVoucher: comboVoucherName2,
  flashSale: flashSaleName2,
  partnerDirectory: partnerDirectoryName2,
  partnerRequest: partnerRequestName2,
  promotionSource: promotionSourceName2,
  referralCampaign: referralCampaignName2,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName2,
  taskerSpecialCampaign: taskerSpecialCampaignName2,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName2,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName2,
  userProfile: userProfileName2,
  communityTagOrder: communityTagOrderName2,
  journeySetting: journeySettingName2,
  userComboVoucher: userComboVoucherName2,
  bundleVoucher: bundleVoucherName2
};
var index_server_default2 = ID;

// schemas/isoCode/th/bEmployee.server.ts
import { mongoClientApp as mongoClientApp139 } from "mongo-connection";
import { Schema as Schema139 } from "mongoose";
var bEmployeeName3 = "th_bEmployee";
var BEmployeeSchema3 = new Schema139(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName3 }
);
var BEmployeeModel3 = mongoClientApp139.model("th_bEmployee", BEmployeeSchema3);
var bEmployee3 = BEmployeeModel3;

// schemas/isoCode/th/bEmployeeSetting.server.ts
import { mongoClientApp as mongoClientApp140 } from "mongo-connection";
import { Schema as Schema140 } from "mongoose";
var bEmployeeSettingName3 = "th_bEmployeeSetting";
var BEmployeeSettingSchema3 = new Schema140(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName3 }
);
var BEmployeeSettingModel3 = mongoClientApp140.model(
  "th_bEmployeeSetting",
  BEmployeeSettingSchema3
);
var bEmployeeSetting3 = BEmployeeSettingModel3;

// schemas/isoCode/th/bundleVoucher.ts
import { mongoClientApp as mongoClientApp141 } from "mongo-connection";
import { Schema as Schema141 } from "mongoose";
var bundleVoucherName3 = "th_bundleVoucher";
var BundleVoucherSchema3 = new Schema141(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema141.Types.Mixed },
            nextData: { $type: Schema141.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema141.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName3 }
);
var BundleVoucherModel3 = mongoClientApp141.model(
  bundleVoucherName3,
  BundleVoucherSchema3
);
var bundleVoucher3 = BundleVoucherModel3;

// schemas/isoCode/th/campaign-payment.server.ts
import { mongoClientApp as mongoClientApp142 } from "mongo-connection";
import { Schema as Schema142 } from "mongoose";
var marketingCampaignPaymentMethodName3 = "th_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema3 = new Schema142(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: Schema142.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName3 }
);
var MarketingCampaignPaymentMethodModel3 = mongoClientApp142.model(
  marketingCampaignPaymentMethodName3,
  MarketingCampaignPaymentMethodSchema3
);
var marketingCampaignPaymentMethod3 = MarketingCampaignPaymentMethodModel3;

// schemas/isoCode/th/campaign.server.ts
import { mongoClientApp as mongoClientApp143 } from "mongo-connection";
import { Schema as Schema143 } from "mongoose";
var marketingCampaignName3 = "th_marketingCampaign";
var MarketingCampaignSchema3 = new Schema143(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: Schema143.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: Schema143.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema143.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema143.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema143.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema143.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName3 }
);
var MarketingCampaignModel3 = mongoClientApp143.model(
  marketingCampaignName3,
  MarketingCampaignSchema3
);
var marketingCampaign3 = MarketingCampaignModel3;

// schemas/isoCode/th/comboVoucher.server.ts
import { mongoClientApp as mongoClientApp144 } from "mongo-connection";
import { Schema as Schema144 } from "mongoose";
var comboVoucherName3 = "th_comboVoucher";
var ComboVoucherSchema3 = new Schema144(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName3 }
);
var ComboVoucherModel3 = mongoClientApp144.model(
  "th_comboVoucher",
  ComboVoucherSchema3
);
var comboVoucher3 = ComboVoucherModel3;

// schemas/isoCode/th/communityComment.server.ts
import { mongoClientApp as mongoClientApp145 } from "mongo-connection";
import { Schema as Schema145 } from "mongoose";
var communityCommentName3 = "th_communityComment";
var CommunityCommentSchema3 = new Schema145(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName3 }
);
var CommunityCommentModel3 = mongoClientApp145.model(
  communityCommentName3,
  CommunityCommentSchema3
);
var communityComment3 = CommunityCommentModel3;

// schemas/isoCode/th/communityMedal.server.ts
import { mongoClientApp as mongoClientApp146 } from "mongo-connection";
import { Schema as Schema146 } from "mongoose";
var communityMedalName3 = "th_communityMedal";
var CommunityMedalSchema3 = new Schema146(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: Schema146.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: Schema146.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: Schema146.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName3 }
);
var CommunityMedalModel3 = mongoClientApp146.model(
  communityMedalName3,
  CommunityMedalSchema3
);
var communityMedal3 = CommunityMedalModel3;

// schemas/isoCode/th/communityNotification.server.ts
import { mongoClientApp as mongoClientApp147 } from "mongo-connection";
import { Schema as Schema147 } from "mongoose";
var communityNotificationName3 = "th_communityNotification";
var CommunityNotificationSchema3 = new Schema147(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: Schema147.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName3 }
);
var CommunityNotificationModel3 = mongoClientApp147.model(
  communityNotificationName3,
  CommunityNotificationSchema3
);
var communityNotification3 = CommunityNotificationModel3;

// schemas/isoCode/th/communityOrderTag.server.ts
import { mongoClientApp as mongoClientApp148 } from "mongo-connection";
import { Schema as Schema148 } from "mongoose";
var communityTagOrderName3 = "th_communityTagOrder";
var CommunityTagOrderSchema3 = new Schema148(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: Schema148.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: Schema148.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName3 }
);
var CommunityTagOrderModel3 = mongoClientApp148.model(
  communityTagOrderName3,
  CommunityTagOrderSchema3
);
var communityTagOrder3 = CommunityTagOrderModel3;

// schemas/isoCode/th/communityPost.server.ts
import { mongoClientApp as mongoClientApp149 } from "mongo-connection";
import { Schema as Schema149 } from "mongoose";
var communityPostName3 = "th_communityPost";
var CommunityPostSchema3 = new Schema149(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: Schema149.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityPostName3 }
);
var CommunityPostModel3 = mongoClientApp149.model(
  communityPostName3,
  CommunityPostSchema3
);
var communityPost3 = CommunityPostModel3;

// schemas/isoCode/th/communitySetting.server.ts
import { mongoClientApp as mongoClientApp150 } from "mongo-connection";
import { Schema as Schema150 } from "mongoose";
var communitySettingName3 = "th_communitySetting";
var CommunitySettingSchema3 = new Schema150(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: Schema150.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName3
  }
);
var CommunitySettingModel3 = mongoClientApp150.model(
  communitySettingName3,
  CommunitySettingSchema3
);
var communitySetting3 = CommunitySettingModel3;

// schemas/isoCode/th/communityTag.server.ts
import { mongoClientApp as mongoClientApp151 } from "mongo-connection";
import { Schema as Schema151 } from "mongoose";
var communityTagName3 = "th_communityTag";
var CommunityTagSchema3 = new Schema151(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: Schema151.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: Schema151.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName3 }
);
var CommunityTagModel3 = mongoClientApp151.model(
  communityTagName3,
  CommunityTagSchema3
);
var communityTag3 = CommunityTagModel3;

// schemas/isoCode/th/communityUser.server.ts
import { mongoClientApp as mongoClientApp152 } from "mongo-connection";
import { Schema as Schema152 } from "mongoose";
var communityUserName3 = "th_communityUser";
var CommunityUserSchema3 = new Schema152(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName3 }
);
var CommunityUserModel3 = mongoClientApp152.model(
  communityUserName3,
  CommunityUserSchema3
);
var communityUser3 = CommunityUserModel3;

// schemas/isoCode/th/communityUserReport.server.ts
import { mongoClientApp as mongoClientApp153 } from "mongo-connection";
import { Schema as Schema153 } from "mongoose";
var communityUserReportName3 = "th_communityUserReport";
var CommunityUserReportSchema3 = new Schema153(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName3 }
);
var CommunityUserReportModel3 = mongoClientApp153.model(
  communityUserReportName3,
  CommunityUserReportSchema3
);
var communityUserReport3 = CommunityUserReportModel3;

// schemas/isoCode/th/employeeProfile.server.ts
import { Schema as Schema154 } from "mongoose";
import { mongoClientApp as mongoClientApp154 } from "mongo-connection";
var employeeProfileName3 = "th_employeeProfile";
var EmployeeProfileSchema3 = new Schema154(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName3 }
);
var EmployeeProfileModel3 = mongoClientApp154.model(
  "th_employeeProfile",
  EmployeeProfileSchema3
);
var employeeProfile3 = EmployeeProfileModel3;

// schemas/isoCode/th/FATransaction.server.ts
import { mongoClientApp as mongoClientApp155 } from "mongo-connection";
import { Schema as Schema155 } from "mongoose";
var FATransactionName3 = "th_FATransaction";
var FATransactionSchema3 = new Schema155(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName3 }
);
var FATransactionModel3 = mongoClientApp155.model(
  "th_FATransaction",
  FATransactionSchema3
);
var FATransaction3 = FATransactionModel3;

// schemas/isoCode/th/financialAccount.server.ts
import { Schema as Schema156 } from "mongoose";
import { mongoClientApp as mongoClientApp156 } from "mongo-connection";
var financialAccountName3 = "th_financialAccount";
var FinancialAccountSchema3 = new Schema156(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName3 }
);
var FinancialAccountModel3 = mongoClientApp156.model(
  "th_financialAccount",
  FinancialAccountSchema3
);
var financialAccount3 = FinancialAccountModel3;

// schemas/isoCode/th/flashSale.server.ts
import { mongoClientApp as mongoClientApp157 } from "mongo-connection";
import { Schema as Schema157 } from "mongoose";
var flashSaleName3 = "th_askerFlashSaleIncentive";
var FlashSaleSchema3 = new Schema157(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName3 }
);
var FlashSaleModel3 = mongoClientApp157.model(
  "th_askerFlashSaleIncentive",
  FlashSaleSchema3
);
var flashSale3 = FlashSaleModel3;

// schemas/isoCode/th/historyTasks.server.ts
import { mongoClientApp as mongoClientApp158 } from "mongo-connection";
import { Schema as Schema158 } from "mongoose";
var historyTasksName3 = "history_tasks";
var HistoryTasksSchema3 = new Schema158(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema158.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema158.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema158.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema158.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName3 }
);
var HistoryTasksModel3 = mongoClientApp158.model("th_history_tasks", HistoryTasksSchema3);
var historyTasks3 = HistoryTasksModel3;

// schemas/isoCode/th/incentive.server.ts
import { mongoClientApp as mongoClientApp159 } from "mongo-connection";
import { Schema as Schema159 } from "mongoose";
var incentiveName3 = "th_incentive";
var IncentiveSchema3 = new Schema159(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName3 }
);
var IncentiveModel3 = mongoClientApp159.model("th_incentive", IncentiveSchema3);
var incentive3 = IncentiveModel3;

// schemas/isoCode/th/journeySetting.server.ts
import { mongoClientApp as mongoClientApp160 } from "mongo-connection";
import { Schema as Schema160 } from "mongoose";
var journeySettingName3 = "th_journeySetting";
var JourneySettingSchema3 = new Schema160(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName3 }
);
var JourneySettingModel3 = mongoClientApp160.model(
  journeySettingName3,
  JourneySettingSchema3
);
var journeySetting3 = JourneySettingModel3;

// schemas/isoCode/th/notification.server.ts
import { Schema as Schema161 } from "mongoose";
import { mongoClientApp as mongoClientApp161 } from "mongo-connection";
var notificationName3 = "th_notification";
var NotificationSchema3 = new Schema161(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName3 }
);
var NotificationModel3 = mongoClientApp161.model(
  "th_notification",
  NotificationSchema3
);
var notification3 = NotificationModel3;

// schemas/isoCode/th/partnerBusiness.server.ts
import { mongoClientApp as mongoClientApp162 } from "mongo-connection";
import { Schema as Schema162 } from "mongoose";
var businessName3 = "th_business";
var BusinessSchema3 = new Schema162(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [{ name: { $type: String }, url: { $type: String } }],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema162.Types.Mixed },
            nextData: { $type: Schema162.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema162.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName3 }
);
var BusinessModel3 = mongoClientApp162.model(businessName3, BusinessSchema3);
var business3 = BusinessModel3;

// schemas/isoCode/th/partnerBusinessLevel.server.ts
import { Schema as Schema163 } from "mongoose";
import { mongoClientApp as mongoClientApp163 } from "mongo-connection";
var businessLevelName3 = "th_businessLevel";
var BusinessLevelSchema3 = new Schema163(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName3 }
);
var BusinessLevelModel3 = mongoClientApp163.model(
  businessLevelName3,
  BusinessLevelSchema3
);
var businessLevel3 = BusinessLevelModel3;

// schemas/isoCode/th/partnerBusinessMember.server.ts
import { Schema as Schema164 } from "mongoose";
import { mongoClientApp as mongoClientApp164 } from "mongo-connection";
var businessMemberName3 = "th_businessMember";
var BusinessMemberSchema3 = new Schema164(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName3 }
);
var BusinessMemberModel3 = mongoClientApp164.model(businessMemberName3, BusinessMemberSchema3);
var businessMember3 = BusinessMemberModel3;

// schemas/isoCode/th/partnerBusinessMemberTransaction.server.ts
import { mongoClientApp as mongoClientApp165 } from "mongo-connection";
import { Schema as Schema165 } from "mongoose";
var businessMemberTransactionName3 = "th_businessMemberTransaction";
var BusinessMemberTransactionSchema3 = new Schema165(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    taskId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    userId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName3 }
);
var BusinessMemberTransactionModel3 = mongoClientApp165.model(businessMemberTransactionName3, BusinessMemberTransactionSchema3);
var businessMemberTransaction3 = BusinessMemberTransactionModel3;

// schemas/isoCode/th/partnerBusinessSetupAllocateAndReallocate.server.ts
import { mongoClientApp as mongoClientApp166 } from "mongo-connection";
import { Schema as Schema166 } from "mongoose";
var businessSetupAllocationAndReallocationName3 = "th_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema3 = new Schema166(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName3 }
);
var BusinessSetupAllocationAndReallocationModel3 = mongoClientApp166.model(businessSetupAllocationAndReallocationName3, BusinessSetupAllocationAndReallocationSchema3);
var businessSetupAllocationAndReallocation3 = BusinessSetupAllocationAndReallocationModel3;

// schemas/isoCode/th/partnerBusinessTransaction.server.ts
import { mongoClientApp as mongoClientApp167 } from "mongo-connection";
import { Schema as Schema167 } from "mongoose";
var businessTransactionName3 = "th_businessTransaction";
var BusinessTransactionSchema3 = new Schema167(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName3 }
);
var BusinessTransactionModel3 = mongoClientApp167.model(
  businessTransactionName3,
  BusinessTransactionSchema3
);
var businessTransaction3 = BusinessTransactionModel3;

// schemas/isoCode/th/partnerDirectory.server.ts
import { Schema as Schema168 } from "mongoose";
import { mongoClientApp as mongoClientApp168 } from "mongo-connection";
var partnerDirectoryName3 = "th_partnerDirectory";
var PartnerDirectorySchema3 = new Schema168(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName3 }
);
var PartnerDirectoryModel3 = mongoClientApp168.model(
  partnerDirectoryName3,
  PartnerDirectorySchema3
);
var partnerDirectory3 = PartnerDirectoryModel3;

// schemas/isoCode/th/partnerRequest.server.ts
import { Schema as Schema169 } from "mongoose";
import { mongoClientApp as mongoClientApp169 } from "mongo-connection";
var partnerRequestName3 = "th_partnerRequest";
var PartnerRequestSchema3 = new Schema169(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName3 }
);
var PartnerRequestModel3 = mongoClientApp169.model(
  partnerRequestName3,
  PartnerRequestSchema3
);
var partnerRequest3 = PartnerRequestModel3;

// schemas/isoCode/th/paymentToolKitTransaction.server.ts
import { mongoClientApp as mongoClientApp170 } from "mongo-connection";
import { Schema as Schema170 } from "mongoose";
var paymentToolKitTransactionName3 = "th_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema3 = new Schema170(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName3 }
);
var PaymentToolKitTransactionModel3 = mongoClientApp170.model(
  paymentToolKitTransactionName3,
  PaymentToolKitTransactionSchema3
);
var paymentToolKitTransaction3 = PaymentToolKitTransactionModel3;

// schemas/isoCode/th/promotionCode.server.ts
import { mongoClientApp as mongoClientApp171 } from "mongo-connection";
import { Schema as Schema171 } from "mongoose";
var promotionCodeName3 = "th_promotionCode";
var PromotionCodeSchema3 = new Schema171(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: Schema171.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName3 }
);
var PromotionCodeModel3 = mongoClientApp171.model(
  promotionCodeName3,
  PromotionCodeSchema3
);
var promotionCode3 = PromotionCodeModel3;

// schemas/isoCode/th/promotionHistory.server.ts
import { mongoClientApp as mongoClientApp172 } from "mongo-connection";
import { Schema as Schema172 } from "mongoose";
var promotionHistoryName3 = "th_promotionHistory";
var PromotionHistorySchema3 = new Schema172(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName3 }
);
var PromotionHistoryModel3 = mongoClientApp172.model(
  promotionHistoryName3,
  PromotionHistorySchema3
);
var promotionHistory3 = PromotionHistoryModel3;

// schemas/isoCode/th/promotionSource.server.ts
import { Schema as Schema173 } from "mongoose";
import { mongoClientApp as mongoClientApp173 } from "mongo-connection";
var promotionSourceName3 = "th_promotionSource";
var PromotionSourceSchema3 = new Schema173(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName3 }
);
var PromotionSourceModel3 = mongoClientApp173.model(
  promotionSourceName3,
  PromotionSourceSchema3
);
var promotionSource3 = PromotionSourceModel3;

// schemas/isoCode/th/rating.server.ts
import { mongoClientApp as mongoClientApp174 } from "mongo-connection";
import { Schema as Schema174 } from "mongoose";
var ratingName3 = "th_rating";
var RatingSchema3 = new Schema174(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName3 }
);
var RatingModel3 = mongoClientApp174.model(ratingName3, RatingSchema3);
var rating3 = RatingModel3;

// schemas/isoCode/th/referralCampaign.server.ts
import { mongoClientApp as mongoClientApp175 } from "mongo-connection";
import { Schema as Schema175 } from "mongoose";
var referralCampaignName3 = "th_askerReferralCampaign";
var ReferralCampaignSchema3 = new Schema175(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: Schema175.Types.Mixed, required: true },
    inviter: { $type: Schema175.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName3 }
);
var ReferralCampaignModel3 = mongoClientApp175.model(
  referralCampaignName3,
  ReferralCampaignSchema3
);
var referralCampaign3 = ReferralCampaignModel3;

// schemas/isoCode/th/service.server.ts
import { mongoClientApp as mongoClientApp176 } from "mongo-connection";
import { Schema as Schema176 } from "mongoose";
var serviceName3 = "th_service";
var ServiceSchema3 = new Schema176(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: Schema176.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: Schema176.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: Schema176.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName3 }
);
var ServiceModel3 = mongoClientApp176.model("th_service", ServiceSchema3);
var service3 = ServiceModel3;

// schemas/isoCode/th/serviceChannel.server.ts
import { mongoClientApp as mongoClientApp177 } from "mongo-connection";
import { Schema as Schema177 } from "mongoose";
var serviceChannelName3 = "th_serviceChannel";
var ServiceChannelSchema3 = new Schema177(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName3 }
);
var ServiceChannelModel3 = mongoClientApp177.model(
  "th_serviceChannel",
  ServiceChannelSchema3
);
var serviceChannel3 = ServiceChannelModel3;

// schemas/isoCode/th/settingCountry.server.ts
import { mongoClientApp as mongoClientApp178 } from "mongo-connection";
import { Schema as Schema178 } from "mongoose";
var settingCountryName3 = "th_settingCountry";
var SettingCountrySchema3 = new Schema178(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName3 }
);
var SettingCountryModel3 = mongoClientApp178.model(
  settingCountryName3,
  SettingCountrySchema3
);
var settingCountry3 = SettingCountryModel3;

// schemas/isoCode/th/settingSystem.server.ts
import { mongoClientApp as mongoClientApp179 } from "mongo-connection";
import { Schema as Schema179 } from "mongoose";
var settingSystemName3 = "th_settingSystem";
var SettingSystemSchema3 = new Schema179(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: Schema179.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName3 }
);
var SettingSystemModel3 = mongoClientApp179.model(
  "th_settingSystem",
  SettingSystemSchema3
);
var settingSystem3 = SettingSystemModel3;

// schemas/isoCode/th/subscription.server.ts
import { mongoClientApp as mongoClientApp180 } from "mongo-connection";
import { Schema as Schema180 } from "mongoose";
var subscriptionName3 = "th_subscription";
var SubscriptionSchema3 = new Schema180(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: Schema180.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: Schema180.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName3 }
);
var SubscriptionModel3 = mongoClientApp180.model(subscriptionName3, SubscriptionSchema3);
var subscription3 = SubscriptionModel3;

// schemas/isoCode/th/task.server.ts
import { mongoClientApp as mongoClientApp181 } from "mongo-connection";
import { Schema as Schema181 } from "mongoose";
var taskName3 = "th_task";
var TasksSchema3 = new Schema181(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema181.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema181.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema181.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema181.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: taskName3 }
);
var TasksModel3 = mongoClientApp181.model("th_tasks", TasksSchema3);
var task3 = TasksModel3;

// schemas/isoCode/th/taskerBNPLProcess.server.ts
import { mongoClientApp as mongoClientApp182 } from "mongo-connection";
import { Schema as Schema182 } from "mongoose";
var taskerBNPLProcessName3 = "th_taskerBNPLProcess";
var TaskerBNPLProcess3 = new Schema182(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName3 }
);
var TaskerBNPLProcessModel3 = mongoClientApp182.model(
  "th_taskerBNPLProcess",
  TaskerBNPLProcess3
);
var taskerBNPLProcess3 = TaskerBNPLProcessModel3;

// schemas/isoCode/th/taskerBNPLTransaction.server.ts
import { mongoClientApp as mongoClientApp183 } from "mongo-connection";
import { Schema as Schema183 } from "mongoose";
var taskerBNPLTransactionName3 = "th_taskerBNPLTransaction";
var TaskerBNPLTransaction3 = new Schema183(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName3 }
);
var TaskerBNPLTransactionModel3 = mongoClientApp183.model(
  taskerBNPLTransactionName3,
  TaskerBNPLTransaction3
);
var taskerBNPLTransaction3 = TaskerBNPLTransactionModel3;

// schemas/isoCode/th/taskerGift.server.ts
import { Schema as Schema184 } from "mongoose";
import { mongoClientApp as mongoClientApp184 } from "mongo-connection";
var taskerGiftName3 = "th_taskerGift";
var TaskerGiftSchema3 = new Schema184(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName3 }
);
var TaskerGiftModel3 = mongoClientApp184.model("th_taskerGift", TaskerGiftSchema3);
var taskerGift3 = TaskerGiftModel3;

// schemas/isoCode/th/taskerIncentive.server.ts
import { Schema as Schema185 } from "mongoose";
import { mongoClientApp as mongoClientApp185 } from "mongo-connection";
var taskerIncentiveName3 = "th_taskerIncentive";
var TaskerIncentiveSchema3 = new Schema185(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    giftInfo: {},
    social: {},
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: Schema185.Types.Mixed
    },
    status: {
      $type: String
    },
    codeList: Schema185.Types.Mixed,
    codeFromPartner: {
      $type: Number
    },
    office: Schema185.Types.Mixed,
    applyFor: Schema185.Types.Mixed,
    brandInfo: Schema185.Types.Mixed,
    redeemLink: Schema185.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName3 }
);
var TaskerIncentiveModel3 = mongoClientApp185.model(
  "th_taskerIncentive",
  TaskerIncentiveSchema3
);
var taskerIncentive3 = TaskerIncentiveModel3;

// schemas/isoCode/th/taskerOnboardingSetting.server.ts
import { mongoClientApp as mongoClientApp186 } from "mongo-connection";
import { Schema as Schema186 } from "mongoose";
var taskerOnboardingSettingName3 = "th_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema3 = new Schema186(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName3 }
);
var TaskerOnboardingSettingModel3 = mongoClientApp186.model(
  taskerOnboardingSettingName3,
  TaskerOnboardingSettingSchema3
);
var taskerOnboardingSetting3 = TaskerOnboardingSettingModel3;

// schemas/isoCode/th/taskerPointTransaction.server.ts
import { Schema as Schema187 } from "mongoose";
import { mongoClientApp as mongoClientApp187 } from "mongo-connection";
var taskerPointTransactionName3 = "th_taskerPointTransaction";
var TaskerPointTransactionSchema3 = new Schema187(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName3 }
);
var TaskerPointTransactionModel3 = mongoClientApp187.model(
  "th_taskerPointTransaction",
  TaskerPointTransactionSchema3
);
var taskerPointTransaction3 = TaskerPointTransactionModel3;

// schemas/isoCode/th/taskerProfile.server.ts
import { Schema as Schema188 } from "mongoose";
import { mongoClientApp as mongoClientApp188 } from "mongo-connection";
var taskerProfileName3 = "th_taskerProfile";
var TaskerProfileSchema3 = new Schema188(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName3
  }
);
var TaskerProfileModel3 = mongoClientApp188.model(
  "th_taskerProfile",
  TaskerProfileSchema3
);
var taskerProfile3 = TaskerProfileModel3;

// schemas/isoCode/th/taskerSpecialCampaign.server.ts
import { mongoClientApp as mongoClientApp189 } from "mongo-connection";
import { Schema as Schema189 } from "mongoose";
var taskerSpecialCampaignName3 = "th_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema3 = new Schema189(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: Schema189.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName3, _id: false }
);
var TaskerSpecialCampaignModel3 = mongoClientApp189.model(
  taskerSpecialCampaignName3,
  TaskerSpecialCampaignSchema3
);
var taskerSpecialCampaign3 = TaskerSpecialCampaignModel3;

// schemas/isoCode/th/taskerSpecialCampaignTransaction.server.ts
import { mongoClientApp as mongoClientApp190 } from "mongo-connection";
import { Schema as Schema190 } from "mongoose";
var taskerSpecialCampaignTransactionName3 = "th_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema3 = new Schema190(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName3 }
);
var TaskerSpecialCampaignTransactionModel3 = mongoClientApp190.model(
  taskerSpecialCampaignTransactionName3,
  TaskerSpecialCampaignTransactionSchema3
);
var taskerSpecialCampaignTransaction3 = TaskerSpecialCampaignTransactionModel3;

// schemas/isoCode/th/taskerToolkitLadingDetails.server.ts
import { Schema as Schema191 } from "mongoose";
import { mongoClientApp as mongoClientApp191 } from "mongo-connection";
var taskerToolkitLadingDetailsName3 = "th_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema3 = new Schema191(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName3 }
);
var TaskerToolkitLadingDetailsModel3 = mongoClientApp191.model(
  "th_taskerToolkitLadingDetails",
  TaskerToolkitLadingDetailsSchema3
);
var taskerToolkitLadingDetails3 = TaskerToolkitLadingDetailsModel3;

// schemas/isoCode/th/taskerTrainingCourse.server.ts
import { mongoClientApp as mongoClientApp192 } from "mongo-connection";
import { Schema as Schema192 } from "mongoose";
var taskerTrainingCourseName3 = "th_trainingTaskerCourse";
var CourseSchema3 = new Schema192(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName3 }
);
var CourseModel3 = mongoClientApp192.model(
  taskerTrainingCourseName3,
  CourseSchema3
);
var taskerTrainingCourse3 = CourseModel3;

// schemas/isoCode/th/taskerTrainingCourseStartDate.ts
import { mongoClientApp as mongoClientApp193 } from "mongo-connection";
import { Schema as Schema193 } from "mongoose";
var taskerTrainingCourseStartDateName3 = "th_trainingTaskerCourseStartDate";
var CourseStartDateSchema3 = new Schema193(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName3 }
);
var CourseStartDateModel3 = mongoClientApp193.model(
  taskerTrainingCourseStartDateName3,
  CourseStartDateSchema3
);
var taskerTrainingCourseStartDate3 = CourseStartDateModel3;

// schemas/isoCode/th/taskerTrainingQuiz.server.ts
import { Schema as Schema194 } from "mongoose";
import { mongoClientApp as mongoClientApp194 } from "mongo-connection";
var taskerTrainingQuizName3 = "th_trainingTaskerQuiz";
var QuizSchema3 = new Schema194(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName3 }
);
var QuizModel3 = mongoClientApp194.model(taskerTrainingQuizName3, QuizSchema3);
var taskerTrainingQuiz3 = QuizModel3;

// schemas/isoCode/th/taskerTrainingQuizCollection.server.ts
import { Schema as Schema195 } from "mongoose";
import { mongoClientApp as mongoClientApp195 } from "mongo-connection";
var taskerTrainingQuizCollectionName3 = "th_trainingTaskerQuizCollection";
var QuizCollectionSchema3 = new Schema195(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName3 }
);
var QuizCollectionModel3 = mongoClientApp195.model(
  taskerTrainingQuizCollectionName3,
  QuizCollectionSchema3
);
var taskerTrainingQuizCollection3 = QuizCollectionModel3;

// schemas/isoCode/th/taskerTrainingSubmission.server.ts
import { Schema as Schema196 } from "mongoose";
import { mongoClientApp as mongoClientApp196 } from "mongo-connection";
var taskerTrainingSubmissionName3 = "th_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema3 = new Schema196(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName3 }
);
var TaskerTrainingSubmissionModel3 = mongoClientApp196.model(
  taskerTrainingSubmissionName3,
  TaskerTrainingSubmissionSchema3
);
var taskerTrainingSubmission3 = TaskerTrainingSubmissionModel3;

// schemas/isoCode/th/thingsToKnow.server.ts
import { Schema as Schema197 } from "mongoose";
import { mongoClientApp as mongoClientApp197 } from "mongo-connection";
var thingsToKnowName3 = "th_thingsToKnow";
var ThingToKnowSchema3 = new Schema197(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName3 }
);
var ThingsToKnowModel3 = mongoClientApp197.model(
  "th_thingsToKnow",
  ThingToKnowSchema3
);
var thingsToKnow3 = ThingsToKnowModel3;

// schemas/isoCode/th/toolKitItems.server.ts
import { Schema as Schema198 } from "mongoose";
import { mongoClientApp as mongoClientApp198 } from "mongo-connection";
var toolKitItemsName3 = "th_toolKitItems";
var ToolKitItemsSchema3 = new Schema198(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName3 }
);
var ToolKitItemsModel3 = mongoClientApp198.model(
  "th_toolKitItems",
  ToolKitItemsSchema3
);
var toolKitItems3 = ToolKitItemsModel3;

// schemas/isoCode/th/toolKitSetting.server.ts
import { Schema as Schema199 } from "mongoose";
import { mongoClientApp as mongoClientApp199 } from "mongo-connection";
var toolKitSettingName3 = "th_toolKitSetting";
var ToolKitSettingSchema3 = new Schema199(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName3 }
);
var ToolKitSettingModel3 = mongoClientApp199.model(
  "th_toolKitSetting",
  ToolKitSettingSchema3
);
var toolKitSetting3 = ToolKitSettingModel3;

// schemas/isoCode/th/trainingJourney.server.ts
import { Schema as Schema200 } from "mongoose";
import { mongoClientApp as mongoClientApp200 } from "mongo-connection";
var trainingJourneyName3 = "th_trainingJourney";
var TrainingJourneySchema3 = new Schema200(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName3 }
);
var TrainingJourneyModel3 = mongoClientApp200.model(
  "th_trainingJourney",
  TrainingJourneySchema3
);
var trainingJourney3 = TrainingJourneyModel3;

// schemas/isoCode/th/trainingTasker.server.ts
import { Schema as Schema201 } from "mongoose";
import { mongoClientApp as mongoClientApp201 } from "mongo-connection";
var trainingTaskerName3 = "th_trainingTasker";
var TrainingTaskerSchema3 = new Schema201(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName3 }
);
var TrainingTaskerModel3 = mongoClientApp201.model(
  "th_trainingTasker",
  TrainingTaskerSchema3
);
var trainingTasker3 = TrainingTaskerModel3;

// schemas/isoCode/th/userActivation.server.ts
import { mongoClientApp as mongoClientApp202 } from "mongo-connection";
import { Schema as Schema202 } from "mongoose";
var userActivationName3 = "th_userActivation";
var UserActivationSchema3 = new Schema202(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName3 }
);
var UserActivationModel3 = mongoClientApp202.model(
  userActivationName3,
  UserActivationSchema3
);
var userActivation3 = UserActivationModel3;

// schemas/isoCode/th/userApp.server.ts
import { mongoClientApp as mongoClientApp203 } from "mongo-connection";
import { Schema as Schema203 } from "mongoose";
var usersName3 = "users";
var UsersAppSchema3 = new Schema203(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName3 }
);
var UsersAppModel3 = mongoClientApp203.model("th_users", UsersAppSchema3);
var users3 = UsersAppModel3;

// schemas/isoCode/th/userComboVoucher.server.ts
import { mongoClientApp as mongoClientApp204 } from "mongo-connection";
import { Schema as Schema204 } from "mongoose";
var userComboVoucherName3 = "th_userComboVoucher";
var UserComboVoucherSchema3 = new Schema204(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: Schema204.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: Schema204.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName3 }
);
var UserComboVoucherModel3 = mongoClientApp204.model(
  userComboVoucherName3,
  UserComboVoucherSchema3
);
var userComboVoucher3 = UserComboVoucherModel3;

// schemas/isoCode/th/userLocationHistory.server.ts
import { Schema as Schema205 } from "mongoose";
import { mongoClientApp as mongoClientApp205 } from "mongo-connection";
var userLocationHistoryName3 = "th_userLocationHistory";
var UserLocationHistorySchema3 = new Schema205(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName3 }
);
var UserLocationHistoryModel3 = mongoClientApp205.model(
  userLocationHistoryName3,
  UserLocationHistorySchema3
);
var userLocationHistory3 = UserLocationHistoryModel3;

// schemas/isoCode/th/userProfile.server.ts
import { mongoClientApp as mongoClientApp206 } from "mongo-connection";
import { Schema as Schema206 } from "mongoose";
var userProfileName3 = "th_userProfile";
var UserProfileSchema3 = new Schema206(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Schema206.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName3 }
);
var UserProfileModel3 = mongoClientApp206.model(userProfileName3, UserProfileSchema3);
var userProfile3 = UserProfileModel3;

// schemas/isoCode/th/workingPlaces.server.ts
import { Schema as Schema207 } from "mongoose";
import { mongoClientApp as mongoClientApp207 } from "mongo-connection";
var workingPlacesName3 = "th_workingPlaces";
var WorkingPlacesSchema3 = new Schema207(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName3 }
);
var WorkingPlacesModel3 = mongoClientApp207.model(
  "th_workingPlaces",
  WorkingPlacesSchema3
);
var workingPlaces3 = WorkingPlacesModel3;

// schemas/isoCode/th/index.server.ts
var TH = {
  task: task3,
  historyTasks: historyTasks3,
  userLocationHistory: userLocationHistory3,
  trainingTasker: trainingTasker3,
  trainingJourney: trainingJourney3,
  toolKitSetting: toolKitSetting3,
  toolKitItems: toolKitItems3,
  thingsToKnow: thingsToKnow3,
  taskerTrainingSubmission: taskerTrainingSubmission3,
  taskerTrainingQuizCollection: taskerTrainingQuizCollection3,
  taskerTrainingQuiz: taskerTrainingQuiz3,
  taskerTrainingCourse: taskerTrainingCourse3,
  taskerToolkitLadingDetails: taskerToolkitLadingDetails3,
  financialAccount: financialAccount3,
  taskerIncentive: taskerIncentive3,
  taskerPointTransaction: taskerPointTransaction3,
  taskerGift: taskerGift3,
  notification: notification3,
  taskerProfile: taskerProfile3,
  employeeProfile: employeeProfile3,
  workingPlaces: workingPlaces3,
  flashSale: flashSale3,
  incentive: incentive3,
  promotionCode: promotionCode3,
  promotionSource: promotionSource3,
  service: service3,
  promotionHistory: promotionHistory3,
  settingCountry: settingCountry3,
  comboVoucher: comboVoucher3,
  referralCampaign: referralCampaign3,
  marketingCampaign: marketingCampaign3,
  settingSystem: settingSystem3,
  partnerDirectory: partnerDirectory3,
  partnerRequest: partnerRequest3,
  communityComment: communityComment3,
  communityMedal: communityMedal3,
  communityNotification: communityNotification3,
  communityPost: communityPost3,
  communitySetting: communitySetting3,
  communityTag: communityTag3,
  communityUser: communityUser3,
  communityUserReport: communityUserReport3,
  subscription: subscription3,
  taskerOnboardingSetting: taskerOnboardingSetting3,
  serviceChannel: serviceChannel3,
  users: users3,
  bEmployee: bEmployee3,
  bEmployeeSetting: bEmployeeSetting3,
  FATransaction: FATransaction3,
  taskerBNPLTransaction: taskerBNPLTransaction3,
  taskerBNPLProcess: taskerBNPLProcess3,
  paymentToolKitTransaction: paymentToolKitTransaction3,
  rating: rating3,
  userActivation: userActivation3,
  business: business3,
  businessLevel: businessLevel3,
  businessMember: businessMember3,
  businessMemberTransaction: businessMemberTransaction3,
  businessTransaction: businessTransaction3,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocation3,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDate3,
  taskerSpecialCampaign: taskerSpecialCampaign3,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransaction3,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethod3,
  userProfile: userProfile3,
  communityTagOrder: communityTagOrder3,
  journeySetting: journeySetting3,
  userComboVoucher: userComboVoucher3,
  bundleVoucher: bundleVoucher3
};
var THName = {
  task: taskName3,
  historyTasks: historyTasksName3,
  userLocationHistory: userLocationHistoryName3,
  trainingTasker: trainingTaskerName3,
  trainingJourney: trainingJourneyName3,
  toolKitSetting: toolKitSettingName3,
  toolKitItems: toolKitItemsName3,
  thingsToKnow: thingsToKnowName3,
  taskerTrainingSubmission: taskerTrainingSubmissionName3,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName3,
  taskerTrainingQuiz: taskerTrainingQuizName3,
  taskerTrainingCourse: taskerTrainingCourseName3,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName3,
  taskerPointTransaction: taskerPointTransactionName3,
  taskerIncentive: taskerIncentiveName3,
  taskerGift: taskerGiftName3,
  notification: notificationName3,
  financialAccount: financialAccountName3,
  taskerProfile: taskerProfileName3,
  employeeProfile: employeeProfileName3,
  workingPlaces: workingPlacesName3,
  service: serviceName3,
  settingCountry: settingCountryName3,
  settingSystem: settingSystemName3,
  incentive: incentiveName3,
  communityComment: communityCommentName3,
  communityMedal: communityMedalName3,
  communityNotification: communityNotificationName3,
  communityPost: communityPostName3,
  communitySetting: communitySettingName3,
  communityTag: communityTagName3,
  communityUser: communityUserName3,
  communityUserReport: communityUserReportName3,
  subscription: subscriptionName3,
  taskerOnboardingSetting: taskerOnboardingSettingName3,
  users: usersName3,
  serviceChannel: serviceChannelName3,
  bEmployee: bEmployeeName3,
  bEmployeeSetting: bEmployeeSettingName3,
  FATransaction: FATransactionName3,
  taskerBNPLTransaction: taskerBNPLTransactionName3,
  taskerBNPLProcess: taskerBNPLProcessName3,
  paymentToolKitTransaction: paymentToolKitTransactionName3,
  promotionCode: promotionCodeName3,
  promotionHistory: promotionHistoryName3,
  marketingCampaign: marketingCampaignName3,
  rating: ratingName3,
  userActivation: userActivationName3,
  business: businessName3,
  businessMember: businessMemberName3,
  businessLevel: businessLevelName3,
  businessMemberTransaction: businessMemberTransactionName3,
  businessTransaction: businessTransactionName3,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName3,
  comboVoucher: comboVoucherName3,
  flashSale: flashSaleName3,
  partnerDirectory: partnerDirectoryName3,
  partnerRequest: partnerRequestName3,
  promotionSource: promotionSourceName3,
  referralCampaign: referralCampaignName3,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName3,
  taskerSpecialCampaign: taskerSpecialCampaignName3,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName3,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName3,
  userProfile: userProfileName3,
  communityTagOrder: communityTagOrderName3,
  journeySetting: journeySettingName3,
  userComboVoucher: userComboVoucherName3,
  bundleVoucher: bundleVoucherName3
};
var index_server_default3 = TH;

// schemas/isoCode/vn/bEmployee.server.ts
import { mongoClientApp as mongoClientApp208 } from "mongo-connection";
import { Schema as Schema208 } from "mongoose";
var bEmployeeName4 = "bEmployee";
var BEmployeeSchema4 = new Schema208(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    phone: { $type: String },
    isoCode: { $type: String },
    team: { $type: String },
    city: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    joinAt: { $type: Date },
    count: { $type: Number },
    quota: { $type: Number },
    level: { $type: String }
  },
  { typeKey: "$type", collection: bEmployeeName4 }
);
var BEmployeeModel4 = mongoClientApp208.model("vn_bEmployee", BEmployeeSchema4);
var bEmployee4 = BEmployeeModel4;

// schemas/isoCode/vn/bEmployeeSetting.server.ts
import { mongoClientApp as mongoClientApp209 } from "mongo-connection";
import { Schema as Schema209 } from "mongoose";
var bEmployeeSettingName4 = "vn_bEmployeeSetting";
var BEmployeeSettingSchema4 = new Schema209(
  {
    _id: { $type: String, required: true },
    level: { $type: [{ name: { $type: String }, quota: { $type: Number } }] }
  },
  { typeKey: "$type", collection: bEmployeeSettingName4 }
);
var BEmployeeSettingModel4 = mongoClientApp209.model(
  "vn_bEmployeeSetting",
  BEmployeeSettingSchema4
);
var bEmployeeSetting4 = BEmployeeSettingModel4;

// schemas/isoCode/vn/bundleVoucher.ts
import { mongoClientApp as mongoClientApp210 } from "mongo-connection";
import { Schema as Schema210 } from "mongoose";
var bundleVoucherName4 = "vn_bundleVoucher";
var BundleVoucherSchema4 = new Schema210(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    type: { $type: String, enum: ["SECRET_BOX"] },
    startDate: { $type: Date },
    endDate: { $type: Date },
    status: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    exchange: {
      by: { $type: String },
      totalPoint: { $type: Number },
      requiredPoint: { $type: Number }
    },
    isTesting: { $type: Boolean },
    vouchers: {
      $type: [
        {
          _id: false,
          incentiveId: { $type: String },
          title: { $type: Map, of: String },
          quantity: { $type: Number },
          image: { $type: String },
          stock: { $type: Number },
          type: { $type: String, required: true },
          originalPoint: { $type: Number }
        }
      ],
      default: void 0
    },
    rankRequire: { $type: Number },
    createdAt: { $type: Date, default: Date.now },
    createdBy: { $type: String },
    updatedAt: { $type: Date },
    updatedBy: { $type: String },
    askerTurn: { $type: Number },
    isUnlimitRedeem: { $type: Boolean },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema210.Types.Mixed },
            nextData: { $type: Schema210.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema210.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: bundleVoucherName4 }
);
var BundleVoucherModel4 = mongoClientApp210.model(
  bundleVoucherName4,
  BundleVoucherSchema4
);
var bundleVoucher4 = BundleVoucherModel4;

// schemas/isoCode/vn/campaign-payment.server.ts
import { mongoClientApp as mongoClientApp211 } from "mongo-connection";
import { Schema as Schema211 } from "mongoose";
var marketingCampaignPaymentMethodName4 = "vn_paymentMethodCampaign";
var MarketingCampaignPaymentMethodSchema4 = new Schema211(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    campaignName: { $type: String, required: true },
    description: { $type: Schema211.Types.Mixed, required: true },
    navigateTo: { $type: String, required: true },
    paymentMethod: { $type: String, required: true },
    status: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true }
  },
  { typeKey: "$type", collection: marketingCampaignPaymentMethodName4 }
);
var MarketingCampaignPaymentMethodModel4 = mongoClientApp211.model(
  marketingCampaignPaymentMethodName4,
  MarketingCampaignPaymentMethodSchema4
);
var marketingCampaignPaymentMethod4 = MarketingCampaignPaymentMethodModel4;

// schemas/isoCode/vn/campaign.server.ts
import { mongoClientApp as mongoClientApp212 } from "mongo-connection";
import { Schema as Schema212 } from "mongoose";
var marketingCampaignName4 = "vn_marketingCampaign";
var MarketingCampaignSchema4 = new Schema212(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    startDate: { $type: Date, required: true },
    endDate: { $type: Date, required: true },
    isTesting: { $type: Boolean },
    notShowApplyTime: { $type: Boolean },
    isSpecialCampaign: { $type: Boolean },
    status: { $type: String, required: true },
    type: { $type: String, required: true },
    applyForUser: { $type: Schema212.Types.Mixed },
    title: {
      $type: Map,
      of: String
    },
    description: {
      $type: Map,
      of: String
    },
    subDescription: {
      $type: Map,
      of: String
    },
    content: {
      $type: Map,
      of: String
    },
    subContent: {
      $type: Map,
      of: String
    },
    isApplyForAllService: { $type: Boolean },
    promotion: {
      code: { $type: String },
      serviceId: { $type: String },
      serviceText: { $type: Schema212.Types.Mixed }
    },
    primaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema212.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema212.Types.Mixed }
    },
    secondaryButtonConfig: {
      action: {
        navigateTo: { $type: String },
        url: { $type: String },
        params: { $type: Schema212.Types.Mixed },
        isRequireLogin: { $type: Boolean }
      },
      text: { $type: Schema212.Types.Mixed }
    },
    createdAt: { $type: Date, required: true },
    isoCode: { $type: String },
    isHidingOnHomePage: { $type: Boolean }
  },
  { typeKey: "$type", collection: marketingCampaignName4 }
);
var MarketingCampaignModel4 = mongoClientApp212.model(
  marketingCampaignName4,
  MarketingCampaignSchema4
);
var marketingCampaign4 = MarketingCampaignModel4;

// schemas/isoCode/vn/comboVoucher.server.ts
import { mongoClientApp as mongoClientApp213 } from "mongo-connection";
import { Schema as Schema213 } from "mongoose";
var comboVoucherName4 = "vn_comboVoucher";
var ComboVoucherSchema4 = new Schema213(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    thumbnail: { $type: String },
    isTesting: { $type: Boolean },
    cost: { $type: Number },
    price: { $type: Number },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    termsAndCondition: { $type: Map, of: String },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    status: { $type: String, required: true },
    target: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    currency: { sign: { $type: String }, code: { $type: String } },
    updatedAt: { $type: Date },
    vouchers: [
      {
        _id: false,
        image: { $type: String },
        title: { $type: Map, of: String },
        content: { $type: Map, of: String },
        note: { $type: Map, of: String },
        brandInfo: {
          image: { $type: String },
          name: { $type: String },
          text: { $type: Map, of: String }
        },
        quantity: { $type: Number },
        promotion: {
          prefix: { $type: String },
          type: { $type: String },
          value: { $type: Number },
          maxValue: { $type: Number },
          numberOfDayDueDate: { $type: Number },
          expiredDate: { $type: Date },
          isChosenExpiredDate: { $type: String }
        },
        applyFor: {
          cities: [{ $type: String }],
          districts: [{ $type: String }],
          services: [{ $type: String }],
          isAllUsers: { $type: Boolean },
          isSharePublic: { $type: Boolean }
        },
        from: { $type: String, enum: ["SYSTEM"] }
      }
    ],
    isoCode: { $type: String },
    userIds: { $type: [String], default: void 0 },
    isSubscription: { $type: Boolean },
    numberOfDayDueDate: { $type: Number },
    expiredDate: { $type: Date },
    usageInstruction: { $type: Map, of: String },
    allowedPaymentMethods: { $type: [String] },
    cancellationReasons: {
      $type: [
        {
          content: { $type: Map, of: String },
          isRequireAdditionalReason: { $type: Boolean }
        }
      ],
      default: void 0
    },
    isAllUsers: { $type: Boolean },
    isSharePublic: { $type: Boolean }
  },
  { typeKey: "$type", collection: comboVoucherName4 }
);
var ComboVoucherModel4 = mongoClientApp213.model(
  comboVoucherName4,
  ComboVoucherSchema4
);
var comboVoucher4 = ComboVoucherModel4;

// schemas/isoCode/vn/communityComment.server.ts
import { mongoClientApp as mongoClientApp214 } from "mongo-connection";
import { Schema as Schema214 } from "mongoose";
var communityCommentName4 = "vn_communityComment";
var CommunityCommentSchema4 = new Schema214(
  {
    _id: {
      $type: String,
      required: true
    },
    postId: {
      $type: String
    },
    userId: {
      $type: String
    },
    content: {
      $type: String
    },
    likeUserIds: { $type: [String] },
    status: {
      $type: String
    },
    hiddenByUserId: { $type: String },
    hiddenByUserName: { $type: String },
    mentions: [
      {
        _id: { $type: String },
        name: { $type: String }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    commentId: { $type: String },
    isReported: { $type: Boolean }
  },
  { typeKey: "$type", collection: communityCommentName4 }
);
var CommunityCommentModel4 = mongoClientApp214.model(
  communityCommentName4,
  CommunityCommentSchema4
);
var communityComment4 = CommunityCommentModel4;

// schemas/isoCode/vn/communityMedal.server.ts
import { mongoClientApp as mongoClientApp215 } from "mongo-connection";
import { Schema as Schema215 } from "mongoose";
var communityMedalName4 = "vn_communityMedal";
var CommunityMedalSchema4 = new Schema215(
  {
    _id: {
      $type: String,
      required: true
    },
    text: {
      $type: Schema215.Types.Mixed
    },
    icon: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    conditions: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          text: { $type: Schema215.Types.Mixed },
          operator: { $type: String, enum: ["lt", "gt", "lte", "gte", "eq"] },
          value: { $type: Number }
        }
      ],
      default: void 0
    },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"], default: "ACTIVE" },
    type: { $type: String, enum: ["LEVEL", "INTERACT"], default: "LEVEL" },
    tagIds: { $type: [String] },
    description: {
      $type: Schema215.Types.Mixed
    }
  },
  { typeKey: "$type", collection: communityMedalName4 }
);
var CommunityMedalModel4 = mongoClientApp215.model(
  communityMedalName4,
  CommunityMedalSchema4
);
var communityMedal4 = CommunityMedalModel4;

// schemas/isoCode/vn/communityNotification.server.ts
import { mongoClientApp as mongoClientApp216 } from "mongo-connection";
import { Schema as Schema216 } from "mongoose";
var communityNotificationName4 = "vn_communityNotification";
var CommunityNotificationSchema4 = new Schema216(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String
    },
    message: {
      $type: Schema216.Types.Mixed
    },
    isRead: { $type: Boolean, default: false },
    commendId: { $type: String },
    postId: {
      $type: String
    },
    navigateTo: {
      $type: String
    },
    icon: { $type: String },
    from: {
      userId: { $type: String },
      avatar: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityNotificationName4 }
);
var CommunityNotificationModel4 = mongoClientApp216.model(
  communityNotificationName4,
  CommunityNotificationSchema4
);
var communityNotification4 = CommunityNotificationModel4;

// schemas/isoCode/vn/communityOrderTag.server.ts
import { mongoClientApp as mongoClientApp217 } from "mongo-connection";
import { Schema as Schema217 } from "mongoose";
var communityTagOrderName4 = "vn_communityTagOrder";
var CommunityTagOrderSchema4 = new Schema217(
  {
    _id: {
      $type: String,
      required: true
    },
    name: { $type: String },
    status: { $type: String },
    text: { $type: Schema217.Types.Mixed },
    startDate: { $type: Date },
    endDate: { $type: Date },
    tagOrderBox: {
      $type: [{
        tagId: { $type: String },
        order: { $type: Number }
      }]
    },
    createdAt: { $type: Date },
    changeHistories: { $type: Schema217.Types.Mixed },
    updatedAt: { $type: Date },
    createdBy: { $type: String },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: communityTagOrderName4 }
);
var CommunityTagOrderModel4 = mongoClientApp217.model(
  communityTagOrderName4,
  CommunityTagOrderSchema4
);
var communityTagOrder4 = CommunityTagOrderModel4;

// schemas/isoCode/vn/communityPost.server.ts
import { mongoClientApp as mongoClientApp218 } from "mongo-connection";
import { Schema as Schema218 } from "mongoose";
var communityPostName4 = "vn_communityPost";
var CommunityPostSchema4 = new Schema218(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    content: {
      $type: String,
      required: true
    },
    likeUserIds: { $type: [String] },
    commentIds: { $type: [String] },
    sharedUserIds: { $type: [String] },
    sharedByPostId: { $type: String },
    numberOfLikes: { $type: Number },
    numberOfShares: { $type: Number },
    numberOfComments: { $type: Number },
    tagIds: { $type: [String] },
    status: {
      $type: String
    },
    images: [
      {
        _id: { $type: String },
        imageUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    videos: [
      {
        _id: { $type: String },
        videoUrl: { $type: String },
        thumbnailUrl: { $type: String }
      }
    ],
    location: {
      longitude: { $type: Number },
      latitude: { $type: Number }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    support: {
      userId: { $type: String },
      username: { $type: String }
    },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        content: { $type: Schema218.Types.Mixed }
      }
    ],
    isReported: { $type: Boolean },
    isPinned: { $type: Boolean, default: false }
  },
  { typeKey: "$type", collection: communityPostName4 }
);
var CommunityPostModel4 = mongoClientApp218.model(
  communityPostName4,
  CommunityPostSchema4
);
var communityPost4 = CommunityPostModel4;

// schemas/isoCode/vn/communityTag.server.ts
import { mongoClientApp as mongoClientApp219 } from "mongo-connection";
import { Schema as Schema219 } from "mongoose";
var communityTagName4 = "vn_communityTag";
var CommunityTagSchema4 = new Schema219(
  {
    _id: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    text: {
      $type: Schema219.Types.Mixed
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    startDate: { $type: Date, default: void 0 },
    endDate: { $type: Date, default: void 0 },
    displayPosition: { $type: [String] },
    frame: { $type: String, default: void 0 },
    isDefault: { $type: Boolean },
    isAdminOnly: { $type: Boolean },
    changeHistories: [
      {
        key: { $type: String },
        createdBy: { $type: String },
        content: { $type: Schema219.Types.Mixed },
        createdByUserName: { $type: String },
        createdAt: { $type: Date, default: Date.now }
      }
    ]
  },
  { typeKey: "$type", collection: communityTagName4 }
);
var CommunityTagModel4 = mongoClientApp219.model(
  communityTagName4,
  CommunityTagSchema4
);
var communityTag4 = CommunityTagModel4;

// schemas/isoCode/vn/communityUser.server.ts
import { mongoClientApp as mongoClientApp220 } from "mongo-connection";
import { Schema as Schema220 } from "mongoose";
var communityUserName4 = "vn_communityUser";
var CommunityUserSchema4 = new Schema220(
  {
    _id: {
      $type: String,
      required: true
    },
    numberOfLikes: { $type: Number },
    numberOfPosts: { $type: Number },
    numberOfShares: { $type: Number },
    // TODO: I'd defined this field to serve product's requirement because it's not now. Must confirm again.
    numberOfAskerViews: { $type: Number },
    name: {
      $type: String
    },
    bio: { $type: String },
    avatar: { $type: String },
    medals: [
      {
        _id: { $type: String },
        receivedAt: { $type: Date },
        isUsed: { $type: Boolean }
      }
    ],
    status: { $type: String },
    following: {
      $type: [String]
    },
    followers: {
      $type: [String]
    },
    favouriteTagIds: {
      $type: [String]
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    isAdmin: { $type: Boolean },
    isReported: { $type: Boolean },
    changeHistories: [
      {
        _id: { $type: String },
        createdAt: { $type: Date, default: Date.now },
        createdBy: { $type: String },
        key: { $type: String },
        reason: { $type: String },
        createdByUserName: { $type: String }
      }
    ],
    isVerifiedProfile: { type: Boolean }
  },
  { typeKey: "$type", collection: communityUserName4 }
);
var CommunityUserModel4 = mongoClientApp220.model(
  communityUserName4,
  CommunityUserSchema4
);
var communityUser4 = CommunityUserModel4;

// schemas/isoCode/vn/communitySetting.server.ts
import { mongoClientApp as mongoClientApp221 } from "mongo-connection";
import { Schema as Schema221 } from "mongoose";
var communitySettingName4 = "vn_communitySetting";
var CommunitySettingSchema4 = new Schema221(
  {
    _id: {
      $type: String,
      required: true
    },
    isTesting: {
      $type: Boolean,
      required: true
    },
    tester: {
      $type: [String],
      required: true
    },
    isBTaskeeHelpEnabled: {
      $type: Boolean,
      required: true
    },
    postThemes: {
      $type: [
        {
          _id: {
            $type: Schema221.Types.ObjectId,
            required: true
          },
          themeImageUrl: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    },
    isDisablePostNews: {
      $type: Boolean,
      required: true
    },
    postPinned: {
      $type: [String],
      required: false,
      default: []
    },
    termAndConditions: {
      $type: [
        {
          vi: {
            $type: String,
            required: true
          },
          en: {
            $type: String,
            required: true
          },
          ko: {
            $type: String,
            required: true
          },
          th: {
            $type: String,
            required: true
          },
          id: {
            $type: String,
            required: true
          },
          ms: {
            $type: String,
            required: true
          }
        }
      ],
      required: true
    }
  },
  {
    typeKey: "$type",
    collection: communitySettingName4
  }
);
var CommunitySettingModel4 = mongoClientApp221.model(
  communitySettingName4,
  CommunitySettingSchema4
);
var communitySetting4 = CommunitySettingModel4;

// schemas/isoCode/vn/communityUserReport.server.ts
import { mongoClientApp as mongoClientApp222 } from "mongo-connection";
import { Schema as Schema222 } from "mongoose";
var communityUserReportName4 = "vn_communityUserReport";
var CommunityUserReportSchema4 = new Schema222(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    reportedUserId: { $type: String },
    status: {
      $type: String
    },
    commentId: {
      $type: String
    },
    reason: {
      name: { $type: String },
      content: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    profileId: {
      $type: String
    },
    postId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: communityUserReportName4 }
);
var CommunityUserReportModel4 = mongoClientApp222.model(
  communityUserReportName4,
  CommunityUserReportSchema4
);
var communityUserReport4 = CommunityUserReportModel4;

// schemas/isoCode/vn/employeeProfile.server.ts
import { Schema as Schema223 } from "mongoose";
import { mongoClientApp as mongoClientApp223 } from "mongo-connection";
var employeeProfileName4 = "vn_employeeProfile";
var EmployeeProfileSchema4 = new Schema223(
  {
    _id: {
      $type: String,
      required: true
    },
    idNumber: {
      $type: String,
      required: true
    },
    companyId: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    gender: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    portrait: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date }
      }
    ]
  },
  { typeKey: "$type", collection: employeeProfileName4 }
);
var EmployeeProfileModel4 = mongoClientApp223.model(
  "vn_employeeProfile",
  EmployeeProfileSchema4
);
var employeeProfile4 = EmployeeProfileModel4;

// schemas/isoCode/vn/FATransaction.server.ts
import { mongoClientApp as mongoClientApp224 } from "mongo-connection";
import { Schema as Schema224 } from "mongoose";
var FATransactionName4 = "FATransaction";
var FATransactionSchema4 = new Schema224(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    accountType: { $type: String },
    type: { $type: String },
    source: {
      name: { $type: String },
      value: { $type: String },
      reference: { $type: String },
      cashierId: { $type: String },
      cashierName: { $type: String },
      subscriptionCode: { $type: String },
      BNPLTransactionId: { $type: String }
    },
    amount: { $type: Number },
    date: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: FATransactionName4 }
);
var FATransactionModel4 = mongoClientApp224.model(
  "vn_FATransaction",
  FATransactionSchema4
);
var FATransaction4 = FATransactionModel4;

// schemas/isoCode/vn/financialAccount.server.ts
import { Schema as Schema225 } from "mongoose";
import { mongoClientApp as mongoClientApp225 } from "mongo-connection";
var financialAccountName4 = "vn_financialAccount";
var FinancialAccountSchema4 = new Schema225(
  {
    _id: {
      $type: String,
      required: true
    },
    FMainAccount: {
      $type: Number
    },
    TH_FMainAccount: { $type: Number },
    ID_FMainAccount: { $type: Number },
    Promotion: { $type: Number },
    TH_Promotion: { $type: Number },
    ID_Promotion: { $type: Number },
    updatedAt: { $type: Date },
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: financialAccountName4 }
);
var FinancialAccountModel4 = mongoClientApp225.model(
  "vn_financialAccount",
  FinancialAccountSchema4
);
var financialAccount4 = FinancialAccountModel4;

// schemas/isoCode/vn/flashSale.server.ts
import { mongoClientApp as mongoClientApp226 } from "mongo-connection";
import { Schema as Schema226 } from "mongoose";
var flashSaleName4 = "vn_askerFlashSaleIncentive";
var FlashSaleSchema4 = new Schema226(
  {
    _id: { $type: String, required: true },
    description: { $type: String },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    incentiveInfos: [
      {
        _id: { $type: String, required: true },
        image: { $type: String, required: true },
        title: {
          $type: Map,
          of: String
        },
        point: { $type: Number, required: true },
        originalPoint: { $type: Number, required: true }
      }
    ],
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: flashSaleName4 }
);
var FlashSaleModel4 = mongoClientApp226.model(
  flashSaleName4,
  FlashSaleSchema4
);
var flashSale4 = FlashSaleModel4;

// schemas/isoCode/vn/historyTasks.server.ts
import { mongoClientApp as mongoClientApp227 } from "mongo-connection";
import { Schema as Schema227 } from "mongoose";
var historyTasksName4 = "history_tasks";
var HistoryTasksSchema4 = new Schema227(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema227.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema227.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema227.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema227.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: historyTasksName4 }
);
var HistoryTasksModel4 = mongoClientApp227.model("vn_history_tasks", HistoryTasksSchema4);
var historyTasks4 = HistoryTasksModel4;

// schemas/isoCode/vn/incentive.server.ts
import { mongoClientApp as mongoClientApp228 } from "mongo-connection";
import { Schema as Schema228 } from "mongoose";
var incentiveName4 = "vn_incentive";
var IncentiveSchema4 = new Schema228(
  {
    _id: { $type: String, required: true },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    exchange: { by: { $type: String }, point: { $type: Number } },
    status: { $type: String, required: true },
    isTesting: { $type: Boolean },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    image: { $type: String, required: true },
    from: { $type: String },
    categoryName: { $type: String },
    createdBy: { $type: String },
    promotion: {
      expiredDate: { $type: Date },
      numberOfDayDueDate: { $type: Number },
      type: String,
      maxValue: { $type: Number },
      value: { $type: Number }
    },
    rankRequire: { $type: Number },
    applyFor: {
      service: { $type: [{ $type: String }], default: void 0 },
      cities: { $type: [{ $type: String }], default: void 0 },
      isAllUsers: { $type: Boolean },
      isSharePublic: { $type: Boolean },
      districts: { $type: [{ $type: String }], default: void 0 }
    },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    type: String,
    createdAt: {
      $type: Date,
      default: Date.now
    },
    codeList: {
      $type: [
        {
          code: { $type: String },
          isUsed: { $type: Boolean, default: false }
        }
      ],
      default: void 0
    },
    codeFromPartner: {
      code: { $type: String },
      limit: { $type: Number },
      numberOfUsed: { $type: Number }
    },
    giftInfo: {
      id: { $type: String },
      cat_id: { $type: String },
      gift_id: { $type: String },
      price: { $type: Number }
    },
    isSpecialIncentive: { $type: Boolean },
    social: {
      hotline: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String },
      email: { $type: String },
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String }
    },
    isRedeemOneTime: { $type: Boolean },
    redeemLink: { $type: String },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ],
      default: void 0
    },
    updatedAt: { $type: Date },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: incentiveName4 }
);
var IncentiveModel4 = mongoClientApp228.model(incentiveName4, IncentiveSchema4);
var incentive4 = IncentiveModel4;

// schemas/isoCode/vn/journeySetting.server.ts
import { mongoClientApp as mongoClientApp229 } from "mongo-connection";
import { Schema as Schema229 } from "mongoose";
var journeySettingName4 = "vn_journeySetting";
var JourneySettingSchema4 = new Schema229(
  {
    _id: { $type: String, required: true },
    status: { $type: String },
    cityName: { $type: String },
    levels: {
      $type: [
        {
          _id: false,
          name: { $type: String },
          title: { $type: Map, of: String },
          text: { $type: Map, of: String },
          icon: { $type: String }
        }
      ],
      default: void 0
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: journeySettingName4 }
);
var JourneySettingModel4 = mongoClientApp229.model(
  journeySettingName4,
  JourneySettingSchema4
);
var journeySetting4 = JourneySettingModel4;

// schemas/isoCode/vn/notification.server.ts
import { Schema as Schema230 } from "mongoose";
import { mongoClientApp as mongoClientApp230 } from "mongo-connection";
var notificationName4 = "vn_notification";
var NotificationSchema4 = new Schema230(
  {
    _id: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    taskId: {
      $type: String
    },
    title: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    isForce: {
      $type: Boolean
    },
    type: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: notificationName4 }
);
var NotificationModel4 = mongoClientApp230.model(
  "vn_notification",
  NotificationSchema4
);
var notification4 = NotificationModel4;

// schemas/isoCode/vn/partnerBusiness.server.ts
import { mongoClientApp as mongoClientApp231 } from "mongo-connection";
import { Schema as Schema231 } from "mongoose";
var businessName4 = "vn_business";
var BusinessSchema4 = new Schema231(
  {
    _id: { $type: String, required: true },
    // AskerId
    name: { $type: String, required: true },
    taxCode: { $type: String, required: true },
    email: { $type: String, required: true },
    address: { $type: String },
    sector: {
      $type: String,
      required: true,
      enum: [
        "E_COMMERCE",
        "INFORMATION_TECHNOLOGY",
        "SERVICE_FINANCE",
        "TRAVEL_TOURISM",
        "EDUCATION_TRAINING",
        "FOOD_BEVERAGE",
        "MANUFACTURING",
        "HEALTHCARE_SERVICE",
        "OTHER"
      ]
    },
    businessSize: {
      $type: String,
      required: true,
      enum: ["UNDER_50", "UNDER_250", "UNDER_500", "UNDER_1000"]
    },
    businessLicense: [{ name: { $type: String }, url: { $type: String } }],
    // Array of licenses
    status: {
      $type: String,
      required: true,
      enum: ["REGISTERED", "VERIFYING", "REJECTED", "BLOCKED", "ACTIVE"]
    },
    reason: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    bPay: { $type: Number },
    changeHistories: {
      $type: [
        {
          _id: false,
          key: { $type: String, required: true },
          actionFrom: { $type: String, required: true },
          // APP | BE
          userId: { $type: String, required: true },
          username: { $type: String },
          dataChange: {
            _id: false,
            currentData: { $type: Schema231.Types.Mixed },
            nextData: { $type: Schema231.Types.Mixed }
          },
          createdAt: { $type: Date, required: true },
          dataExtend: { $type: Schema231.Types.Mixed }
          // Dữ liệu bổ sung khác, hiện tại không có field này
        }
      ],
      default: void 0
    },
    topUpSetting: {
      //Added a getter function that ensures the values are stored as floating-point numbers with 2 decimal places
      period: { $type: Number, get: (v) => parseFloat(v.toFixed(2)) },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    revokeSetting: {
      period: { $type: Number, get: (v) => parseFloat(v.toFixed(2)) },
      nextTime: { $type: Date },
      dayInMonth: { $type: String },
      isDisable: { $type: Boolean },
      status: {
        $type: String,
        enum: ["ACTIVE", "INACTIVE"]
      }
    },
    notes: {
      $type: [
        {
          _id: false,
          note: { $type: String },
          noteBy: { $type: String },
          noteAt: { $type: Date }
        }
      ],
      default: void 0
    }
  },
  { typeKey: "$type", collection: businessName4 }
);
var BusinessModel4 = mongoClientApp231.model(businessName4, BusinessSchema4);
var business4 = BusinessModel4;

// schemas/isoCode/vn/partnerBusinessLevel.server.ts
import { Schema as Schema232 } from "mongoose";
import { mongoClientApp as mongoClientApp232 } from "mongo-connection";
var businessLevelName4 = "vn_businessLevel";
var BusinessLevelSchema4 = new Schema232(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    name: { $type: String, required: true },
    amount: { $type: Number, required: true },
    status: {
      $type: String,
      enum: ["ACTIVE", "DELETED"],
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ],
    updatedAt: { $type: Date },
    updatedBy: { $type: String }
  },
  { typeKey: "$type", collection: businessLevelName4 }
);
var BusinessLevelModel4 = mongoClientApp232.model(
  businessLevelName4,
  BusinessLevelSchema4
);
var businessLevel4 = BusinessLevelModel4;

// schemas/isoCode/vn/partnerBusinessMember.server.ts
import { Schema as Schema233 } from "mongoose";
import { mongoClientApp as mongoClientApp233 } from "mongo-connection";
var businessMemberName4 = "vn_businessMember";
var BusinessMemberSchema4 = new Schema233(
  {
    _id: { $type: String, required: true },
    // Unique identifier
    businessId: { $type: String, required: true },
    // Reference to Business
    userId: { $type: String, required: true },
    // Reference to User
    levelId: { $type: String },
    // Role or level in the business
    phone: { $type: String },
    // Phone number
    bPay: { $type: Number },
    // Payment or salary (float64)
    status: {
      $type: String,
      required: true,
      enum: ["ACTIVE", "INACTIVE"]
    },
    createdAt: { $type: Date, default: Date.now },
    changeHistories: [
      {
        changedBy: { $type: String, required: true },
        // User who made the change
        changeDate: { $type: Date, required: true },
        // Date of the change
        changes: { $type: String }
        // Description of what changed
      }
    ]
  },
  { typeKey: "$type", collection: businessMemberName4 }
);
var BusinessMemberModel4 = mongoClientApp233.model(
  businessMemberName4,
  BusinessMemberSchema4
);
var businessMember4 = BusinessMemberModel4;

// schemas/isoCode/vn/partnerBusinessMemberTransaction.server.ts
import { mongoClientApp as mongoClientApp234 } from "mongo-connection";
import { Schema as Schema234 } from "mongoose";
var businessMemberTransactionName4 = "vn_businessMemberTransaction";
var BusinessMemberTransactionSchema4 = new Schema234(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String, required: true },
    taskId: { $type: String, required: true },
    memberId: { $type: String, required: true },
    userId: { $type: String, required: true },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now }
  },
  { typeKey: "$type", collection: businessMemberTransactionName4 }
);
var BusinessMemberTransactionModel4 = mongoClientApp234.model(businessMemberTransactionName4, BusinessMemberTransactionSchema4);
var businessMemberTransaction4 = BusinessMemberTransactionModel4;

// schemas/isoCode/vn/partnerBusinessSetupAllocateAndReallocate.server.ts
import { mongoClientApp as mongoClientApp235 } from "mongo-connection";
import { Schema as Schema235 } from "mongoose";
var businessSetupAllocationAndReallocationName4 = "vn_businessSetupAllocateAndReallocate";
var BusinessSetupAllocationAndReallocationSchema4 = new Schema235(
  {
    _id: { $type: String, required: true },
    businessId: { $type: String, required: true },
    allocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    },
    reallocate: {
      type: { $type: String, required: true },
      freq: { $type: Number },
      date: { $type: String },
      otherDate: { $type: Date },
      status: { $type: String }
    }
  },
  { typeKey: "$type", collection: businessSetupAllocationAndReallocationName4 }
);
var BusinessSetupAllocationAndReallocationModel4 = mongoClientApp235.model(businessSetupAllocationAndReallocationName4, BusinessSetupAllocationAndReallocationSchema4);
var businessSetupAllocationAndReallocation4 = BusinessSetupAllocationAndReallocationModel4;

// schemas/isoCode/vn/partnerBusinessTransaction.server.ts
import { mongoClientApp as mongoClientApp236 } from "mongo-connection";
import { Schema as Schema236 } from "mongoose";
var businessTransactionName4 = "vn_businessTransaction";
var BusinessTransactionSchema4 = new Schema236(
  {
    _id: { $type: String, required: true },
    type: { $type: String, required: true },
    amount: { $type: Number, required: true },
    name: { $type: String, required: true },
    reason: { $type: String },
    memberId: { $type: String },
    businessId: { $type: String, required: true },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    createdAt: { $type: Date, default: Date.now },
    payment: {
      method: { $type: String },
      referenceCode: { $type: String },
      invoice: { $type: String }
    },
    requester: { $type: String },
    createdBy: { $type: String }
  },
  { typeKey: "$type", collection: businessTransactionName4 }
);
var BusinessTransactionModel4 = mongoClientApp236.model(
  businessTransactionName4,
  BusinessTransactionSchema4
);
var businessTransaction4 = BusinessTransactionModel4;

// schemas/isoCode/vn/partnerDirectory.server.ts
import { Schema as Schema237 } from "mongoose";
import { mongoClientApp as mongoClientApp237 } from "mongo-connection";
var partnerDirectoryName4 = "vn_partnerDirectory";
var PartnerDirectorySchema4 = new Schema237(
  {
    _id: { $type: String, required: true },
    country: { $type: String },
    isoCode: { $type: String },
    logo: { $type: String, required: true },
    city: [String],
    name: { $type: String, required: true },
    description: { $type: String },
    headquarters: { $type: String },
    website: { $type: String },
    email: { $type: String },
    hotline: { $type: String },
    storeAddresses: [String],
    socialLinks: {
      facebook: { $type: String },
      instagram: { $type: String },
      tiktok: { $type: String },
      youtube: { $type: String }
    },
    applicationLinks: {
      playStore: { $type: String },
      appStore: { $type: String }
    },
    level: { $type: String },
    industry: { $type: String, required: true },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String },
      position: { $type: String },
      note: { $type: String }
    },
    createdAt: {
      $type: Date
    },
    updatedAt: {
      $type: Date
    }
  },
  { typeKey: "$type", collection: partnerDirectoryName4 }
);
var PartnerDirectoryModel4 = mongoClientApp237.model(
  partnerDirectoryName4,
  PartnerDirectorySchema4
);
var partnerDirectory4 = PartnerDirectoryModel4;

// schemas/isoCode/vn/partnerRequest.server.ts
import { Schema as Schema238 } from "mongoose";
import { mongoClientApp as mongoClientApp238 } from "mongo-connection";
var partnerRequestName4 = "vn_partnerRequest";
var PartnerRequestSchema4 = new Schema238(
  {
    _id: { $type: String, required: true },
    source: { $type: String, required: true },
    status: { $type: String, required: true },
    note: { $type: String },
    companyInformation: {
      name: { $type: String, required: true },
      industry: { $type: String, required: true },
      city: [String]
    },
    contactInformation: {
      contact: { $type: String },
      email: { $type: String },
      phoneNumber: { $type: String }
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: partnerRequestName4 }
);
var PartnerRequestModel4 = mongoClientApp238.model(
  partnerRequestName4,
  PartnerRequestSchema4
);
var partnerRequest4 = PartnerRequestModel4;

// schemas/isoCode/vn/paymentToolKitTransaction.server.ts
import { mongoClientApp as mongoClientApp239 } from "mongo-connection";
import { Schema as Schema239 } from "mongoose";
var paymentToolKitTransactionName4 = "vn_paymentToolKitTransaction";
var PaymentToolKitTransactionSchema4 = new Schema239(
  {
    _id: {
      $type: String,
      required: true
    },
    amount: { $type: Number },
    taskerId: { $type: String },
    type: { $type: String },
    payment: {
      transactionId: { $type: String },
      method: { $type: String },
      status: { $type: String }
    },
    listTool: [
      {
        _id: { $type: String },
        image: { $type: String },
        price: { $type: Number },
        text: { $type: Map, of: String },
        quantity: { $type: Number },
        createdAt: { $type: Date }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: paymentToolKitTransactionName4 }
);
var PaymentToolKitTransactionModel4 = mongoClientApp239.model(
  paymentToolKitTransactionName4,
  PaymentToolKitTransactionSchema4
);
var paymentToolKitTransaction4 = PaymentToolKitTransactionModel4;

// schemas/isoCode/vn/promotionCode.server.ts
import { mongoClientApp as mongoClientApp240 } from "mongo-connection";
import { Schema as Schema240 } from "mongoose";
var promotionCodeName4 = "vn_promotionCode";
var PromotionCodeSchema4 = new Schema240(
  {
    _id: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    limit: {
      $type: Number,
      required: true
    },
    serviceIds: { $type: [String] },
    isoCode: { $type: String, required: true },
    value: {
      type: { $type: String },
      value: { $type: Number },
      maxValue: { $type: Number }
    },
    taskPlace: { $type: Schema240.Types.Mixed, required: true },
    typeOfPromotion: {
      $type: String,
      required: true
    },
    source: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    description: { $type: String },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    taskStartDate: { $type: Date },
    taskEndDate: { $type: Date },
    userIds: [String],
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean },
    isFirstBooking: { $type: Boolean },
    promotionBy: { $type: String },
    minOrderValue: { $type: Number },
    numberOfUsed: { $type: Number },
    status: { $type: String },
    series: { $type: String },
    number: { $type: Number },
    prefix: { $type: String },
    createdBy: { $type: String },
    locked: { $type: Boolean },
    paymentMethods: {
      $type: [String],
      default: void 0
    },
    hourRanges: {
      $type: [
        {
          from: { $type: String },
          to: { $type: String },
          weekDays: { $type: [Number], default: void 0 },
          _id: false
        }
      ],
      default: void 0
    },
    target: { $type: String, enum: ["BOTH", "ASKER", "TASKER"] },
    coordinate: {
      address: { $type: String },
      lat: { $type: Number },
      lng: { $type: Number },
      radius: { $type: Number }
    }
  },
  { typeKey: "$type", collection: promotionCodeName4 }
);
var PromotionCodeModel4 = mongoClientApp240.model(
  promotionCodeName4,
  PromotionCodeSchema4
);
var promotionCode4 = PromotionCodeModel4;

// schemas/isoCode/vn/promotionHistory.server.ts
import { mongoClientApp as mongoClientApp241 } from "mongo-connection";
import { Schema as Schema241 } from "mongoose";
var promotionHistoryName4 = "vn_promotionHistory";
var PromotionHistorySchema4 = new Schema241(
  {
    _id: {
      $type: String,
      required: true
    },
    promotionCode: {
      $type: String,
      required: true
    },
    userId: {
      $type: String,
      required: true
    },
    phone: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    date: {
      $type: Date,
      required: true
    },
    value: {
      $type: Number,
      required: true
    },
    createdAt: {
      $type: Number,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionHistoryName4 }
);
var PromotionHistoryModel4 = mongoClientApp241.model(
  promotionHistoryName4,
  PromotionHistorySchema4
);
var promotionHistory4 = PromotionHistoryModel4;

// schemas/isoCode/vn/promotionSource.server.ts
import { Schema as Schema242 } from "mongoose";
import { mongoClientApp as mongoClientApp242 } from "mongo-connection";
var promotionSourceName4 = "vn_promotionSource";
var PromotionSourceSchema4 = new Schema242(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    description: {
      $type: String,
      required: true
    },
    status: {
      $type: String
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    createdBy: {
      $type: String,
      required: true
    }
  },
  { typeKey: "$type", collection: promotionSourceName4 }
);
var PromotionSourceModel4 = mongoClientApp242.model(
  promotionSourceName4,
  PromotionSourceSchema4
);
var promotionSource4 = PromotionSourceModel4;

// schemas/isoCode/vn/rating.server.ts
import { mongoClientApp as mongoClientApp243 } from "mongo-connection";
import { Schema as Schema243 } from "mongoose";
var ratingName4 = "vn_rating";
var RatingSchema4 = new Schema243(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, default: Date.now },
    askerId: { $type: String },
    taskerId: { $type: String },
    taskId: { $type: String },
    rate: { $type: Number },
    review: { $type: String },
    badges: [String]
  },
  { typeKey: "$type", collection: ratingName4 }
);
var RatingModel4 = mongoClientApp243.model(ratingName4, RatingSchema4);
var rating4 = RatingModel4;

// schemas/isoCode/vn/referralCampaign.server.ts
import { mongoClientApp as mongoClientApp244 } from "mongo-connection";
import { Schema as Schema244 } from "mongoose";
var referralCampaignName4 = "vn_askerReferralCampaign";
var ReferralCampaignSchema4 = new Schema244(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      }
    },
    endDate: {
      $type: Date,
      required: true,
      default: () => {
        const today = /* @__PURE__ */ new Date();
        today.setHours(23, 59, 59, 999);
        return today;
      }
    },
    invitee: { $type: Schema244.Types.Mixed, required: true },
    inviter: { $type: Schema244.Types.Mixed, required: true },
    description: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    isTesting: {
      $type: Boolean
    },
    status: {
      $type: String
    },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: referralCampaignName4 }
);
var ReferralCampaignModel4 = mongoClientApp244.model(
  referralCampaignName4,
  ReferralCampaignSchema4
);
var referralCampaign4 = ReferralCampaignModel4;

// schemas/isoCode/vn/service.server.ts
import { mongoClientApp as mongoClientApp245 } from "mongo-connection";
import { Schema as Schema245 } from "mongoose";
var serviceName4 = "service";
var ServiceSchema4 = new Schema245(
  {
    _id: {
      $type: String,
      required: true
    },
    icon: {
      $type: String,
      required: true
    },
    text: { $type: Schema245.Types.Mixed },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String,
      required: true
    },
    discountByDuration: {
      $type: Array
    },
    discountByDoneTask: {
      $type: Array
    },
    city: {
      $type: Array
    },
    onlyShowTasker: {
      $type: Boolean
    },
    shortText: { $type: Schema245.Types.Mixed },
    isSubscription: {
      $type: Boolean
    },
    detailService: { $type: Schema245.Types.Mixed }
  },
  { typeKey: "$type", collection: serviceName4 }
);
var ServiceModel4 = mongoClientApp245.model("vn_service", ServiceSchema4);
var service4 = ServiceModel4;

// schemas/isoCode/vn/serviceChannel.server.ts
import { mongoClientApp as mongoClientApp246 } from "mongo-connection";
import { Schema as Schema246 } from "mongoose";
var serviceChannelName4 = "vn_serviceChannel";
var ServiceChannelSchema4 = new Schema246(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerList: { $type: [String] },
    serviceId: {
      $type: String
    }
  },
  { typeKey: "$type", collection: serviceChannelName4 }
);
var ServiceChannelModel4 = mongoClientApp246.model(
  "vn_serviceChannel",
  ServiceChannelSchema4
);
var serviceChannel4 = ServiceChannelModel4;

// schemas/isoCode/vn/settingCountry.server.ts
import { mongoClientApp as mongoClientApp247 } from "mongo-connection";
import { Schema as Schema247 } from "mongoose";
var settingCountryName4 = "vn_settingCountry";
var SettingCountrySchema4 = new Schema247(
  {
    _id: { $type: String, required: true },
    isoCode: { $type: String },
    text: { $type: String },
    countryCode: { $type: String },
    countryName: { $type: String },
    locale: { $type: String },
    currency: {
      sign: { $type: String },
      code: { $type: String }
    },
    city: [
      {
        name: { $type: String, required: true },
        key: { $type: String },
        status: { $type: String },
        district: [
          {
            key: { $type: String },
            name: { $type: String }
          }
        ]
      }
    ],
    paymentMethods: {
      $type: Map,
      of: [
        {
          _id: false,
          name: { $type: String, required: true },
          status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
          minVersion: { $type: String, required: true },
          notApplyForServices: { $type: [String], default: void 0 },
          isTesting: { $type: Boolean }
        }
      ]
    }
  },
  { typeKey: "$type", collection: settingCountryName4 }
);
var SettingCountryModel4 = mongoClientApp247.model(
  settingCountryName4,
  SettingCountrySchema4
);
var settingCountry4 = SettingCountryModel4;

// schemas/isoCode/vn/settingSystem.server.ts
import { mongoClientApp as mongoClientApp248 } from "mongo-connection";
import { Schema as Schema248 } from "mongoose";
var settingSystemName4 = "settingSystem";
var SettingSystemSchema4 = new Schema248(
  {
    _id: { $type: String, required: true },
    giftSetting: {
      category: [
        {
          id: { $type: Number },
          name: { $type: String },
          text: {
            $type: Map,
            of: String
          },
          icon: { $type: String },
          iconForV3: { $type: String, required: true },
          status: { $type: String },
          weight: { $type: Number }
        }
      ]
    },
    minCost: { $type: Number },
    supportPhone: { $type: String },
    smsProvider: { $type: String },
    referralValue: { $type: Number },
    taskerSignUpPromotion: { $type: Number },
    costForChooseTasker: { $type: Number },
    notificationRadiusSmall: { $type: Number },
    notificationRadiusBig: { $type: Number },
    timeInBetweenTask: { $type: Number },
    defaultPostTaskTime: { $type: Number },
    minPostTaskTime: { $type: Number },
    emergencyTime: { $type: Number },
    feeForEmergencyTask: { $type: Number },
    feeForWeekend: { $type: Number },
    surgePriceTime: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          rate: { $type: Number }
        }
      ]
    },
    postTaskMinHours: { $type: Number },
    postTaskMaxHours: { $type: Number },
    minuteAskerCancelTask: { $type: Number },
    limitNumberCancelTask: { $type: Number },
    limitDaysCancelTask: { $type: Number },
    minFAccountTasker: { $type: Number },
    limitNumberAcceptTaskInDay: { $type: Number },
    listOfToolsForTasker: { $type: String },
    citiesApplyDistance: { $type: [String] },
    requireEmail: { $type: Boolean },
    defaultDuration: { $type: Number },
    arraySuggestTimePostTask: {
      $type: [
        {
          start: { $type: Number },
          end: { $type: Number },
          time: {
            $type: Schema248.Types.Mixed
          }
        }
      ]
    },
    feeWeekendApplyForCity: { $type: [String] },
    topTaskersPriorityScore: { $type: Number },
    defaultTaskTime: { $type: Number },
    tester: { $type: [String] },
    versionAsker: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    versionApp: {
      ios: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      },
      android: {
        version: { $type: String },
        link: { $type: String },
        description: { $type: String },
        isShow: { $type: Boolean },
        isForce: { $type: Boolean },
        forcedVersion: { $type: String },
        startDate: { $type: Date }
      }
    },
    submitionAddressForTasker: [
      {
        city: { $type: String },
        address: { $type: String },
        phoneNumber: { $type: String },
        name: { $type: String },
        workingStartDate: { $type: Date },
        workingEndDate: { $type: Date },
        status: { $type: String },
        allowedTaskerGender: { $type: String },
        createdAt: { $type: Date },
        createdBy: { $type: String },
        workingDays: { $type: [Number] },
        offDays: [
          {
            _id: { $type: String },
            from: { $type: Date },
            to: { $type: Date },
            reason: { $type: String },
            isHoliday: { $type: Boolean },
            isActive: { $type: Boolean },
            createdAt: { $type: Date },
            createdBy: { $type: String }
          }
        ]
      }
    ]
  },
  { typeKey: "$type", collection: settingSystemName4 }
);
var SettingSystemModel4 = mongoClientApp248.model(
  "vn_settingSystem",
  SettingSystemSchema4
);
var settingSystem4 = SettingSystemModel4;

// schemas/isoCode/vn/subscription.server.ts
import { mongoClientApp as mongoClientApp249 } from "mongo-connection";
import { Schema as Schema249 } from "mongoose";
var subscriptionName4 = "vn_subscription";
var SubscriptionSchema4 = new Schema249(
  {
    _id: {
      $type: String,
      required: true
    },
    startDate: { $type: Date },
    endDate: {
      $type: Date
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    address: { $type: String },
    location: {
      lat: { $type: Number },
      lng: { $type: Number }
    },
    schedule: {
      $type: Schema249.Types.Mixed
    },
    taskIds: {
      $type: [String]
    },
    serviceId: { $type: String },
    userId: { $type: String },
    month: { $type: Number },
    serviceText: {
      $type: Schema249.Types.Mixed
    },
    contactName: { $type: String },
    phone: { $type: String },
    status: { $type: String },
    createdAt: { $type: Date },
    orderId: { $type: String },
    weekday: { $type: [Number] },
    isoCode: { $type: String },
    price: { $type: Number },
    description: { $type: String },
    autoChooseTasker: { $type: Boolean },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      session: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      pricing: {
        $type: [
          {
            date: { $type: Date },
            duration: { $type: Number },
            costDetail: {
              baseCost: { $type: Number },
              cost: { $type: Number },
              finalCost: { $type: Number },
              duration: { $type: Number },
              date: { $type: Date },
              decreasedReasons: {
                $type: [{
                  key: { $type: String },
                  value: { $type: Number }
                }]
              },
              transportFee: { $type: Number },
              depositMoney: { $type: Number },
              newFinalCost: { $type: Number }
            }
          }
        ]
      }
    },
    createdBy: { $type: String },
    createFrom: { $type: String },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          reason: { $type: String },
          refundMoney: { $type: Number }
        }
      ]
    }
  },
  { typeKey: "$type", collection: subscriptionName4 }
);
var SubscriptionModel4 = mongoClientApp249.model(subscriptionName4, SubscriptionSchema4);
var subscription4 = SubscriptionModel4;

// schemas/isoCode/vn/task.server.ts
import { mongoClientApp as mongoClientApp250 } from "mongo-connection";
import { Schema as Schema250 } from "mongoose";
var taskName4 = "vn_task";
var TasksSchema4 = new Schema250(
  {
    _id: {
      $type: String,
      required: true
    },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: {
      $type: Date
    },
    status: {
      $type: String,
      required: true
    },
    taskPlace: {
      country: { $type: String },
      city: { $type: String },
      district: { $type: String }
    },
    phone: { $type: String },
    contactName: { $type: String },
    lat: { $type: Number },
    lng: { $type: Number },
    date: { $type: Date },
    currency: { $type: String },
    address: { $type: String },
    houseNumber: { $type: String },
    payment: {
      method: { $type: String }
    },
    description: { $type: String },
    rated: { $type: Boolean },
    taskerRated: { $type: Boolean },
    isoCode: { $type: String },
    taskNote: { $type: String },
    originCurrency: {
      sign: { $type: String },
      code: { $type: String }
    },
    shortAddress: { $type: String },
    pricing: {
      updatedAt: { $type: Date },
      costDetail: {
        baseCost: { $type: Number },
        cost: { $type: Number },
        finalCost: { $type: Number },
        reason: { $type: Schema250.Types.Mixed },
        currency: { $type: String },
        isIncrease: { $type: Boolean }
      }
    },
    changesHistory: {
      $type: [
        {
          key: { $type: String },
          from: { $type: String },
          user: { $type: String },
          createdAt: { $type: Date },
          createdBy: { $type: String },
          content: { $type: Schema250.Types.Mixed }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    askerId: { $type: String },
    cost: { $type: Number },
    serviceText: { $type: Schema250.Types.Mixed },
    serviceId: { $type: String },
    costDetail: {
      baseCost: { $type: Number },
      cost: { $type: Number },
      finalCost: { $type: Number },
      increaseReasons: { $type: Schema250.Types.Mixed },
      duration: { $type: Number },
      currency: {
        sign: { $type: String },
        code: { $type: String }
      },
      isIncrease: { $type: Boolean },
      transportFee: { $type: Number },
      depositMoney: { $type: Number },
      newFinalCost: { $type: Number },
      decreasedReasons: {
        $type: [{
          type: { $type: String },
          value: { $type: Number },
          promotionBy: { $type: String }
        }]
      }
    },
    serviceName: { $type: String },
    duration: { $type: Number },
    acceptedTasker: {
      $type: [
        {
          taskerId: { $type: String },
          name: { $type: String },
          avatar: { $type: String },
          avgRating: { $type: Number },
          taskDone: { $type: Number },
          acceptedAt: { $type: Date },
          isPremiumTasker: { $type: Boolean }
        }
      ]
    },
    requirements: {
      $type: [
        {
          cost: { $type: Number }
        }
      ]
    },
    newCostDetail: {
      cost: { $type: Number }
    },
    promotion: {
      promotionValue: { $type: Number }
    }
  },
  { typeKey: "$type", collection: taskName4 }
);
var TasksModel4 = mongoClientApp250.model("vn_tasks", TasksSchema4);
var task4 = TasksModel4;

// schemas/isoCode/vn/taskerBNPLProcess.server.ts
import { mongoClientApp as mongoClientApp251 } from "mongo-connection";
import { Schema as Schema251 } from "mongoose";
var taskerBNPLProcessName4 = "vn_taskerBNPLProcess";
var TaskerBNPLProcess4 = new Schema251(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    status: { $type: String },
    amount: { $type: Number },
    remainingAmount: { $type: Number },
    moneyBNPLOnTask: { $type: Number },
    expiredAt: { $type: Date },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number },
    doneAt: { $type: Date },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: taskerBNPLProcessName4 }
);
var TaskerBNPLProcessModel4 = mongoClientApp251.model(
  "vn_taskerBNPLProcess",
  TaskerBNPLProcess4
);
var taskerBNPLProcess4 = TaskerBNPLProcessModel4;

// schemas/isoCode/vn/taskerBNPLTransaction.server.ts
import { mongoClientApp as mongoClientApp252 } from "mongo-connection";
import { Schema as Schema252 } from "mongoose";
var taskerBNPLTransactionName4 = "vn_taskerBNPLTransaction";
var TaskerBNPLTransaction4 = new Schema252(
  {
    _id: { $type: String, required: true },
    taskerId: { $type: String },
    BNPLId: { $type: String },
    source: {
      name: { $type: String },
      taskId: { $type: String },
      reason: { $type: String }
    },
    amount: { $type: Number },
    type: { $type: String },
    accountType: { $type: String },
    createdAt: { $type: Date },
    firstPayMoney: { $type: Number }
  },
  { typeKey: "$type", collection: taskerBNPLTransactionName4 }
);
var TaskerBNPLTransactionModel4 = mongoClientApp252.model(
  taskerBNPLTransactionName4,
  TaskerBNPLTransaction4
);
var taskerBNPLTransaction4 = TaskerBNPLTransactionModel4;

// schemas/isoCode/vn/taskerGift.server.ts
import { Schema as Schema253 } from "mongoose";
import { mongoClientApp as mongoClientApp253 } from "mongo-connection";
var taskerGiftName4 = "vn_taskerGift";
var TaskerGiftSchema4 = new Schema253(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    source: { $type: String },
    title: { $type: Map, of: String },
    content: { $type: Map, of: String },
    note: { $type: Map, of: String },
    image: { $type: String },
    brandInfo: {
      name: { $type: String },
      text: { $type: Map, of: String },
      image: { $type: String }
    },
    promotionCode: { $type: String },
    expired: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    reference: {
      journeySettingId: { $type: String },
      level: { $type: String },
      from: { $type: String }
    },
    office: {
      $type: [
        {
          name: { $type: String },
          text: { $type: Map, of: String }
        }
      ]
    },
    social: {
      facebook: { $type: String },
      instagram: { $type: String },
      ios: { $type: String },
      android: { $type: String },
      website: { $type: String },
      email: { $type: String },
      hotline: { $type: String }
    },
    used: { $type: Boolean },
    usedAt: { $type: Date },
    responsiblePerson: { $type: String },
    pointTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerGiftName4 }
);
var TaskerGiftModel4 = mongoClientApp253.model("vn_taskerGift", TaskerGiftSchema4);
var taskerGift4 = TaskerGiftModel4;

// schemas/isoCode/vn/taskerIncentive.server.ts
import { Schema as Schema254 } from "mongoose";
import { mongoClientApp as mongoClientApp254 } from "mongo-connection";
var taskerIncentiveName4 = "vn_taskerIncentive";
var TaskerIncentiveSchema4 = new Schema254(
  {
    _id: {
      $type: String,
      required: true
    },
    image: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    from: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    createdAt: {
      $type: Date,
      required: true
    },
    createdBy: {
      $type: String,
      required: true
    },
    isoCode: {
      $type: String,
      required: true
    },
    categoryName: {
      $type: String,
      required: true
    },
    exchange: {
      by: {
        $type: String,
        required: true
      },
      point: {
        $type: Number,
        required: true
      }
    },
    title: {
      $type: Map,
      of: String
    },
    content: {
      $type: String
    },
    note: {
      $type: String
    },
    isSpecialIncentive: {
      $type: Boolean
    },
    promotion: {
      type: {
        $type: String
      },
      maxValue: {
        $type: Number
      },
      value: {
        $type: Number
      },
      prefix: {
        $type: String
      },
      limit: {
        $type: Number
      },
      rankRequire: {
        $type: Number
      }
    },
    partner: {
      $type: String
    },
    giftInfo: {},
    social: {},
    isTesting: {
      $type: Boolean
    },
    isRedeemOneTime: {
      $type: Boolean
    },
    originalPoint: {
      $type: Schema254.Types.Mixed
    },
    status: {
      $type: String
    },
    codeList: Schema254.Types.Mixed,
    codeFromPartner: {
      $type: Number
    },
    office: Schema254.Types.Mixed,
    applyFor: Schema254.Types.Mixed,
    brandInfo: Schema254.Types.Mixed,
    redeemLink: Schema254.Types.Mixed
  },
  { typeKey: "$type", collection: taskerIncentiveName4 }
);
var TaskerIncentiveModel4 = mongoClientApp254.model(
  "vn_taskerIncentive",
  TaskerIncentiveSchema4
);
var taskerIncentive4 = TaskerIncentiveModel4;

// schemas/isoCode/vn/taskerOnboardingSetting.server.ts
import { mongoClientApp as mongoClientApp255 } from "mongo-connection";
import { Schema as Schema255 } from "mongoose";
var taskerOnboardingSettingName4 = "vn_taskerOnboardingSetting";
var TaskerOnboardingSettingSchema4 = new Schema255(
  {
    _id: { $type: String, required: true },
    reasons: [
      {
        _id: { $type: String, required: true },
        type: {
          $type: String,
          enum: [
            "NEEDS_UPDATE",
            "FAIL_UPDATED",
            "ELIMINATED",
            "FAIL_CALLING",
            "FAIL_INTERVIEW",
            "REJECTED",
            "REJECT_DOCUMENTS",
            "RESTORED"
          ]
        },
        name: { $type: String, required: true },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: { $type: Date },
        createdBy: { $type: String },
        updatedBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerOnboardingSettingName4 }
);
var TaskerOnboardingSettingModel4 = mongoClientApp255.model(
  taskerOnboardingSettingName4,
  TaskerOnboardingSettingSchema4
);
var taskerOnboardingSetting4 = TaskerOnboardingSettingModel4;

// schemas/isoCode/vn/taskerPointTransaction.server.ts
import { Schema as Schema256 } from "mongoose";
import { mongoClientApp as mongoClientApp256 } from "mongo-connection";
var taskerPointTransactionName4 = "vn_taskerPointTransaction";
var TaskerPointTransactionSchema4 = new Schema256(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    referenceId: { $type: String },
    source: {
      name: { $type: String },
      text: { $type: String },
      taskId: { $type: String }
    },
    point: { $type: Number },
    oldPoint: { $type: Number },
    type: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    isoCode: { $type: String }
  },
  { typeKey: "$type", collection: taskerPointTransactionName4 }
);
var TaskerPointTransactionModel4 = mongoClientApp256.model(
  "vn_taskerPointTransaction",
  TaskerPointTransactionSchema4
);
var taskerPointTransaction4 = TaskerPointTransactionModel4;

// schemas/isoCode/vn/taskerProfile.server.ts
import { Schema as Schema257 } from "mongoose";
import { mongoClientApp as mongoClientApp257 } from "mongo-connection";
var taskerProfileName4 = "taskerProfile";
var TaskerProfileSchema4 = new Schema257(
  {
    _id: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true,
      ref: "Users"
    },
    taskerPhone: {
      $type: String,
      required: true
    },
    taskerIdNumber: {
      $type: String,
      required: true
    },
    identityCard: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    dob: { $type: Date },
    household: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    isPartner: {
      $type: Boolean
    },
    criminalRecords: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    massagePracticeCertificate: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    curriculumVitae: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    confirmationConduct: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    passport: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    houseElectricBill: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    workPermit: {
      images: {
        $type: Array
      },
      status: {
        $type: String
      },
      actionHistories: [
        {
          userId: { $type: String },
          reason: { $type: String },
          newStatus: { $type: String },
          updatedByUsername: { $type: String },
          createdAt: { $type: Date }
        }
      ]
    },
    taskerName: {
      $type: String,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdAt: { $type: Date, required: true },
    appointmentInfo: {
      address: {
        $type: String,
        required: true
      },
      city: {
        $type: String,
        required: true
      },
      name: {
        $type: String,
        required: true
      },
      phoneNumber: {
        $type: String,
        required: true
      },
      date: {
        $type: Date,
        required: true
      }
    },
    status: {
      $type: String
    },
    actionHistories: [
      {
        userId: { $type: String },
        oldStatus: { $type: String },
        newStatus: { $type: String },
        updatedByUsername: { $type: String },
        reason: { $type: String },
        createdAt: { $type: Date }
      }
    ],
    notes: [
      {
        updatedByUserId: { $type: String },
        notedBy: { $type: String },
        description: { $type: String },
        notedAt: { $type: Date },
        updatedAt: { $type: Date }
      }
    ],
    processStatus: {
      $type: String
    }
  },
  {
    typeKey: "$type",
    collection: taskerProfileName4
  }
);
var TaskerProfileModel4 = mongoClientApp257.model(
  "vn_taskerProfile",
  TaskerProfileSchema4
);
var taskerProfile4 = TaskerProfileModel4;

// schemas/isoCode/vn/taskerSpecialCampaign.server.ts
import { mongoClientApp as mongoClientApp258 } from "mongo-connection";
import { Schema as Schema258 } from "mongoose";
var taskerSpecialCampaignName4 = "vn_taskerSpecialCampaign";
var TaskerSpecialCampaignSchema4 = new Schema258(
  {
    _id: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    image: {
      imageUrl: { $type: String },
      thumbnailUrl: { $type: String }
    },
    type: {
      $type: String,
      enum: ["REFERRAL_CAMPAIGN", "TASK_CAMPAIGN"]
    },
    status: {
      $type: String,
      enum: ["ACTIVE", "INACTIVE"]
    },
    value: {
      $type: Number
    },
    startDate: {
      $type: Date,
      required: true
    },
    endDate: {
      $type: Date,
      required: true
    },
    text: { $type: Schema258.Types.Mixed },
    rewards: [
      {
        type: { $type: String, enum: ["BPOINT", "MONEY", "PROMOTION"] },
        amount: { $type: Number },
        applyAccountType: { $type: String, enum: ["M", "P"] },
        taskerJourneyLevels: { $type: [String], default: void 0 },
        minRateTask: { $type: Number },
        applyForServices: { $type: [String], default: void 0 }
      }
    ],
    createdAt: {
      $type: Date,
      default: Date.now
    },
    updatedAt: {
      $type: Date
    },
    createdBy: {
      $type: String
    },
    updatedBy: {
      $type: String
    },
    city: {
      $type: [String],
      default: void 0
    },
    changeHistories: [
      {
        key: { $type: String },
        content: { $type: Map, of: String },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ]
  },
  { typeKey: "$type", collection: taskerSpecialCampaignName4, _id: false }
);
var TaskerSpecialCampaignModel4 = mongoClientApp258.model(
  taskerSpecialCampaignName4,
  TaskerSpecialCampaignSchema4
);
var taskerSpecialCampaign4 = TaskerSpecialCampaignModel4;

// schemas/isoCode/vn/taskerSpecialCampaignTransaction.server.ts
import { mongoClientApp as mongoClientApp259 } from "mongo-connection";
import { Schema as Schema259 } from "mongoose";
var taskerSpecialCampaignTransactionName4 = "vn_taskerSpecialCampaignTransaction";
var TaskerSpecialCampaignTransactionSchema4 = new Schema259(
  {
    _id: {
      $type: String,
      required: true
    },
    phone: {
      $type: String
    },
    taskerId: {
      $type: String
    },
    campaignId: {
      $type: String
    },
    campaignName: {
      $type: String
    },
    status: {
      $type: String,
      enum: ["IN-PROGRESS", "COMPLETED", "REWARDED"]
    },
    completedDate: {
      $type: Date
    },
    rewardedDate: {
      $type: Date
    },
    createdAt: {
      $type: Date,
      default: Date.now
    }
  },
  { typeKey: "$type", collection: taskerSpecialCampaignTransactionName4 }
);
var TaskerSpecialCampaignTransactionModel4 = mongoClientApp259.model(
  taskerSpecialCampaignTransactionName4,
  TaskerSpecialCampaignTransactionSchema4
);
var taskerSpecialCampaignTransaction4 = TaskerSpecialCampaignTransactionModel4;

// schemas/isoCode/vn/taskerToolkitLadingDetails.server.ts
import { Schema as Schema260 } from "mongoose";
import { mongoClientApp as mongoClientApp260 } from "mongo-connection";
var taskerToolkitLadingDetailsName4 = "vn_taskerToolkitLadingDetails";
var TaskerToolkitLadingDetailsSchema4 = new Schema260(
  {
    _id: { $type: String, required: true },
    dateOfLading: { $type: Date },
    typeOfLading: { $type: String },
    placeOfReceipt: { $type: String },
    billOfLading: { $type: String },
    domesticRouting: { $type: String },
    taskerId: { $type: String },
    createdBy: { $type: String },
    isReceived: { $type: Boolean },
    receivedAt: { $type: Date },
    amount: { $type: Number },
    toolKitTransactionId: { $type: String }
  },
  { typeKey: "$type", collection: taskerToolkitLadingDetailsName4 }
);
var TaskerToolkitLadingDetailsModel4 = mongoClientApp260.model(
  "vn_taskerToolkitLadingDetails",
  TaskerToolkitLadingDetailsSchema4
);
var taskerToolkitLadingDetails4 = TaskerToolkitLadingDetailsModel4;

// schemas/isoCode/vn/taskerTrainingCourse.server.ts
import { mongoClientApp as mongoClientApp261 } from "mongo-connection";
import { Schema as Schema261 } from "mongoose";
var taskerTrainingCourseName4 = "vn_trainingTaskerCourse";
var CourseSchema4 = new Schema261(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    type: {
      $type: String,
      required: true
    },
    timeToCompleteByMinutes: {
      $type: Number
    },
    maximumNumberOfRetries: {
      $type: Number
    },
    percentageToPass: {
      $type: Number
    },
    deadlineIn: {
      $type: Number
    },
    isDisplayAllAnswer: {
      $type: Boolean
    },
    status: {
      $type: String,
      required: true
    },
    relatedServices: [
      {
        _id: {
          $type: String
        },
        name: {
          $type: String
        }
      }
    ],
    quizCollections: [
      {
        _id: {
          $type: String
        },
        order: {
          $type: Number
        }
      }
    ],
    condition: {
      coursesMustBeCompleted: {
        courseIds: { $type: [String], default: void 0 }
      },
      byTasker: {
        minimumStar: {
          $type: Number
        }
      },
      manuallyUnblock: {
        taskerIdsAllowed: { $type: [String], default: void 0 }
      }
    },
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    },
    displayPosition: {
      $type: String
    },
    cities: {
      $type: [String],
      default: void 0
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseName4 }
);
var CourseModel4 = mongoClientApp261.model(
  taskerTrainingCourseName4,
  CourseSchema4
);
var taskerTrainingCourse4 = CourseModel4;

// schemas/isoCode/vn/taskerTrainingCourseStartDate.ts
import { mongoClientApp as mongoClientApp262 } from "mongo-connection";
import { Schema as Schema262 } from "mongoose";
var taskerTrainingCourseStartDateName4 = "vn_trainingTaskerCourseStartDate";
var CourseStartDateSchema4 = new Schema262(
  {
    _id: {
      $type: String,
      required: true
    },
    courseId: {
      $type: String,
      required: true
    },
    taskerId: {
      $type: String,
      required: true
    },
    startDate: {
      $type: Date
    },
    isUnblocked: {
      $type: Boolean
    },
    deadlineIn: {
      $type: Number
    }
  },
  { typeKey: "$type", collection: taskerTrainingCourseStartDateName4 }
);
var CourseStartDateModel4 = mongoClientApp262.model(
  taskerTrainingCourseStartDateName4,
  CourseStartDateSchema4
);
var taskerTrainingCourseStartDate4 = CourseStartDateModel4;

// schemas/isoCode/vn/taskerTrainingQuiz.server.ts
import { Schema as Schema263 } from "mongoose";
import { mongoClientApp as mongoClientApp263 } from "mongo-connection";
var taskerTrainingQuizName4 = "vn_trainingTaskerQuiz";
var QuizSchema4 = new Schema263(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    isRandomAnswer: {
      $type: Boolean
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    answers: [
      {
        _id: { $type: String },
        content: { $type: String },
        isCorrect: { $type: Boolean }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizName4 }
);
var QuizModel4 = mongoClientApp263.model(taskerTrainingQuizName4, QuizSchema4);
var taskerTrainingQuiz4 = QuizModel4;

// schemas/isoCode/vn/taskerTrainingQuizCollection.server.ts
import { Schema as Schema264 } from "mongoose";
import { mongoClientApp as mongoClientApp264 } from "mongo-connection";
var taskerTrainingQuizCollectionName4 = "vn_trainingTaskerQuizCollection";
var QuizCollectionSchema4 = new Schema264(
  {
    _id: {
      $type: String,
      required: true
    },
    title: {
      $type: String,
      required: true
    },
    code: {
      $type: String,
      required: true
    },
    description: {
      $type: String
    },
    timeToCompleteByMinutes: {
      $type: Number,
      required: true
    },
    numberOfDisplayQuizzes: {
      $type: Number,
      required: true
    },
    isRandomQuizzes: {
      $type: Boolean,
      default: false
    },
    image: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    video: {
      url: {
        $type: String
      },
      description: {
        $type: String
      }
    },
    quizzes: [
      {
        _id: { $type: String },
        order: { $type: Number }
      }
    ],
    createdAt: {
      $type: Date,
      required: true
    },
    updatedAt: {
      $type: Date
    },
    createdByUserId: {
      $type: String,
      required: true
    },
    createdByUsername: {
      $type: String,
      required: true
    },
    updatedByUserId: {
      $type: String
    },
    updatedByUsername: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: taskerTrainingQuizCollectionName4 }
);
var QuizCollectionModel4 = mongoClientApp264.model(
  taskerTrainingQuizCollectionName4,
  QuizCollectionSchema4
);
var taskerTrainingQuizCollection4 = QuizCollectionModel4;

// schemas/isoCode/vn/taskerTrainingSubmission.server.ts
import { Schema as Schema265 } from "mongoose";
import { mongoClientApp as mongoClientApp265 } from "mongo-connection";
var taskerTrainingSubmissionName4 = "vn_trainingTaskerSubmission";
var TaskerTrainingSubmissionSchema4 = new Schema265(
  {
    _id: {
      $type: String,
      required: true
    },
    status: { $type: String },
    duration: { $type: Number },
    taskerId: { $type: String },
    displayPosition: { $type: String },
    numberOfSubmissions: { $type: Number },
    course: {
      _id: { $type: String },
      title: { $type: String },
      code: { $type: String },
      type: { $type: String },
      timeToCompleteByMinutes: { $type: Number },
      maximumNumberOfRetries: { $type: Number },
      percentageToPass: { $type: Number },
      deadline: { $type: Number },
      quizCollections: [
        {
          _id: { $type: String },
          title: { $type: String },
          code: { $type: String },
          description: { $type: String },
          timeToCompleteByMinutes: { $type: Number },
          totalCorrectAnswer: { $type: Number },
          status: { $type: String },
          order: { $type: Number },
          image: {
            url: { $type: String },
            description: { $type: String }
          },
          video: {
            url: { $type: String },
            description: { $type: String }
          },
          quizzes: [
            {
              _id: { $type: String },
              title: { $type: String },
              description: { $type: String },
              taskerAnswer: { $type: String },
              order: { $type: Number },
              image: {
                url: { $type: String },
                description: { $type: String }
              },
              answers: [
                {
                  content: { $type: String },
                  isCorrect: { $type: Boolean }
                }
              ]
            }
          ]
        }
      ],
      createdAt: { $type: Date },
      updatedAt: { $type: Date }
    },
    changeHistories: [
      {
        from: { $type: String },
        key: { $type: String },
        content: {
          quizCollections: { $type: Array },
          quizzesCorrect: { $type: [String] },
          statusSubmission: { $type: String }
        },
        createdAt: { $type: Date },
        createdBy: { $type: String }
      }
    ],
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    numOfTimeOpened: { $type: Number }
  },
  { typeKey: "$type", collection: taskerTrainingSubmissionName4 }
);
var TaskerTrainingSubmissionModel4 = mongoClientApp265.model(
  taskerTrainingSubmissionName4,
  TaskerTrainingSubmissionSchema4
);
var taskerTrainingSubmission4 = TaskerTrainingSubmissionModel4;

// schemas/isoCode/vn/thingsToKnow.server.ts
import { Schema as Schema266 } from "mongoose";
import { mongoClientApp as mongoClientApp266 } from "mongo-connection";
var thingsToKnowName4 = "vn_thingsToKnow";
var ThingToKnowSchema4 = new Schema266(
  {
    _id: { $type: String, required: true },
    image: { $type: String },
    title: { $type: String },
    status: { $type: String },
    type: { $type: String },
    content: { $type: String },
    createdAt: { $type: Date },
    updatedAt: { $type: Date },
    menu: [
      {
        _id: { $type: String, required: true },
        title: { $type: String },
        content: { $type: String }
      }
    ],
    menuGroup: [
      {
        _id: { $type: String, required: true },
        title: String,
        menu: [
          {
            _id: { $type: String, required: true },
            title: { $type: String },
            content: { $type: String }
          }
        ]
      }
    ],
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: thingsToKnowName4 }
);
var ThingsToKnowModel4 = mongoClientApp266.model(
  "vn_thingsToKnow",
  ThingToKnowSchema4
);
var thingsToKnow4 = ThingsToKnowModel4;

// schemas/isoCode/vn/toolKitItems.server.ts
import { Schema as Schema267 } from "mongoose";
import { mongoClientApp as mongoClientApp267 } from "mongo-connection";
var toolKitItemsName4 = "vn_toolKitItems";
var ToolKitItemsSchema4 = new Schema267(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    price: { $type: Number, required: true },
    text: { $type: Map, of: String },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitItemsName4 }
);
var ToolKitItemsModel4 = mongoClientApp267.model(
  "vn_toolKitItems",
  ToolKitItemsSchema4
);
var toolKitItems4 = ToolKitItemsModel4;

// schemas/isoCode/vn/toolKitSetting.server.ts
import { Schema as Schema268 } from "mongoose";
import { mongoClientApp as mongoClientApp268 } from "mongo-connection";
var toolKitSettingName4 = "vn_toolKitSetting";
var ToolKitSettingSchema4 = new Schema268(
  {
    _id: { $type: String, required: true },
    image: { $type: String, required: true },
    discountForOncePay: { $type: Number },
    text: { $type: Map, of: String },
    BNPLSetting: {
      firstPayPercent: { $type: Number },
      percentBNPLOnTask: { $type: Number },
      period: { $type: Number }
    },
    toolKitItems: {
      $type: [{ _id: { $type: String }, quantity: { $type: Number } }]
    },
    serviceIds: { $type: [String], required: true },
    createdAt: { $type: Date, default: Date.now },
    updatedAt: { $type: Date }
  },
  { typeKey: "$type", collection: toolKitSettingName4 }
);
var ToolKitSettingModel4 = mongoClientApp268.model(
  "vn_toolKitSetting",
  ToolKitSettingSchema4
);
var toolKitSetting4 = ToolKitSettingModel4;

// schemas/isoCode/vn/trainingJourney.server.ts
import { Schema as Schema269 } from "mongoose";
import { mongoClientApp as mongoClientApp269 } from "mongo-connection";
var trainingJourneyName4 = "vn_trainingJourney";
var TrainingJourneySchema4 = new Schema269(
  {
    _id: {
      $type: String,
      required: true
    },
    quizzes: {
      $type: [String]
    },
    videos: {
      $type: [String]
    },
    status: {
      $type: String,
      required: true
    },
    name: {
      $type: String
    },
    title: {
      $type: Map,
      of: String
    },
    target: {
      $type: Number
    },
    readingTime: {
      $type: Number
    },
    createdAt: {
      $type: Date,
      required: true
    },
    level: {
      $type: String
    },
    isTesting: {
      $type: Boolean
    }
  },
  { typeKey: "$type", collection: trainingJourneyName4 }
);
var TrainingJourneyModel4 = mongoClientApp269.model(
  "vn_trainingJourney",
  TrainingJourneySchema4
);
var trainingJourney4 = TrainingJourneyModel4;

// schemas/isoCode/vn/trainingTasker.server.ts
import { Schema as Schema270 } from "mongoose";
import { mongoClientApp as mongoClientApp270 } from "mongo-connection";
var trainingTaskerName4 = "trainingTasker";
var TrainingTaskerSchema4 = new Schema270(
  {
    _id: { $type: String, required: true },
    name: { $type: String },
    status: { $type: String },
    type: { $type: String },
    serviceName: {
      $type: [String]
    },
    title: { $type: String },
    maxNumberOfExecution: { $type: Number },
    readingTime: { $type: Number },
    target: { $type: Number },
    quizzes: { $type: [String] },
    createdAt: { $type: Date },
    point: { $type: Number },
    videos: { $type: [String] },
    updatedAt: { $type: Date },
    isTesting: { $type: Boolean }
  },
  { typeKey: "$type", collection: trainingTaskerName4 }
);
var TrainingTaskerModel4 = mongoClientApp270.model(
  "vn_trainingTasker",
  TrainingTaskerSchema4
);
var trainingTasker4 = TrainingTaskerModel4;

// schemas/isoCode/vn/userActivation.server.ts
import { mongoClientApp as mongoClientApp271 } from "mongo-connection";
import { Schema as Schema271 } from "mongoose";
var userActivationName4 = "vn_userActivation";
var UserActivationSchema4 = new Schema271(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    phone: { $type: String },
    code: { $type: String },
    createdAt: { $type: Date, default: Date.now },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userActivationName4 }
);
var UserActivationModel4 = mongoClientApp271.model(
  userActivationName4,
  UserActivationSchema4
);
var userActivation4 = UserActivationModel4;

// schemas/isoCode/vn/userApp.server.ts
import { mongoClientApp as mongoClientApp272 } from "mongo-connection";
import { Schema as Schema272 } from "mongoose";
var usersName4 = "users";
var UsersAppSchema4 = new Schema272(
  {
    _id: { $type: String, required: true },
    createdAt: { $type: Date, required: true },
    username: { $type: String },
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    status: { $type: String, required: true },
    type: { $type: String },
    address: { $type: String },
    language: { $type: String, required: true },
    countryCode: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String, required: true },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    updatedAt: { $type: Date },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    isoCode: { $type: String },
    bPay: { $type: Number },
    bPoint: { $type: Map, of: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      $type: Map,
      of: {
        point: { $type: Number },
        rankName: { $type: String },
        text: { $type: Map, of: String }
      }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    appVersion: { $type: String },
    devices: {
      $type: [
        {
          platform: { $type: String },
          version: { $type: String },
          model: { $type: String },
          manufacturer: { $type: String }
        }
      ]
    },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    }
  },
  { typeKey: "$type", collection: usersName4 }
);
var UsersAppModel4 = mongoClientApp272.model("vn_users", UsersAppSchema4);
var users4 = UsersAppModel4;

// schemas/isoCode/vn/userComboVoucher.server.ts
import { mongoClientApp as mongoClientApp273 } from "mongo-connection";
import { Schema as Schema273 } from "mongoose";
var userComboVoucherName4 = "vn_userComboVoucher";
var UserComboVoucherSchema4 = new Schema273(
  {
    _id: { $type: String, required: true },
    userId: { $type: String },
    comboVoucherId: { $type: String },
    isSubscription: { $type: Boolean },
    status: { $type: String, enum: ["ACTIVE", "INACTIVE"] },
    expiredDate: { $type: Date },
    createdAt: { $type: Date, default: Date.now },
    extendedHistories: { $type: [Date] },
    isCancelled: { $type: Boolean },
    cancellationReason: { $type: Schema273.Types.Mixed },
    additionalCancellationReason: { $type: String },
    paymentMethod: { $type: String },
    cancelledAt: { $type: Date },
    vouchers: [
      {
        _id: { $type: String },
        voucherId: { $type: String },
        image: { $type: String },
        title: { $type: Schema273.Types.Mixed },
        promotionCodes: { $type: [String] },
        quantity: { $type: Number },
        serviceIds: { $type: [String] }
      }
    ]
  },
  { typeKey: "$type", collection: userComboVoucherName4 }
);
var UserComboVoucherModel4 = mongoClientApp273.model(
  userComboVoucherName4,
  UserComboVoucherSchema4
);
var userComboVoucher4 = UserComboVoucherModel4;

// schemas/isoCode/vn/userLocationHistory.server.ts
import { Schema as Schema274 } from "mongoose";
import { mongoClientApp as mongoClientApp274 } from "mongo-connection";
var userLocationHistoryName4 = "vn_userLocationHistory";
var UserLocationHistorySchema4 = new Schema274(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    phone: { $type: String },
    name: { $type: String },
    type: { $type: String },
    userStatus: { $type: String },
    history: [
      {
        time: { $type: Date },
        lat: { $type: Number },
        lng: { $type: Number }
      }
    ],
    createdAt: { $type: Date }
  },
  { typeKey: "$type", collection: userLocationHistoryName4 }
);
var UserLocationHistoryModel4 = mongoClientApp274.model(
  userLocationHistoryName4,
  UserLocationHistorySchema4
);
var userLocationHistory4 = UserLocationHistoryModel4;

// schemas/isoCode/vn/userProfile.server.ts
import { mongoClientApp as mongoClientApp275 } from "mongo-connection";
import { Schema as Schema275 } from "mongoose";
var userProfileName4 = "vn_userProfile";
var UserProfileSchema4 = new Schema275(
  {
    _id: { $type: String, required: true },
    userId: { $type: String, required: true },
    createdAt: { $type: Date },
    profileCreatedAt: { $type: Date },
    // Ngay tao user profile
    name: { $type: String },
    phone: { $type: String },
    avatar: { $type: String },
    address: { $type: String },
    language: { $type: String },
    favouriteTasker: {
      $type: [String]
    },
    workingPlaces: {
      $type: [
        {
          country: String,
          city: String,
          district: String
        }
      ]
    },
    idNumber: { $type: String },
    referralCode: { $type: String },
    fAccountId: { $type: String },
    avgRating: { $type: Number },
    taskDone: { $type: Number },
    changeHistory: { $type: Array },
    bankAccount: {
      name: { $type: String },
      icon: { $type: String },
      note: { $type: Map, of: String },
      text: {
        $type: Map,
        of: String
      },
      accountNumber: { $type: Number },
      accountHolder: { $type: Number }
    },
    gender: { $type: String },
    isPremiumTasker: { $type: Boolean },
    lastDoneTask: { $type: Date },
    cities: {
      $type: [
        {
          country: { $type: String },
          city: { $type: String }
        }
      ]
    },
    bPay: { $type: Number },
    bPoint: { $type: Number },
    point: { $type: Number },
    rankInfoByCountry: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Map, of: String }
    },
    rankInfo: {
      point: { $type: Number },
      rankName: { $type: String },
      text: { $type: Schema275.Types.Mixed }
    },
    emails: {
      $type: [
        {
          address: { $type: String },
          verified: { $type: Boolean }
        }
      ]
    },
    lastPostedTask: { $type: Date },
    lastOnline: { $type: Date },
    csNotes: {
      $type: [
        {
          _id: String,
          note: String,
          noteBy: String,
          noteAt: Date
        }
      ]
    },
    blackList: {
      $type: [String]
    },
    taskNoteByServiceV3: {
      $type: [
        {
          _id: String,
          note: String,
          serviceId: String,
          noteAt: String,
          noteBy: String
        }
      ]
    },
    bankAccountNumber: { $type: String },
    favouriteServiceByCountry: { $type: [String] },
    countryCode: { $type: String }
  },
  { typeKey: "$type", collection: userProfileName4 }
);
var UserProfileModel4 = mongoClientApp275.model(userProfileName4, UserProfileSchema4);
var userProfile4 = UserProfileModel4;

// schemas/isoCode/vn/workingPlaces.server.ts
import { Schema as Schema276 } from "mongoose";
import { mongoClientApp as mongoClientApp276 } from "mongo-connection";
var workingPlacesName4 = "vn_workingPlaces";
var WorkingPlacesSchema4 = new Schema276(
  {
    _id: { $type: String, required: true },
    countryCode: { $type: String },
    countryName: { $type: String },
    cities: [
      {
        name: { $type: String, required: true },
        code: { $type: String },
        districts: [String]
      }
    ]
  },
  { typeKey: "$type", collection: workingPlacesName4 }
);
var WorkingPlacesModel4 = mongoClientApp276.model(
  "vn_workingPlaces",
  WorkingPlacesSchema4
);
var workingPlaces4 = WorkingPlacesModel4;

// schemas/isoCode/vn/index.server.ts
var VN = {
  task: task4,
  historyTasks: historyTasks4,
  trainingTasker: trainingTasker4,
  userLocationHistory: userLocationHistory4,
  trainingJourney: trainingJourney4,
  toolKitSetting: toolKitSetting4,
  toolKitItems: toolKitItems4,
  thingsToKnow: thingsToKnow4,
  taskerTrainingSubmission: taskerTrainingSubmission4,
  taskerTrainingQuizCollection: taskerTrainingQuizCollection4,
  taskerTrainingQuiz: taskerTrainingQuiz4,
  taskerTrainingCourse: taskerTrainingCourse4,
  taskerToolkitLadingDetails: taskerToolkitLadingDetails4,
  financialAccount: financialAccount4,
  taskerIncentive: taskerIncentive4,
  taskerPointTransaction: taskerPointTransaction4,
  taskerGift: taskerGift4,
  notification: notification4,
  taskerProfile: taskerProfile4,
  employeeProfile: employeeProfile4,
  workingPlaces: workingPlaces4,
  flashSale: flashSale4,
  incentive: incentive4,
  promotionCode: promotionCode4,
  promotionSource: promotionSource4,
  service: service4,
  promotionHistory: promotionHistory4,
  settingCountry: settingCountry4,
  comboVoucher: comboVoucher4,
  referralCampaign: referralCampaign4,
  marketingCampaign: marketingCampaign4,
  settingSystem: settingSystem4,
  partnerDirectory: partnerDirectory4,
  partnerRequest: partnerRequest4,
  communityComment: communityComment4,
  communityMedal: communityMedal4,
  communityNotification: communityNotification4,
  communityPost: communityPost4,
  communitySetting: communitySetting4,
  communityTag: communityTag4,
  communityUser: communityUser4,
  communityUserReport: communityUserReport4,
  subscription: subscription4,
  taskerOnboardingSetting: taskerOnboardingSetting4,
  users: users4,
  serviceChannel: serviceChannel4,
  bEmployee: bEmployee4,
  bEmployeeSetting: bEmployeeSetting4,
  FATransaction: FATransaction4,
  taskerBNPLTransaction: taskerBNPLTransaction4,
  taskerBNPLProcess: taskerBNPLProcess4,
  paymentToolKitTransaction: paymentToolKitTransaction4,
  rating: rating4,
  userActivation: userActivation4,
  business: business4,
  businessLevel: businessLevel4,
  businessMember: businessMember4,
  businessMemberTransaction: businessMemberTransaction4,
  businessTransaction: businessTransaction4,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocation4,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDate4,
  taskerSpecialCampaign: taskerSpecialCampaign4,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransaction4,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethod4,
  userProfile: userProfile4,
  communityTagOrder: communityTagOrder4,
  journeySetting: journeySetting4,
  userComboVoucher: userComboVoucher4,
  bundleVoucher: bundleVoucher4
};
var VNName = {
  task: taskName4,
  historyTasks: historyTasksName4,
  userLocationHistory: userLocationHistoryName4,
  trainingTasker: trainingTaskerName4,
  trainingJourney: trainingJourneyName4,
  toolKitSetting: toolKitSettingName4,
  toolKitItems: toolKitItemsName4,
  thingsToKnow: thingsToKnowName4,
  taskerTrainingSubmission: taskerTrainingSubmissionName4,
  taskerTrainingQuizCollection: taskerTrainingQuizCollectionName4,
  taskerTrainingQuiz: taskerTrainingQuizName4,
  taskerTrainingCourse: taskerTrainingCourseName4,
  taskerToolkitLadingDetails: taskerToolkitLadingDetailsName4,
  taskerPointTransaction: taskerPointTransactionName4,
  taskerIncentive: taskerIncentiveName4,
  taskerGift: taskerGiftName4,
  notification: notificationName4,
  financialAccount: financialAccountName4,
  taskerProfile: taskerProfileName4,
  employeeProfile: employeeProfileName4,
  workingPlaces: workingPlacesName4,
  service: serviceName4,
  settingCountry: settingCountryName4,
  settingSystem: settingSystemName4,
  incentive: incentiveName4,
  communityComment: communityCommentName4,
  communityMedal: communityMedalName4,
  communityNotification: communityNotificationName4,
  communityPost: communityPostName4,
  communitySetting: communitySettingName4,
  communityTag: communityTagName4,
  communityUser: communityUserName4,
  communityUserReport: communityUserReportName4,
  subscription: subscriptionName4,
  taskerOnboardingSetting: taskerOnboardingSettingName4,
  users: usersName4,
  serviceChannel: serviceChannelName4,
  bEmployee: bEmployeeName4,
  bEmployeeSetting: bEmployeeSettingName4,
  FATransaction: FATransactionName4,
  taskerBNPLTransaction: taskerBNPLTransactionName4,
  taskerBNPLProcess: taskerBNPLProcessName4,
  paymentToolKitTransaction: paymentToolKitTransactionName4,
  promotionCode: promotionCodeName4,
  promotionHistory: promotionHistoryName4,
  marketingCampaign: marketingCampaignName4,
  rating: ratingName4,
  userActivation: userActivationName4,
  business: businessName4,
  businessLevel: businessLevelName4,
  businessMember: businessMemberName4,
  businessMemberTransaction: businessMemberTransactionName4,
  businessTransaction: businessTransactionName4,
  businessSetupAllocationAndReallocation: businessSetupAllocationAndReallocationName4,
  comboVoucher: comboVoucherName4,
  flashSale: flashSaleName4,
  partnerDirectory: partnerDirectoryName4,
  partnerRequest: partnerRequestName4,
  promotionSource: promotionSourceName4,
  referralCampaign: referralCampaignName4,
  taskerTrainingCourseStartDate: taskerTrainingCourseStartDateName4,
  taskerSpecialCampaign: taskerSpecialCampaignName4,
  taskerSpecialCampaignTransaction: taskerSpecialCampaignTransactionName4,
  marketingCampaignPaymentMethod: marketingCampaignPaymentMethodName4,
  userProfile: userProfileName4,
  communityTagOrder: communityTagOrderName4,
  journeySetting: journeySettingName4,
  userComboVoucher: userComboVoucherName4,
  bundleVoucher: bundleVoucherName4
};
var index_server_default4 = VN;

// schemas/isoCode/method.server.ts
var EnumIsoCode = /* @__PURE__ */ ((EnumIsoCode2) => {
  EnumIsoCode2["VN"] = "VN";
  EnumIsoCode2["TH"] = "TH";
  EnumIsoCode2["ID"] = "ID";
  EnumIsoCode2["MY"] = "MY";
  return EnumIsoCode2;
})(EnumIsoCode || {});
function getModels(isoCode) {
  if (isoCode === "VN") {
    return index_server_default4;
  }
  if (isoCode === "TH") {
    return index_server_default3;
  }
  if (isoCode === "ID") {
    return index_server_default2;
  }
  if (isoCode === "MY") {
    return index_server_default;
  }
  throw new Error("Iso code not is VN or TH or ID or MY");
}
function getFieldNameByIsoCode({
  isoCode,
  fieldName
}) {
  if (isoCode === "VN" /* VN */ || isoCode === "MY" /* MY */) return fieldName;
  return `${isoCode}_${fieldName}`;
}
function getCollectionNameByIsoCode(isoCode) {
  if (isoCode === "VN") {
    return VNName;
  }
  if (isoCode === "TH") {
    return THName;
  }
  if (isoCode === "ID") {
    return IDName;
  }
  if (isoCode === "MY") {
    return MYName;
  }
  throw new Error("Iso code not is VN or TH or ID or MY");
}
async function getFinancialAccountByIsoCode({
  fAccountId,
  isoCode
}) {
  if (!isoCode) {
    throw new Error("IsoCode is not valid");
  }
  const fAccount = await getModels(isoCode).financialAccount.findById(fAccountId).lean();
  if (!fAccount) {
    throw new Error("Financial account not found");
  }
  const fMainAccountFieldName = getFieldNameByIsoCode({
    isoCode,
    fieldName: "FMainAccount"
  });
  const promotionFieldName = getFieldNameByIsoCode({
    isoCode,
    fieldName: "Promotion"
  });
  return {
    [fMainAccountFieldName]: fAccount?.[fMainAccountFieldName] || 0,
    [promotionFieldName]: fAccount?.[promotionFieldName] || 0
  };
}
export {
  EnumIsoCode,
  getCollectionNameByIsoCode,
  getFieldNameByIsoCode,
  getFinancialAccountByIsoCode,
  getModels
};
//# sourceMappingURL=index.mjs.map
